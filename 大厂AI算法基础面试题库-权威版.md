# 大厂AI算法基础面试题库 - 权威版

> **权威来源**: 基于Google、Meta、Amazon、Microsoft、Apple、OpenAI、Nvidia等顶级科技公司真实面试题汇总
> 
> **更新时间**: 2025年1月
> 
> **适用对象**: AI/ML工程师、算法工程师、数据科学家

---

## 目录

- [第一部分：数学基础面试题](#第一部分数学基础面试题)
- [第二部分：机器学习算法面试题](#第二部分机器学习算法面试题)
- [第三部分：深度学习面试题](#第三部分深度学习面试题)
- [第四部分：系统设计面试题](#第四部分系统设计面试题)
- [第五部分：编程实现面试题](#第五部分编程实现面试题)
- [第六部分：行为面试题](#第六部分行为面试题)

---

# 第一部分：数学基础面试题

## 1.1 线性代数类题目

### 题目1：矩阵分解的应用
**公司来源**: Google, Meta
**难度**: ⭐⭐⭐

**题目**: 解释SVD（奇异值分解）的数学原理，并说明它在推荐系统中的具体应用。如果你有一个用户-物品评分矩阵，如何使用SVD进行协同过滤？

**多种解答方案**:

**方案A：理论导向**
- SVD数学定义：A = UΣV^T
- U：左奇异向量矩阵（用户特征）
- Σ：奇异值对角矩阵（重要性权重）
- V^T：右奇异向量矩阵（物品特征）
- 降维原理：保留前k个最大奇异值

**方案B：实践导向**
- 数据预处理：处理缺失值、标准化
- 矩阵分解：scipy.linalg.svd()实现
- 维度选择：解释方差比例选择k
- 预测计算：重构矩阵预测评分
- 性能优化：稀疏矩阵处理

**方案C：工程导向**
- 大规模实现：分布式SVD算法
- 在线更新：增量SVD方法
- 冷启动问题：新用户/物品处理
- 评估指标：RMSE、MAE、Precision@K

**推荐方案**: 方案B + 方案C的结合
- 先展示理论理解，再重点讲实践应用
- 结合具体代码示例和工程考虑
- 体现对大规模系统的理解

**实际案例**:
```python
# Netflix推荐系统中的SVD应用
from scipy.sparse.linalg import svds
import numpy as np

def svd_recommendation(rating_matrix, k=50):
    # 处理稀疏矩阵
    U, sigma, Vt = svds(rating_matrix, k=k)
    sigma = np.diag(sigma)
    
    # 重构评分矩阵
    predicted_ratings = np.dot(np.dot(U, sigma), Vt)
    return predicted_ratings

# 处理冷启动问题
def handle_cold_start(new_user_ratings, V, sigma):
    # 将新用户投影到潜在空间
    user_factors = np.dot(new_user_ratings, V.T) / sigma
    return user_factors
```

### 题目2：特征值与特征向量的实际意义
**公司来源**: Microsoft, Apple
**难度**: ⭐⭐

**题目**: 在PCA中，为什么我们选择协方差矩阵的特征向量作为主成分？特征值代表什么含义？

**多种解答方案**:

**方案A：几何解释**
- 特征向量：数据变化最大的方向
- 特征值：该方向上的方差大小
- 几何意义：数据椭圆的主轴方向

**方案B：优化角度**
- 目标函数：最大化投影方差
- 拉格朗日乘数法推导
- 约束条件：单位向量约束

**方案C：信息论角度**
- 信息保留：最大化保留的信息量
- 维度诅咒：高维数据的稀疏性
- 压缩效果：数据压缩与重构

**推荐方案**: 方案A + 实际代码演示
- 用可视化展示几何直觉
- 结合numpy实现展示计算过程

## 1.2 概率统计类题目

### 题目3：贝叶斯定理在机器学习中的应用
**公司来源**: Google, OpenAI
**难度**: ⭐⭐⭐

**题目**: 解释朴素贝叶斯分类器中的"朴素"假设，这个假设在实际应用中是否合理？如何改进？

**多种解答方案**:

**方案A：理论分析**
- 条件独立假设：P(x1,x2,...,xn|y) = ∏P(xi|y)
- 假设的不合理性：特征间往往存在相关性
- 数学推导：从贝叶斯定理到朴素贝叶斯

**方案B：实践改进**
- 特征选择：去除高度相关的特征
- 特征工程：构造独立性更强的特征
- 模型改进：半朴素贝叶斯、贝叶斯网络

**方案C：应用场景**
- 文本分类：词袋模型的合理性
- 垃圾邮件检测：特征独立性分析
- 情感分析：上下文依赖问题

**推荐方案**: 方案A + 方案C
- 先解释理论，再用具体应用场景说明
- 承认假设的局限性，但强调实用性

**实际案例**:
```python
# 文本分类中的朴素贝叶斯应用
from sklearn.naive_bayes import MultinomialNB
from sklearn.feature_extraction.text import TfidfVectorizer

# 处理特征相关性的改进方法
def improved_naive_bayes(texts, labels):
    # 1. 特征选择减少相关性
    vectorizer = TfidfVectorizer(max_features=5000, ngram_range=(1,2))
    X = vectorizer.fit_transform(texts)
    
    # 2. 使用平滑参数处理零概率
    nb = MultinomialNB(alpha=0.1)
    nb.fit(X, labels)
    
    return nb, vectorizer
```

### 题目4：中心极限定理的实际应用
**公司来源**: Amazon, Nvidia
**难度**: ⭐⭐

**题目**: 在深度学习中，为什么我们经常假设权重初始化服从正态分布？这与中心极限定理有什么关系？

**多种解答方案**:

**方案A：理论基础**
- 中心极限定理：大量独立随机变量的和趋向正态分布
- 神经网络中的应用：多个输入的加权和
- 激活函数的输入分布

**方案B：实践考虑**
- 梯度消失/爆炸问题
- Xavier/He初始化的数学原理
- 不同激活函数的初始化策略

**方案C：实验验证**
- 不同初始化方法的比较
- 训练收敛速度的影响
- 模型性能的差异

**推荐方案**: 方案B（最实用）
- 重点解释初始化对训练的影响
- 结合具体的初始化公式

## 1.3 优化理论类题目

### 题目5：梯度下降的收敛性分析
**公司来源**: Meta, Google
**难度**: ⭐⭐⭐⭐

**题目**: 解释为什么SGD（随机梯度下降）在非凸优化问题中仍然能够找到较好的解？与批量梯度下降相比有什么优势？

**多种解答方案**:

**方案A：理论分析**
- 非凸优化的挑战：局部最优vs全局最优
- SGD的随机性：噪声帮助逃离局部最优
- 收敛性理论：Robbins-Monro条件

**方案B：实践优势**
- 计算效率：单样本vs全批次
- 内存需求：大数据集的处理
- 在线学习：流数据处理能力

**方案C：改进方法**
- 动量方法：加速收敛
- 自适应学习率：Adam、RMSprop
- 学习率调度：余弦退火、warmup

**推荐方案**: 方案A + 方案C
- 理论解释SGD的优势
- 重点介绍现代优化器的改进

**实际案例**:
```python
# 不同优化器的比较实现
import torch.optim as optim

def compare_optimizers(model, train_loader):
    optimizers = {
        'SGD': optim.SGD(model.parameters(), lr=0.01, momentum=0.9),
        'Adam': optim.Adam(model.parameters(), lr=0.001),
        'RMSprop': optim.RMSprop(model.parameters(), lr=0.001)
    }
    
    results = {}
    for name, optimizer in optimizers.items():
        # 训练并记录收敛曲线
        losses = train_with_optimizer(model, optimizer, train_loader)
        results[name] = losses
    
    return results
```

---

# 第二部分：机器学习算法面试题

## 2.1 监督学习类题目

### 题目6：偏差-方差权衡
**公司来源**: Google, Microsoft
**难度**: ⭐⭐⭐

**题目**: 解释偏差-方差权衡，并说明如何在实际项目中识别和解决高偏差或高方差问题？

**多种解答方案**:

**方案A：理论解释**
- 数学定义：Error = Bias² + Variance + Noise
- 偏差：模型假设与真实函数的差异
- 方差：模型对训练数据变化的敏感性

**方案B：实践识别**
- 学习曲线分析：训练误差vs验证误差
- 交叉验证：模型稳定性评估
- 诊断方法：欠拟合vs过拟合

**方案C：解决方案**
- 高偏差：增加模型复杂度、特征工程
- 高方差：正则化、更多数据、集成方法
- 平衡策略：模型选择、超参数调优

**推荐方案**: 方案B + 方案C
- 重点展示实际诊断技能
- 提供具体的解决策略

**实际案例**:
```python
# 偏差-方差分析实现
def bias_variance_analysis(model_class, X, y, n_trials=100):
    predictions = []
    
    for trial in range(n_trials):
        # 重新采样训练数据
        X_sample, y_sample = resample(X, y)
        
        # 训练模型
        model = model_class()
        model.fit(X_sample, y_sample)
        
        # 预测
        pred = model.predict(X_test)
        predictions.append(pred)
    
    predictions = np.array(predictions)
    
    # 计算偏差和方差
    mean_pred = np.mean(predictions, axis=0)
    bias_squared = np.mean((mean_pred - y_true) ** 2)
    variance = np.mean(np.var(predictions, axis=0))
    
    return bias_squared, variance
```

### 题目7：特征选择方法比较
**公司来源**: Amazon, Apple
**难度**: ⭐⭐⭐

**题目**: 比较Filter、Wrapper、Embedded三种特征选择方法的优缺点，在什么情况下使用哪种方法？

**多种解答方案**:

**方案A：方法对比**
- Filter：统计测试、相关性分析
- Wrapper：前向/后向选择、递归特征消除
- Embedded：L1正则化、树模型特征重要性

**方案B：性能分析**
- 计算复杂度：Filter < Embedded < Wrapper
- 选择质量：Wrapper > Embedded > Filter
- 泛化能力：Embedded ≈ Wrapper > Filter

**方案C：应用场景**
- 高维数据：Filter方法预筛选
- 小数据集：Wrapper方法精选
- 大规模数据：Embedded方法平衡

**推荐方案**: 方案C（最实用）
- 根据数据规模和计算资源选择
- 结合多种方法的混合策略

## 2.2 无监督学习类题目

### 题目8：聚类算法的选择
**公司来源**: Meta, Nvidia
**难度**: ⭐⭐⭐

**题目**: 给定一个客户行为数据集，如何选择合适的聚类算法？K-means、DBSCAN、层次聚类各适用于什么场景？

**多种解答方案**:

**方案A：算法特点**
- K-means：球形簇、需预设k值
- DBSCAN：任意形状、自动确定簇数
- 层次聚类：树状结构、不需预设参数

**方案B：数据特征匹配**
- 数据分布：球形vs任意形状
- 噪声处理：DBSCAN优势
- 簇数确定：肘部法则、轮廓系数

**方案C：业务需求**
- 客户分群：可解释性要求
- 异常检测：DBSCAN识别离群点
- 市场细分：层次聚类的业务层级

**推荐方案**: 方案B + 方案C
- 技术特点与业务需求结合
- 提供具体的选择决策树

**实际案例**:
```python
# 聚类算法选择决策系统
def choose_clustering_algorithm(data_characteristics):
    """
    根据数据特征选择聚类算法
    """
    if data_characteristics['has_noise'] and data_characteristics['unknown_clusters']:
        return "DBSCAN"
    elif data_characteristics['spherical_clusters'] and data_characteristics['known_k']:
        return "K-means"
    elif data_characteristics['hierarchical_structure']:
        return "Hierarchical Clustering"
    else:
        return "Try multiple algorithms and compare"

# 客户分群实际应用
def customer_segmentation(customer_data):
    # 1. 数据预处理
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(customer_data)
    
    # 2. 算法比较
    algorithms = {
        'K-means': KMeans(n_clusters=5),
        'DBSCAN': DBSCAN(eps=0.5, min_samples=5),
        'AgglomerativeClustering': AgglomerativeClustering(n_clusters=5)
    }
    
    results = {}
    for name, algo in algorithms.items():
        labels = algo.fit_predict(X_scaled)
        silhouette = silhouette_score(X_scaled, labels)
        results[name] = {'labels': labels, 'silhouette': silhouette}
    
    return results
```

### 题目9：降维方法的选择
**公司来源**: Google, OpenAI
**难度**: ⭐⭐⭐⭐

**题目**: PCA、t-SNE、UMAP各有什么特点？在什么情况下选择哪种降维方法？如何评估降维效果？

**多种解答方案**:

**方案A：算法原理**
- PCA：线性降维、保持全局结构
- t-SNE：非线性降维、保持局部结构
- UMAP：平衡全局和局部结构

**方案B：应用场景**
- 数据可视化：t-SNE、UMAP
- 特征压缩：PCA
- 预处理：PCA去噪声

**方案C：评估方法**
- 重构误差：PCA的解释方差比
- 邻域保持：trustworthiness、continuity
- 下游任务：分类准确率

**推荐方案**: 方案A + 方案C
- 理解算法原理是关键
- 评估方法体现专业性

---

# 第三部分：深度学习面试题

## 3.1 神经网络基础

### 题目10：激活函数的选择
**公司来源**: Meta, Nvidia
**难度**: ⭐⭐⭐

**题目**: 为什么ReLU成为了最常用的激活函数？它有什么缺点？如何改进？

**多种解答方案**:

**方案A：优势分析**
- 计算简单：max(0, x)
- 梯度特性：解决梯度消失问题
- 稀疏性：生物学合理性

**方案B：缺点分析**
- Dead ReLU问题：神经元永久失活
- 非零中心：影响收敛速度
- 无界输出：可能导致梯度爆炸

**方案C：改进方案**
- Leaky ReLU：解决Dead ReLU
- ELU：平滑性更好
- Swish/GELU：自适应门控

**推荐方案**: 方案A + 方案C
- 先说明为什么流行，再介绍改进
- 结合具体应用场景

**实际案例**:
```python
# 激活函数比较实验
import torch.nn as nn

def compare_activations(model_class, train_loader, test_loader):
    activations = {
        'ReLU': nn.ReLU(),
        'LeakyReLU': nn.LeakyReLU(0.01),
        'ELU': nn.ELU(),
        'GELU': nn.GELU()
    }
    
    results = {}
    for name, activation in activations.items():
        model = model_class(activation=activation)
        
        # 训练模型
        train_loss, val_acc = train_model(model, train_loader, test_loader)
        
        # 分析Dead ReLU比例
        dead_ratio = analyze_dead_neurons(model)
        
        results[name] = {
            'accuracy': val_acc,
            'dead_neurons': dead_ratio
        }
    
    return results
```

### 题目11：批量归一化的作用机制
**公司来源**: Google, Microsoft
**难度**: ⭐⭐⭐⭐

**题目**: 批量归一化（Batch Normalization）为什么能够加速训练？它在训练和推理阶段有什么不同？

**多种解答方案**:

**方案A：理论解释**
- 内部协变量偏移：减少层间分布变化
- 梯度流改善：减少梯度消失/爆炸
- 正则化效果：减少过拟合

**方案B：实现细节**
- 训练阶段：使用批次统计量
- 推理阶段：使用移动平均统计量
- 参数学习：γ和β的作用

**方案C：实践考虑**
- 批次大小影响：小批次的不稳定性
- 替代方案：Layer Norm、Group Norm
- 应用场景：CNN vs RNN vs Transformer

**推荐方案**: 方案B + 方案C
- 实现细节体现深度理解
- 实践考虑显示工程经验

## 3.2 卷积神经网络

### 题目12：CNN架构设计原则
**公司来源**: Apple, Amazon
**难度**: ⭐⭐⭐

**题目**: 设计一个图像分类的CNN架构，解释你的设计选择：卷积核大小、池化策略、网络深度等。

**多种解答方案**:

**方案A：经典设计**
- 卷积核：3x3为主，偶尔1x1和5x5
- 池化：Max pooling减少空间维度
- 深度：逐层增加通道数，减少空间尺寸

**方案B：现代改进**
- 残差连接：解决深度网络训练问题
- 注意力机制：SE-Net、CBAM
- 高效架构：MobileNet、EfficientNet

**方案C：任务特定**
- 数据集大小：小数据集避免过深
- 计算资源：移动端vs服务器端
- 精度要求：速度vs准确率权衡

**推荐方案**: 方案C（最实用）
- 根据具体需求设计
- 体现工程权衡思维

**实际案例**:
```python
# 自适应CNN架构设计
class AdaptiveCNN(nn.Module):
    def __init__(self, num_classes, input_size, complexity='medium'):
        super().__init__()
        
        if complexity == 'light':
            # 移动端优化
            self.features = self._make_light_features()
        elif complexity == 'medium':
            # 平衡性能和效率
            self.features = self._make_medium_features()
        else:
            # 高精度要求
            self.features = self._make_heavy_features()
        
        self.classifier = nn.Linear(self._get_feature_size(), num_classes)
    
    def _make_light_features(self):
        # MobileNet-like架构
        return nn.Sequential(
            # Depthwise separable convolutions
            self._depthwise_conv(3, 32),
            self._depthwise_conv(32, 64),
            # ... 更多层
        )
    
    def _depthwise_conv(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, groups=in_channels, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(),
            nn.Conv2d(in_channels, out_channels, 1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU()
        )
```

## 3.3 循环神经网络

### 题目13：LSTM vs GRU vs Transformer
**公司来源**: OpenAI, Meta
**难度**: ⭐⭐⭐⭐

**题目**: 比较LSTM、GRU和Transformer在序列建模任务中的优缺点，什么时候选择哪种架构？

**多种解答方案**:

**方案A：架构对比**
- LSTM：三个门控制信息流
- GRU：简化的两门结构
- Transformer：自注意力机制

**方案B：性能分析**
- 长序列建模：Transformer > LSTM > GRU
- 计算效率：GRU > LSTM > Transformer
- 并行化：Transformer >> LSTM ≈ GRU

**方案C：应用场景**
- 短序列：GRU足够
- 长序列：Transformer
- 资源受限：LSTM/GRU
- 大规模数据：Transformer

**推荐方案**: 方案B + 方案C
- 性能对比要有数据支撑
- 应用场景要具体

---

# 第四部分：系统设计面试题

## 4.1 推荐系统设计

### 题目14：设计YouTube推荐系统
**公司来源**: Google
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计YouTube的视频推荐系统，考虑冷启动、实时性、多样性等问题。

**多种解答方案**:

**方案A：系统架构**
- 召回层：多路召回策略
- 排序层：深度学习排序模型
- 重排层：多样性和业务规则

**方案B：算法设计**
- 协同过滤：用户-视频交互
- 内容推荐：视频特征匹配
- 深度学习：Wide&Deep、DIN等

**方案C：工程实现**
- 数据流：实时特征更新
- 模型服务：在线推理优化
- A/B测试：效果评估

**推荐方案**: 方案A + 方案C
- 系统架构展示全局思维
- 工程实现体现实践能力

**实际案例**:
```python
# YouTube推荐系统架构示例
class YouTubeRecommendationSystem:
    def __init__(self):
        self.recall_models = {
            'collaborative': CollaborativeFiltering(),
            'content': ContentBasedFiltering(),
            'popular': PopularityBasedRecall(),
            'deep': DeepRecallModel()
        }
        
        self.ranking_model = WideAndDeepModel()
        self.reranking_rules = DiversityReranker()
    
    def recommend(self, user_id, num_recommendations=20):
        # 1. 多路召回
        candidates = []
        for name, model in self.recall_models.items():
            candidates.extend(model.recall(user_id, num_candidates=100))
        
        # 2. 去重和初筛
        candidates = self._dedup_and_filter(candidates)
        
        # 3. 精排
        ranked_items = self.ranking_model.rank(user_id, candidates)
        
        # 4. 重排（多样性、新鲜度等）
        final_recommendations = self.reranking_rules.rerank(
            user_id, ranked_items, num_recommendations
        )
        
        return final_recommendations
    
    def handle_cold_start(self, user_id):
        """处理冷启动问题"""
        if self._is_new_user(user_id):
            # 基于人口统计学特征推荐
            return self._demographic_based_recommend(user_id)
        else:
            # 基于少量交互的快速学习
            return self._few_shot_recommend(user_id)
```

### 题目15：设计实时广告竞价系统
**公司来源**: Meta, Amazon
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计一个实时广告竞价系统（RTB），要求延迟小于100ms，支持每秒百万级请求。

**多种解答方案**:

**方案A：系统架构**
- 请求处理：负载均衡、请求路由
- 竞价逻辑：实时出价计算
- 响应返回：广告创意和出价

**方案B：算法优化**
- 特征工程：实时特征计算
- 模型预测：CTR/CVR预估
- 出价策略：动态出价算法

**方案C：性能优化**
- 缓存策略：多级缓存
- 数据库：读写分离、分片
- 服务治理：熔断、限流

**推荐方案**: 方案C（性能是关键）
- 重点讲解如何达到延迟要求
- 展示大规模系统设计能力

## 4.2 搜索系统设计

### 题目16：设计Google搜索引擎
**公司来源**: Google, Microsoft
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计一个像Google一样的搜索引擎，包括网页爬取、索引构建、查询处理和排序。

**多种解答方案**:

**方案A：整体架构**
- 爬虫系统：分布式网页爬取
- 索引系统：倒排索引构建
- 查询系统：实时查询处理
- 排序系统：相关性排序

**方案B：核心算法**
- PageRank：网页权威性计算
- TF-IDF：文本相关性计算
- 机器学习：Learning to Rank

**方案C：扩展性设计**
- 分布式存储：索引分片
- 负载均衡：查询分发
- 缓存优化：热点查询缓存

**推荐方案**: 方案A + 方案B
- 架构设计展示系统思维
- 算法细节体现技术深度

---

# 第五部分：编程实现面试题

## 5.1 算法实现类

### 题目17：从零实现梯度下降
**公司来源**: 所有大厂通用
**难度**: ⭐⭐⭐

**题目**: 用Python从零实现梯度下降算法，包括批量梯度下降、随机梯度下降和小批量梯度下降。

**推荐解答**:
```python
import numpy as np
import matplotlib.pyplot as plt

class GradientDescent:
    def __init__(self, learning_rate=0.01, max_iterations=1000, tolerance=1e-6):
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.cost_history = []
    
    def compute_cost(self, X, y, theta):
        """计算代价函数"""
        m = len(y)
        predictions = X.dot(theta)
        cost = (1/(2*m)) * np.sum((predictions - y)**2)
        return cost
    
    def compute_gradient(self, X, y, theta):
        """计算梯度"""
        m = len(y)
        predictions = X.dot(theta)
        gradient = (1/m) * X.T.dot(predictions - y)
        return gradient
    
    def batch_gradient_descent(self, X, y):
        """批量梯度下降"""
        m, n = X.shape
        theta = np.zeros(n)
        
        for i in range(self.max_iterations):
            cost = self.compute_cost(X, y, theta)
            self.cost_history.append(cost)
            
            gradient = self.compute_gradient(X, y, theta)
            theta = theta - self.learning_rate * gradient
            
            # 检查收敛
            if i > 0 and abs(self.cost_history[-2] - self.cost_history[-1]) < self.tolerance:
                print(f"收敛于第{i}次迭代")
                break
        
        return theta
    
    def stochastic_gradient_descent(self, X, y):
        """随机梯度下降"""
        m, n = X.shape
        theta = np.zeros(n)
        
        for i in range(self.max_iterations):
            # 随机选择一个样本
            random_index = np.random.randint(0, m)
            xi = X[random_index:random_index+1]
            yi = y[random_index:random_index+1]
            
            gradient = self.compute_gradient(xi, yi, theta)
            theta = theta - self.learning_rate * gradient
            
            # 每100次迭代计算一次总代价
            if i % 100 == 0:
                cost = self.compute_cost(X, y, theta)
                self.cost_history.append(cost)
        
        return theta
    
    def mini_batch_gradient_descent(self, X, y, batch_size=32):
        """小批量梯度下降"""
        m, n = X.shape
        theta = np.zeros(n)
        
        for i in range(self.max_iterations):
            # 随机选择小批量
            indices = np.random.choice(m, batch_size, replace=False)
            X_batch = X[indices]
            y_batch = y[indices]
            
            gradient = self.compute_gradient(X_batch, y_batch, theta)
            theta = theta - self.learning_rate * gradient
            
            # 每10次迭代计算一次总代价
            if i % 10 == 0:
                cost = self.compute_cost(X, y, theta)
                self.cost_history.append(cost)
        
        return theta

# 使用示例和比较
def compare_gradient_descent_methods():
    # 生成示例数据
    np.random.seed(42)
    m = 1000
    X = np.random.randn(m, 2)
    X = np.c_[np.ones(m), X]  # 添加偏置项
    true_theta = np.array([2, -1, 0.5])
    y = X.dot(true_theta) + 0.1 * np.random.randn(m)
    
    # 比较三种方法
    methods = {
        'Batch GD': lambda gd: gd.batch_gradient_descent(X, y),
        'Stochastic GD': lambda gd: gd.stochastic_gradient_descent(X, y),
        'Mini-batch GD': lambda gd: gd.mini_batch_gradient_descent(X, y, 32)
    }
    
    results = {}
    for name, method in methods.items():
        gd = GradientDescent(learning_rate=0.01, max_iterations=1000)
        theta = method(gd)
        results[name] = {
            'theta': theta,
            'cost_history': gd.cost_history,
            'final_cost': gd.cost_history[-1] if gd.cost_history else None
        }
        print(f"{name}: θ = {theta}, 最终代价 = {results[name]['final_cost']:.6f}")
    
    return results
```

### 题目18：实现K-means聚类算法
**公司来源**: Amazon, Apple
**难度**: ⭐⭐⭐

**题目**: 从零实现K-means聚类算法，包括初始化策略、收敛判断和结果可视化。

**推荐解答**:
```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_blobs

class KMeans:
    def __init__(self, k=3, max_iterations=100, tolerance=1e-4, init_method='random'):
        self.k = k
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.init_method = init_method
        self.centroids = None
        self.labels = None
        self.inertia_history = []
    
    def _initialize_centroids(self, X):
        """初始化聚类中心"""
        n_samples, n_features = X.shape
        
        if self.init_method == 'random':
            # 随机初始化
            self.centroids = np.random.uniform(
                X.min(axis=0), X.max(axis=0), (self.k, n_features)
            )
        elif self.init_method == 'kmeans++':
            # K-means++初始化
            self.centroids = self._kmeans_plus_plus_init(X)
        else:
            raise ValueError("init_method must be 'random' or 'kmeans++'")
    
    def _kmeans_plus_plus_init(self, X):
        """K-means++初始化策略"""
        n_samples, n_features = X.shape
        centroids = np.zeros((self.k, n_features))
        
        # 随机选择第一个中心
        centroids[0] = X[np.random.randint(n_samples)]
        
        for i in range(1, self.k):
            # 计算每个点到最近中心的距离
            distances = np.array([
                min([np.linalg.norm(x - c)**2 for c in centroids[:i]]) 
                for x in X
            ])
            
            # 按距离的概率选择下一个中心
            probabilities = distances / distances.sum()
            cumulative_probs = probabilities.cumsum()
            r = np.random.rand()
            
            for j, p in enumerate(cumulative_probs):
                if r < p:
                    centroids[i] = X[j]
                    break
        
        return centroids
    
    def _assign_clusters(self, X):
        """分配数据点到最近的聚类中心"""
        distances = np.sqrt(((X - self.centroids[:, np.newaxis])**2).sum(axis=2))
        return np.argmin(distances, axis=0)
    
    def _update_centroids(self, X, labels):
        """更新聚类中心"""
        new_centroids = np.zeros_like(self.centroids)
        for i in range(self.k):
            if np.sum(labels == i) > 0:
                new_centroids[i] = X[labels == i].mean(axis=0)
            else:
                # 如果某个簇为空，保持原来的中心
                new_centroids[i] = self.centroids[i]
        return new_centroids
    
    def _compute_inertia(self, X, labels):
        """计算簇内平方和"""
        inertia = 0
        for i in range(self.k):
            cluster_points = X[labels == i]
            if len(cluster_points) > 0:
                inertia += np.sum((cluster_points - self.centroids[i])**2)
        return inertia
    
    def fit(self, X):
        """训练K-means模型"""
        # 初始化聚类中心
        self._initialize_centroids(X)
        
        for iteration in range(self.max_iterations):
            # 分配聚类
            labels = self._assign_clusters(X)
            
            # 计算簇内平方和
            inertia = self._compute_inertia(X, labels)
            self.inertia_history.append(inertia)
            
            # 更新聚类中心
            new_centroids = self._update_centroids(X, labels)
            
            # 检查收敛
            centroid_shift = np.linalg.norm(new_centroids - self.centroids)
            if centroid_shift < self.tolerance:
                print(f"算法在第{iteration+1}次迭代后收敛")
                break
            
            self.centroids = new_centroids
        
        self.labels = labels
        return self
    
    def predict(self, X):
        """预测新数据点的聚类"""
        return self._assign_clusters(X)
    
    def plot_results(self, X, title="K-means聚类结果"):
        """可视化聚类结果"""
        if X.shape[1] != 2:
            print("只能可视化二维数据")
            return
        
        plt.figure(figsize=(12, 4))
        
        # 绘制聚类结果
        plt.subplot(1, 2, 1)
        colors = ['red', 'blue', 'green', 'purple', 'orange']
        for i in range(self.k):
            cluster_points = X[self.labels == i]
            plt.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                       c=colors[i % len(colors)], alpha=0.6, label=f'簇 {i+1}')
        
        # 绘制聚类中心
        plt.scatter(self.centroids[:, 0], self.centroids[:, 1], 
                   c='black', marker='x', s=200, linewidths=3, label='聚类中心')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 绘制收敛曲线
        plt.subplot(1, 2, 2)
        plt.plot(self.inertia_history, 'b-', linewidth=2)
        plt.title('簇内平方和收敛曲线')
        plt.xlabel('迭代次数')
        plt.ylabel('簇内平方和')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# 使用示例
def demonstrate_kmeans():
    # 生成示例数据
    X, y_true = make_blobs(n_samples=300, centers=4, cluster_std=0.60, 
                          random_state=0)
    
    # 比较不同初始化方法
    init_methods = ['random', 'kmeans++']
    
    for method in init_methods:
        print(f"\n使用{method}初始化:")
        kmeans = KMeans(k=4, init_method=method)
        kmeans.fit(X)
        kmeans.plot_results(X, f"K-means聚类 ({method}初始化)")
        print(f"最终簇内平方和: {kmeans.inertia_history[-1]:.2f}")

# 运行演示
demonstrate_kmeans()
```

## 5.2 数据结构类

### 题目19：实现LRU缓存
**公司来源**: Meta, Google
**难度**: ⭐⭐⭐

**题目**: 实现一个LRU（Least Recently Used）缓存，支持get和put操作，时间复杂度要求O(1)。

**推荐解答**:
```python
class LRUCache:
    def __init__(self, capacity):
        self.capacity = capacity
        self.cache = {}  # 存储key -> node的映射
        
        # 创建虚拟头尾节点
        self.head = ListNode(0, 0)
        self.tail = ListNode(0, 0)
        self.head.next = self.tail
        self.tail.prev = self.head
    
    def get(self, key):
        if key in self.cache:
            node = self.cache[key]
            # 移动到头部（最近使用）
            self._move_to_head(node)
            return node.value
        return -1
    
    def put(self, key, value):
        if key in self.cache:
            # 更新现有节点
            node = self.cache[key]
            node.value = value
            self._move_to_head(node)
        else:
            # 添加新节点
            if len(self.cache) >= self.capacity:
                # 删除尾部节点（最久未使用）
                tail_node = self._remove_tail()
                del self.cache[tail_node.key]
            
            # 添加新节点到头部
            new_node = ListNode(key, value)
            self.cache[key] = new_node
            self._add_to_head(new_node)
    
    def _add_to_head(self, node):
        """在头部添加节点"""
        node.prev = self.head
        node.next = self.head.next
        self.head.next.prev = node
        self.head.next = node
    
    def _remove_node(self, node):
        """删除节点"""
        node.prev.next = node.next
        node.next.prev = node.prev
    
    def _move_to_head(self, node):
        """移动节点到头部"""
        self._remove_node(node)
        self._add_to_head(node)
    
    def _remove_tail(self):
        """删除尾部节点"""
        tail_node = self.tail.prev
        self._remove_node(tail_node)
        return tail_node

class ListNode:
    def __init__(self, key, value):
        self.key = key
        self.value = value
        self.prev = None
        self.next = None
```

---

# 第六部分：行为面试题

## 6.1 项目经验类

### 题目20：描述一个机器学习项目
**公司来源**: 所有大厂通用
**难度**: ⭐⭐⭐

**题目**: 描述你参与的一个机器学习项目，包括问题定义、数据处理、模型选择、评估和部署。

**推荐回答框架**:

**STAR方法**:
- **Situation**: 项目背景和业务问题
- **Task**: 你的具体任务和目标
- **Action**: 采取的技术方案和实施过程
- **Result**: 最终结果和业务影响

**具体示例**:
```
Situation: 
在电商公司负责商品推荐系统优化，原有基于协同过滤的系统CTR只有2.3%，
用户反馈推荐商品相关性不高。

Task: 
设计新的推荐算法，目标是将CTR提升到3.5%以上，同时保证推荐多样性。

Action:
1. 数据分析：分析用户行为数据，发现冷启动和长尾商品推荐不足
2. 特征工程：构建用户画像、商品特征、上下文特征
3. 模型设计：采用Wide&Deep架构，结合协同过滤和内容推荐
4. 离线评估：使用AUC、Precision@K等指标评估
5. 在线A/B测试：小流量测试验证效果
6. 模型部署：使用TensorFlow Serving部署，延迟控制在50ms内

Result:
CTR从2.3%提升到4.1%，用户停留时间增加15%，GMV增长8%。
项目获得公司技术创新奖。
```

### 题目21：处理项目中的技术挑战
**公司来源**: Google, Meta
**难度**: ⭐⭐⭐

**题目**: 描述一次你在项目中遇到的技术挑战，以及你是如何解决的。

**推荐回答要点**:
1. **具体的技术问题**：不要泛泛而谈
2. **分析过程**：展示问题分析能力
3. **解决方案**：技术方案的选择和实施
4. **结果验证**：如何验证解决方案的有效性
5. **经验总结**：从中学到了什么

## 6.2 团队协作类

### 题目22：与其他团队的协作经验
**公司来源**: Amazon, Microsoft
**难度**: ⭐⭐

**题目**: 描述一次你与产品经理或业务团队协作的经验，如何平衡技术可行性和业务需求？

**推荐回答要点**:
1. **沟通技巧**：如何向非技术人员解释技术概念
2. **需求理解**：深入理解业务需求的本质
3. **方案权衡**：技术方案的优缺点分析
4. **妥协艺术**：在技术和业务间找到平衡点

---

# 附录：面试准备建议

## A.1 技术准备清单

### 数学基础
- [ ] 线性代数：矩阵运算、特征值分解、SVD
- [ ] 概率统计：贝叶斯定理、中心极限定理、假设检验
- [ ] 优化理论：梯度下降、拉格朗日乘数法、凸优化

### 机器学习
- [ ] 监督学习：线性回归、逻辑回归、SVM、决策树、集成方法
- [ ] 无监督学习：聚类、降维、关联规则
- [ ] 模型评估：交叉验证、偏差-方差权衡、ROC-AUC

### 深度学习
- [ ] 神经网络基础：前向传播、反向传播、激活函数
- [ ] CNN：卷积、池化、经典架构
- [ ] RNN：LSTM、GRU、注意力机制
- [ ] Transformer：自注意力、位置编码

### 编程实现
- [ ] Python：NumPy、Pandas、Scikit-learn
- [ ] 深度学习框架：TensorFlow、PyTorch
- [ ] 数据结构：链表、树、图、哈希表
- [ ] 算法：排序、搜索、动态规划

## A.2 面试技巧

### 技术面试
1. **思路清晰**：先理解问题，再设计方案
2. **代码规范**：变量命名、注释、错误处理
3. **复杂度分析**：时间复杂度和空间复杂度
4. **边界情况**：考虑特殊输入和异常情况

### 系统设计
1. **需求澄清**：明确系统规模和约束条件
2. **高层设计**：先画出整体架构
3. **深入细节**：选择关键组件详细设计
4. **扩展性考虑**：如何处理规模增长

### 行为面试
1. **STAR方法**：结构化描述经历
2. **具体量化**：用数据说话
3. **反思总结**：展示学习能力
4. **价值观匹配**：体现公司文化契合度

## A.3 推荐学习资源

### 书籍
- 《统计学习方法》- 李航
- 《机器学习》- 周志华
- 《深度学习》- Ian Goodfellow
- 《算法导论》- CLRS

### 在线课程
- Andrew Ng的机器学习课程
- CS231n：卷积神经网络
- CS224n：自然语言处理
- CS229：机器学习

### 实践平台
- Kaggle：数据科学竞赛
- LeetCode：算法编程练习
- GitHub：开源项目学习
- Papers With Code：最新论文和代码

---

---

# 第七部分：高频算法题详解

## 7.1 图算法类

### 题目23：图的最短路径算法
**公司来源**: Google, Amazon
**难度**: ⭐⭐⭐⭐

**题目**: 实现Dijkstra算法和Floyd-Warshall算法，比较它们的适用场景。

**推荐解答**:
```python
import heapq
from collections import defaultdict

class GraphAlgorithms:
    def dijkstra(self, graph, start):
        """
        Dijkstra算法 - 单源最短路径
        时间复杂度: O((V + E) log V)
        适用场景: 非负权重图，求单个起点到所有点的最短路径
        """
        distances = defaultdict(lambda: float('inf'))
        distances[start] = 0
        pq = [(0, start)]
        visited = set()

        while pq:
            current_dist, current = heapq.heappop(pq)

            if current in visited:
                continue

            visited.add(current)

            for neighbor, weight in graph[current]:
                distance = current_dist + weight

                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    heapq.heappush(pq, (distance, neighbor))

        return dict(distances)

    def floyd_warshall(self, graph, vertices):
        """
        Floyd-Warshall算法 - 所有点对最短路径
        时间复杂度: O(V³)
        适用场景: 稠密图，需要所有点对之间的最短路径
        """
        # 初始化距离矩阵
        dist = {}
        for i in vertices:
            dist[i] = {}
            for j in vertices:
                if i == j:
                    dist[i][j] = 0
                else:
                    dist[i][j] = float('inf')

        # 填入直接边的权重
        for u in graph:
            for v, weight in graph[u]:
                dist[u][v] = weight

        # Floyd-Warshall核心算法
        for k in vertices:
            for i in vertices:
                for j in vertices:
                    if dist[i][k] + dist[k][j] < dist[i][j]:
                        dist[i][j] = dist[i][k] + dist[k][j]

        return dist

    def bellman_ford(self, graph, start, vertices):
        """
        Bellman-Ford算法 - 处理负权重边
        时间复杂度: O(VE)
        适用场景: 有负权重边的图，可以检测负权重环
        """
        distances = {v: float('inf') for v in vertices}
        distances[start] = 0

        # 松弛操作，重复V-1次
        for _ in range(len(vertices) - 1):
            for u in graph:
                for v, weight in graph[u]:
                    if distances[u] + weight < distances[v]:
                        distances[v] = distances[u] + weight

        # 检测负权重环
        for u in graph:
            for v, weight in graph[u]:
                if distances[u] + weight < distances[v]:
                    return None, "图中存在负权重环"

        return distances, "成功"

# 使用示例和性能比较
def compare_shortest_path_algorithms():
    # 构建测试图
    graph = {
        'A': [('B', 4), ('C', 2)],
        'B': [('C', 1), ('D', 5)],
        'C': [('D', 8), ('E', 10)],
        'D': [('E', 2)],
        'E': []
    }
    vertices = ['A', 'B', 'C', 'D', 'E']

    algo = GraphAlgorithms()

    print("=== 最短路径算法比较 ===")

    # Dijkstra算法
    dijkstra_result = algo.dijkstra(graph, 'A')
    print(f"Dijkstra (从A开始): {dijkstra_result}")

    # Floyd-Warshall算法
    floyd_result = algo.floyd_warshall(graph, vertices)
    print(f"Floyd-Warshall (所有点对):")
    for i in vertices:
        for j in vertices:
            print(f"  {i} -> {j}: {floyd_result[i][j]}")

    # Bellman-Ford算法
    bellman_result, status = algo.bellman_ford(graph, 'A', vertices)
    print(f"Bellman-Ford (从A开始): {bellman_result}")
    print(f"状态: {status}")
```

### 题目24：拓扑排序的应用
**公司来源**: Meta, Microsoft
**难度**: ⭐⭐⭐

**题目**: 给定课程依赖关系，判断是否可以完成所有课程，并给出一个可能的学习顺序。

**推荐解答**:
```python
from collections import defaultdict, deque

class CourseScheduler:
    def can_finish_courses(self, num_courses, prerequisites):
        """
        判断是否可以完成所有课程（检测有向图中的环）
        使用Kahn算法进行拓扑排序
        """
        # 构建图和入度数组
        graph = defaultdict(list)
        in_degree = [0] * num_courses

        for course, prereq in prerequisites:
            graph[prereq].append(course)
            in_degree[course] += 1

        # 找到所有入度为0的节点
        queue = deque([i for i in range(num_courses) if in_degree[i] == 0])
        completed_courses = 0

        while queue:
            current = queue.popleft()
            completed_courses += 1

            # 更新相邻节点的入度
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return completed_courses == num_courses

    def find_course_order(self, num_courses, prerequisites):
        """
        找到一个可能的课程学习顺序
        """
        graph = defaultdict(list)
        in_degree = [0] * num_courses

        for course, prereq in prerequisites:
            graph[prereq].append(course)
            in_degree[course] += 1

        queue = deque([i for i in range(num_courses) if in_degree[i] == 0])
        order = []

        while queue:
            current = queue.popleft()
            order.append(current)

            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return order if len(order) == num_courses else []

    def find_all_possible_orders(self, num_courses, prerequisites):
        """
        找到所有可能的课程学习顺序（回溯算法）
        """
        graph = defaultdict(list)
        in_degree = [0] * num_courses

        for course, prereq in prerequisites:
            graph[prereq].append(course)
            in_degree[course] += 1

        def backtrack(current_order, remaining_in_degree):
            if len(current_order) == num_courses:
                return [current_order[:]]

            results = []
            available_courses = [
                i for i in range(num_courses)
                if remaining_in_degree[i] == 0 and i not in current_order
            ]

            for course in available_courses:
                # 选择当前课程
                current_order.append(course)
                new_in_degree = remaining_in_degree[:]

                # 更新入度
                for neighbor in graph[course]:
                    new_in_degree[neighbor] -= 1

                # 递归
                results.extend(backtrack(current_order, new_in_degree))

                # 回溯
                current_order.pop()

            return results

        return backtrack([], in_degree[:])

# 实际应用示例
def course_scheduling_example():
    scheduler = CourseScheduler()

    # 示例1：可以完成的课程安排
    num_courses1 = 4
    prerequisites1 = [[1, 0], [2, 0], [3, 1], [3, 2]]

    print("=== 课程调度示例 ===")
    print(f"课程数: {num_courses1}")
    print(f"先修关系: {prerequisites1}")

    can_finish = scheduler.can_finish_courses(num_courses1, prerequisites1)
    print(f"是否可以完成所有课程: {can_finish}")

    if can_finish:
        order = scheduler.find_course_order(num_courses1, prerequisites1)
        print(f"一个可能的学习顺序: {order}")

        all_orders = scheduler.find_all_possible_orders(num_courses1, prerequisites1)
        print(f"所有可能的学习顺序: {all_orders}")

    # 示例2：存在循环依赖的情况
    num_courses2 = 2
    prerequisites2 = [[1, 0], [0, 1]]

    print(f"\n课程数: {num_courses2}")
    print(f"先修关系: {prerequisites2}")

    can_finish2 = scheduler.can_finish_courses(num_courses2, prerequisites2)
    print(f"是否可以完成所有课程: {can_finish2}")

course_scheduling_example()
```

## 7.2 动态规划类

### 题目25：背包问题变种
**公司来源**: Amazon, Apple
**难度**: ⭐⭐⭐⭐

**题目**: 实现0-1背包、完全背包和多重背包问题的解决方案，并分析它们的区别。

**推荐解答**:
```python
class KnapsackProblems:
    def zero_one_knapsack(self, weights, values, capacity):
        """
        0-1背包问题：每个物品只能选择一次
        状态转移方程: dp[i][w] = max(dp[i-1][w], dp[i-1][w-weight[i]] + value[i])
        """
        n = len(weights)
        # dp[i][w]表示前i个物品在容量为w时的最大价值
        dp = [[0] * (capacity + 1) for _ in range(n + 1)]

        for i in range(1, n + 1):
            for w in range(capacity + 1):
                # 不选择第i个物品
                dp[i][w] = dp[i-1][w]

                # 选择第i个物品（如果容量允许）
                if w >= weights[i-1]:
                    dp[i][w] = max(dp[i][w],
                                  dp[i-1][w-weights[i-1]] + values[i-1])

        # 回溯找到选择的物品
        selected_items = []
        w = capacity
        for i in range(n, 0, -1):
            if dp[i][w] != dp[i-1][w]:
                selected_items.append(i-1)
                w -= weights[i-1]

        return dp[n][capacity], selected_items[::-1]

    def zero_one_knapsack_optimized(self, weights, values, capacity):
        """
        0-1背包问题的空间优化版本
        只使用一维数组，从后往前更新
        """
        dp = [0] * (capacity + 1)

        for i in range(len(weights)):
            # 从后往前更新，避免重复使用
            for w in range(capacity, weights[i] - 1, -1):
                dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        return dp[capacity]

    def complete_knapsack(self, weights, values, capacity):
        """
        完全背包问题：每个物品可以选择无限次
        状态转移方程: dp[w] = max(dp[w], dp[w-weight[i]] + value[i])
        """
        dp = [0] * (capacity + 1)

        for i in range(len(weights)):
            # 从前往后更新，允许重复使用
            for w in range(weights[i], capacity + 1):
                dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        return dp[capacity]

    def multiple_knapsack(self, weights, values, counts, capacity):
        """
        多重背包问题：每个物品有限定的数量
        使用二进制优化将多重背包转化为0-1背包
        """
        # 二进制优化：将每个物品按2的幂次拆分
        new_weights, new_values = [], []

        for i in range(len(weights)):
            count = counts[i]
            k = 1
            while k < count:
                new_weights.append(weights[i] * k)
                new_values.append(values[i] * k)
                count -= k
                k *= 2

            if count > 0:
                new_weights.append(weights[i] * count)
                new_values.append(values[i] * count)

        # 转化为0-1背包问题
        return self.zero_one_knapsack_optimized(new_weights, new_values, capacity)

    def bounded_knapsack_dp(self, weights, values, counts, capacity):
        """
        多重背包的直接DP解法（三重循环）
        """
        dp = [0] * (capacity + 1)

        for i in range(len(weights)):
            for w in range(capacity, weights[i] - 1, -1):
                for k in range(1, min(counts[i], w // weights[i]) + 1):
                    dp[w] = max(dp[w], dp[w - k * weights[i]] + k * values[i])

        return dp[capacity]

# 背包问题应用示例
def knapsack_applications():
    kp = KnapsackProblems()

    # 测试数据
    weights = [2, 1, 3, 2]
    values = [12, 10, 20, 15]
    capacity = 5
    counts = [1, 2, 1, 1]  # 用于多重背包

    print("=== 背包问题比较 ===")
    print(f"物品重量: {weights}")
    print(f"物品价值: {values}")
    print(f"背包容量: {capacity}")

    # 0-1背包
    max_value_01, selected = kp.zero_one_knapsack(weights, values, capacity)
    print(f"\n0-1背包最大价值: {max_value_01}")
    print(f"选择的物品索引: {selected}")

    # 完全背包
    max_value_complete = kp.complete_knapsack(weights, values, capacity)
    print(f"\n完全背包最大价值: {max_value_complete}")

    # 多重背包
    print(f"\n物品数量限制: {counts}")
    max_value_multiple = kp.multiple_knapsack(weights, values, counts, capacity)
    print(f"多重背包最大价值: {max_value_multiple}")

    # 性能比较
    import time

    # 大规模测试
    large_weights = [i for i in range(1, 101)]
    large_values = [i * 2 for i in range(1, 101)]
    large_capacity = 500

    start_time = time.time()
    result_optimized = kp.zero_one_knapsack_optimized(large_weights, large_values, large_capacity)
    time_optimized = time.time() - start_time

    print(f"\n大规模测试 (100个物品, 容量500):")
    print(f"优化版0-1背包: {result_optimized}, 耗时: {time_optimized:.4f}秒")

knapsack_applications()
```

### 题目26：最长公共子序列及其变种
**公司来源**: Google, Meta
**难度**: ⭐⭐⭐

**题目**: 实现最长公共子序列（LCS）、最长递增子序列（LIS）和编辑距离算法。

**推荐解答**:
```python
class SequenceAlgorithms:
    def longest_common_subsequence(self, text1, text2):
        """
        最长公共子序列 (LCS)
        时间复杂度: O(m*n), 空间复杂度: O(m*n)
        """
        m, n = len(text1), len(text2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        # 回溯构造LCS
        lcs = []
        i, j = m, n
        while i > 0 and j > 0:
            if text1[i-1] == text2[j-1]:
                lcs.append(text1[i-1])
                i -= 1
                j -= 1
            elif dp[i-1][j] > dp[i][j-1]:
                i -= 1
            else:
                j -= 1

        return dp[m][n], ''.join(reversed(lcs))

    def longest_increasing_subsequence(self, nums):
        """
        最长递增子序列 (LIS)
        使用二分查找优化，时间复杂度: O(n log n)
        """
        if not nums:
            return 0, []

        # tails[i]表示长度为i+1的递增子序列的最小尾元素
        tails = []
        # 用于回溯构造LIS
        predecessors = [-1] * len(nums)
        tail_indices = []

        for i, num in enumerate(nums):
            # 二分查找插入位置
            left, right = 0, len(tails)
            while left < right:
                mid = (left + right) // 2
                if tails[mid] < num:
                    left = mid + 1
                else:
                    right = mid

            # 更新tails数组
            if left == len(tails):
                tails.append(num)
                tail_indices.append(i)
            else:
                tails[left] = num
                tail_indices[left] = i

            # 记录前驱
            if left > 0:
                predecessors[i] = tail_indices[left - 1]

        # 回溯构造LIS
        lis = []
        current = tail_indices[-1] if tail_indices else -1
        while current != -1:
            lis.append(nums[current])
            current = predecessors[current]

        return len(tails), list(reversed(lis))

    def edit_distance(self, word1, word2):
        """
        编辑距离 (Levenshtein Distance)
        支持插入、删除、替换三种操作
        """
        m, n = len(word1), len(word2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        # 初始化边界条件
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if word1[i-1] == word2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = min(
                        dp[i-1][j] + 1,    # 删除
                        dp[i][j-1] + 1,    # 插入
                        dp[i-1][j-1] + 1   # 替换
                    )

        return dp[m][n]

    def edit_distance_with_operations(self, word1, word2):
        """
        编辑距离，同时返回具体的操作序列
        """
        m, n = len(word1), len(word2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        operations = [[[] for _ in range(n + 1)] for _ in range(m + 1)]

        # 初始化
        for i in range(m + 1):
            dp[i][0] = i
            if i > 0:
                operations[i][0] = operations[i-1][0] + [f"删除'{word1[i-1]}'"]

        for j in range(n + 1):
            dp[0][j] = j
            if j > 0:
                operations[0][j] = operations[0][j-1] + [f"插入'{word2[j-1]}'"]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if word1[i-1] == word2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                    operations[i][j] = operations[i-1][j-1]
                else:
                    delete_cost = dp[i-1][j] + 1
                    insert_cost = dp[i][j-1] + 1
                    replace_cost = dp[i-1][j-1] + 1

                    min_cost = min(delete_cost, insert_cost, replace_cost)
                    dp[i][j] = min_cost

                    if min_cost == delete_cost:
                        operations[i][j] = operations[i-1][j] + [f"删除'{word1[i-1]}'"]
                    elif min_cost == insert_cost:
                        operations[i][j] = operations[i][j-1] + [f"插入'{word2[j-1]}'"]
                    else:
                        operations[i][j] = operations[i-1][j-1] + [f"替换'{word1[i-1]}'为'{word2[j-1]}'"]

        return dp[m][n], operations[m][n]

# 序列算法应用示例
def sequence_algorithms_demo():
    seq_algo = SequenceAlgorithms()

    print("=== 序列算法演示 ===")

    # LCS示例
    text1 = "ABCDGH"
    text2 = "AEDFHR"
    lcs_length, lcs_string = seq_algo.longest_common_subsequence(text1, text2)
    print(f"\nLCS示例:")
    print(f"字符串1: {text1}")
    print(f"字符串2: {text2}")
    print(f"最长公共子序列长度: {lcs_length}")
    print(f"最长公共子序列: {lcs_string}")

    # LIS示例
    nums = [10, 9, 2, 5, 3, 7, 101, 18]
    lis_length, lis_sequence = seq_algo.longest_increasing_subsequence(nums)
    print(f"\nLIS示例:")
    print(f"数组: {nums}")
    print(f"最长递增子序列长度: {lis_length}")
    print(f"最长递增子序列: {lis_sequence}")

    # 编辑距离示例
    word1 = "horse"
    word2 = "ros"
    edit_dist = seq_algo.edit_distance(word1, word2)
    edit_dist_ops, operations = seq_algo.edit_distance_with_operations(word1, word2)
    print(f"\n编辑距离示例:")
    print(f"单词1: {word1}")
    print(f"单词2: {word2}")
    print(f"编辑距离: {edit_dist}")
    print(f"操作序列: {operations}")

sequence_algorithms_demo()
```

---

# 第七部分：高频算法题详解

## 7.1 图算法类

### 题目23：图的最短路径算法
**公司来源**: Google, Amazon
**难度**: ⭐⭐⭐⭐

**题目**: 实现Dijkstra算法和Floyd-Warshall算法，比较它们的适用场景。

**推荐解答**:
```python
import heapq
from collections import defaultdict

class GraphAlgorithms:
    def dijkstra(self, graph, start):
        """
        Dijkstra算法 - 单源最短路径
        时间复杂度: O((V + E) log V)
        适用场景: 非负权重图，求单个起点到所有点的最短路径
        """
        distances = defaultdict(lambda: float('inf'))
        distances[start] = 0
        pq = [(0, start)]
        visited = set()

        while pq:
            current_dist, current = heapq.heappop(pq)

            if current in visited:
                continue

            visited.add(current)

            for neighbor, weight in graph[current]:
                distance = current_dist + weight

                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    heapq.heappush(pq, (distance, neighbor))

        return dict(distances)

    def floyd_warshall(self, graph, vertices):
        """
        Floyd-Warshall算法 - 所有点对最短路径
        时间复杂度: O(V³)
        适用场景: 稠密图，需要所有点对之间的最短路径
        """
        # 初始化距离矩阵
        dist = {}
        for i in vertices:
            dist[i] = {}
            for j in vertices:
                if i == j:
                    dist[i][j] = 0
                else:
                    dist[i][j] = float('inf')

        # 填入直接边的权重
        for u in graph:
            for v, weight in graph[u]:
                dist[u][v] = weight

        # Floyd-Warshall核心算法
        for k in vertices:
            for i in vertices:
                for j in vertices:
                    if dist[i][k] + dist[k][j] < dist[i][j]:
                        dist[i][j] = dist[i][k] + dist[k][j]

        return dist

# 使用示例
def compare_shortest_path_algorithms():
    graph = {
        'A': [('B', 4), ('C', 2)],
        'B': [('C', 1), ('D', 5)],
        'C': [('D', 8), ('E', 10)],
        'D': [('E', 2)],
        'E': []
    }
    vertices = ['A', 'B', 'C', 'D', 'E']

    algo = GraphAlgorithms()

    # Dijkstra算法
    dijkstra_result = algo.dijkstra(graph, 'A')
    print(f"Dijkstra (从A开始): {dijkstra_result}")

    # Floyd-Warshall算法
    floyd_result = algo.floyd_warshall(graph, vertices)
    print(f"Floyd-Warshall (A到所有点): {floyd_result['A']}")
```

### 题目24：拓扑排序的应用
**公司来源**: Meta, Microsoft
**难度**: ⭐⭐⭐

**题目**: 给定课程依赖关系，判断是否可以完成所有课程，并给出一个可能的学习顺序。

**推荐解答**:
```python
from collections import defaultdict, deque

class CourseScheduler:
    def can_finish_courses(self, num_courses, prerequisites):
        """
        判断是否可以完成所有课程（检测有向图中的环）
        使用Kahn算法进行拓扑排序
        """
        # 构建图和入度数组
        graph = defaultdict(list)
        in_degree = [0] * num_courses

        for course, prereq in prerequisites:
            graph[prereq].append(course)
            in_degree[course] += 1

        # 找到所有入度为0的节点
        queue = deque([i for i in range(num_courses) if in_degree[i] == 0])
        completed_courses = 0

        while queue:
            current = queue.popleft()
            completed_courses += 1

            # 更新相邻节点的入度
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return completed_courses == num_courses

    def find_course_order(self, num_courses, prerequisites):
        """找到一个可能的课程学习顺序"""
        graph = defaultdict(list)
        in_degree = [0] * num_courses

        for course, prereq in prerequisites:
            graph[prereq].append(course)
            in_degree[course] += 1

        queue = deque([i for i in range(num_courses) if in_degree[i] == 0])
        order = []

        while queue:
            current = queue.popleft()
            order.append(current)

            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return order if len(order) == num_courses else []
```

## 7.2 动态规划类

### 题目25：背包问题变种
**公司来源**: Amazon, Apple
**难度**: ⭐⭐⭐⭐

**题目**: 实现0-1背包、完全背包和多重背包问题的解决方案，并分析它们的区别。

**推荐解答**:
```python
class KnapsackProblems:
    def zero_one_knapsack(self, weights, values, capacity):
        """
        0-1背包问题：每个物品只能选择一次
        状态转移方程: dp[i][w] = max(dp[i-1][w], dp[i-1][w-weight[i]] + value[i])
        """
        n = len(weights)
        dp = [[0] * (capacity + 1) for _ in range(n + 1)]

        for i in range(1, n + 1):
            for w in range(capacity + 1):
                # 不选择第i个物品
                dp[i][w] = dp[i-1][w]

                # 选择第i个物品（如果容量允许）
                if w >= weights[i-1]:
                    dp[i][w] = max(dp[i][w],
                                  dp[i-1][w-weights[i-1]] + values[i-1])

        return dp[n][capacity]

    def complete_knapsack(self, weights, values, capacity):
        """
        完全背包问题：每个物品可以选择无限次
        """
        dp = [0] * (capacity + 1)

        for i in range(len(weights)):
            # 从前往后更新，允许重复使用
            for w in range(weights[i], capacity + 1):
                dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        return dp[capacity]

    def multiple_knapsack(self, weights, values, counts, capacity):
        """
        多重背包问题：每个物品有限定的数量
        使用二进制优化
        """
        # 二进制优化：将每个物品按2的幂次拆分
        new_weights, new_values = [], []

        for i in range(len(weights)):
            count = counts[i]
            k = 1
            while k < count:
                new_weights.append(weights[i] * k)
                new_values.append(values[i] * k)
                count -= k
                k *= 2

            if count > 0:
                new_weights.append(weights[i] * count)
                new_values.append(values[i] * count)

        # 转化为0-1背包问题
        return self.zero_one_knapsack_optimized(new_weights, new_values, capacity)

    def zero_one_knapsack_optimized(self, weights, values, capacity):
        """0-1背包问题的空间优化版本"""
        dp = [0] * (capacity + 1)

        for i in range(len(weights)):
            # 从后往前更新，避免重复使用
            for w in range(capacity, weights[i] - 1, -1):
                dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        return dp[capacity]
```

---

# 第八部分：中国大厂特色面试题

## 8.1 字节跳动算法题

### 题目26：抖音推荐算法设计
**公司来源**: 字节跳动
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计抖音的短视频推荐算法，考虑用户兴趣、视频质量、时效性等因素。

**多种解答方案**:

**方案A：多路召回+精排**
- 召回策略：协同过滤、内容推荐、热门推荐、关注推荐
- 特征工程：用户画像、视频特征、上下文特征
- 排序模型：Wide&Deep、DeepFM、DIN等

**方案B：实时推荐系统**
- 流式计算：Kafka + Flink实时特征更新
- 在线学习：增量更新用户兴趣模型
- 冷启动：新用户基于人口统计学推荐

**方案C：多目标优化**
- 目标函数：点击率、完播率、点赞率、分享率
- 多任务学习：MMOE、PLE等架构
- 业务约束：多样性、新鲜度、内容安全

**推荐方案**: 方案A + 方案C
- 展示完整的推荐系统架构
- 重点讲解多目标优化的挑战

**实际案例**:
```python
class DouyinRecommendationSystem:
    def __init__(self):
        self.recall_strategies = {
            'collaborative': CollaborativeFiltering(),
            'content': ContentBasedFiltering(),
            'trending': TrendingVideos(),
            'following': FollowingBasedRecall()
        }

        self.ranking_model = MultiTaskModel(
            tasks=['ctr', 'view_time', 'like', 'share'],
            shared_layers=3,
            task_specific_layers=2
        )

    def recommend_videos(self, user_id, context):
        # 1. 多路召回
        candidates = self.multi_recall(user_id, context)

        # 2. 特征工程
        features = self.extract_features(user_id, candidates, context)

        # 3. 多目标排序
        scores = self.ranking_model.predict(features)

        # 4. 重排序（多样性、新鲜度）
        final_videos = self.rerank(candidates, scores, context)

        return final_videos

    def handle_cold_start(self, user_id):
        """处理新用户冷启动"""
        if self.is_new_user(user_id):
            # 基于人口统计学和热门内容
            return self.demographic_and_trending_recommend(user_id)
        else:
            # 基于少量交互快速学习
            return self.few_shot_learning_recommend(user_id)
```

### 题目27：今日头条新闻分类算法
**公司来源**: 字节跳动
**难度**: ⭐⭐⭐⭐

**题目**: 设计今日头条的新闻自动分类系统，包括文本预处理、特征提取和分类模型。

**推荐解答**:
```python
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from transformers import BertTokenizer, BertForSequenceClassification
import torch

class NewsClassificationSystem:
    def __init__(self, model_type='bert'):
        self.model_type = model_type
        self.categories = ['科技', '体育', '娱乐', '财经', '政治', '社会']

        if model_type == 'traditional':
            self.vectorizer = TfidfVectorizer(max_features=10000)
            self.classifier = LogisticRegression()
        elif model_type == 'bert':
            self.tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
            self.model = BertForSequenceClassification.from_pretrained(
                'bert-base-chinese',
                num_labels=len(self.categories)
            )

    def preprocess_text(self, text):
        """文本预处理"""
        # 1. 去除特殊字符
        import re
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', ' ', text)

        # 2. 分词
        words = jieba.cut(text)

        # 3. 去停用词
        stopwords = self.load_stopwords()
        words = [word for word in words if word not in stopwords and len(word) > 1]

        return ' '.join(words)

    def extract_features_traditional(self, texts):
        """传统方法特征提取"""
        # TF-IDF特征
        tfidf_features = self.vectorizer.fit_transform(texts)

        # 可以添加其他特征
        # - 文本长度
        # - 关键词密度
        # - 命名实体

        return tfidf_features

    def train_traditional_model(self, texts, labels):
        """训练传统机器学习模型"""
        # 预处理
        processed_texts = [self.preprocess_text(text) for text in texts]

        # 特征提取
        features = self.extract_features_traditional(processed_texts)

        # 训练分类器
        self.classifier.fit(features, labels)

        return self.classifier

    def train_bert_model(self, texts, labels, epochs=3):
        """训练BERT模型"""
        from torch.utils.data import DataLoader, Dataset

        class NewsDataset(Dataset):
            def __init__(self, texts, labels, tokenizer, max_length=512):
                self.texts = texts
                self.labels = labels
                self.tokenizer = tokenizer
                self.max_length = max_length

            def __len__(self):
                return len(self.texts)

            def __getitem__(self, idx):
                text = str(self.texts[idx])
                label = self.labels[idx]

                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )

                return {
                    'input_ids': encoding['input_ids'].flatten(),
                    'attention_mask': encoding['attention_mask'].flatten(),
                    'labels': torch.tensor(label, dtype=torch.long)
                }

        # 创建数据集和数据加载器
        dataset = NewsDataset(texts, labels, self.tokenizer)
        dataloader = DataLoader(dataset, batch_size=16, shuffle=True)

        # 训练模型
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=2e-5)

        self.model.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch in dataloader:
                optimizer.zero_grad()

                outputs = self.model(
                    input_ids=batch['input_ids'],
                    attention_mask=batch['attention_mask'],
                    labels=batch['labels']
                )

                loss = outputs.loss
                loss.backward()
                optimizer.step()

                total_loss += loss.item()

            print(f"Epoch {epoch+1}, Average Loss: {total_loss/len(dataloader)}")

    def predict(self, text):
        """预测新闻分类"""
        if self.model_type == 'traditional':
            processed_text = self.preprocess_text(text)
            features = self.vectorizer.transform([processed_text])
            prediction = self.classifier.predict(features)[0]
            probabilities = self.classifier.predict_proba(features)[0]

        elif self.model_type == 'bert':
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=512,
                return_tensors='pt'
            )

            self.model.eval()
            with torch.no_grad():
                outputs = self.model(
                    input_ids=encoding['input_ids'],
                    attention_mask=encoding['attention_mask']
                )

                probabilities = torch.softmax(outputs.logits, dim=-1)
                prediction = torch.argmax(probabilities, dim=-1).item()
                probabilities = probabilities.numpy()[0]

        return {
            'category': self.categories[prediction],
            'confidence': probabilities[prediction],
            'all_probabilities': dict(zip(self.categories, probabilities))
        }

    def load_stopwords(self):
        """加载停用词表"""
        # 这里应该加载实际的停用词文件
        return {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}

# 使用示例
def news_classification_demo():
    # 示例数据
    sample_texts = [
        "苹果公司发布了最新的iPhone，搭载了先进的AI芯片",
        "中国足球队在世界杯预选赛中取得重要胜利",
        "著名演员获得金像奖最佳男主角奖项",
        "央行宣布降准，释放流动性支持实体经济",
        "全国人大通过重要法律修正案"
    ]

    sample_labels = [0, 1, 2, 3, 4]  # 对应科技、体育、娱乐、财经、政治

    # 传统方法
    traditional_classifier = NewsClassificationSystem(model_type='traditional')
    traditional_classifier.train_traditional_model(sample_texts, sample_labels)

    # 测试预测
    test_text = "特斯拉发布新款电动汽车，续航里程突破1000公里"
    result = traditional_classifier.predict(test_text)
    print(f"新闻分类结果: {result}")

news_classification_demo()
```

## 8.2 阿里巴巴算法题

### 题目28：淘宝商品搜索排序
**公司来源**: 阿里巴巴
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计淘宝的商品搜索排序算法，考虑相关性、商品质量、商家信誉等因素。

**推荐解答**:
```python
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import GradientBoostingRegressor

class TaobaoSearchRanking:
    def __init__(self):
        self.text_similarity_model = None
        self.ranking_model = GradientBoostingRegressor(n_estimators=100)
        self.scaler = StandardScaler()

        # 特征权重配置
        self.feature_weights = {
            'relevance': 0.4,      # 相关性
            'quality': 0.25,       # 商品质量
            'seller_reputation': 0.15,  # 商家信誉
            'price_competitiveness': 0.1,  # 价格竞争力
            'user_preference': 0.1  # 用户偏好
        }

    def calculate_text_relevance(self, query, product_title, product_description):
        """计算文本相关性"""
        # 1. 关键词匹配
        query_words = set(query.lower().split())
        title_words = set(product_title.lower().split())
        desc_words = set(product_description.lower().split())

        # 标题匹配度
        title_match = len(query_words & title_words) / len(query_words)

        # 描述匹配度
        desc_match = len(query_words & desc_words) / len(query_words)

        # 2. TF-IDF相似度（简化版）
        def calculate_tfidf_similarity(query, text):
            # 这里应该使用真实的TF-IDF计算
            # 简化为词频重叠度
            query_words = query.lower().split()
            text_words = text.lower().split()

            common_words = set(query_words) & set(text_words)
            return len(common_words) / max(len(set(query_words)), 1)

        tfidf_title = calculate_tfidf_similarity(query, product_title)
        tfidf_desc = calculate_tfidf_similarity(query, product_description)

        # 综合相关性得分
        relevance_score = (
            0.6 * title_match +
            0.2 * desc_match +
            0.15 * tfidf_title +
            0.05 * tfidf_desc
        )

        return min(relevance_score, 1.0)

    def calculate_quality_score(self, product_data):
        """计算商品质量得分"""
        # 商品质量指标
        sales_volume = product_data.get('sales_volume', 0)
        review_score = product_data.get('review_score', 0)
        review_count = product_data.get('review_count', 0)
        return_rate = product_data.get('return_rate', 0)

        # 销量得分（对数变换）
        sales_score = min(np.log(sales_volume + 1) / 10, 1.0)

        # 评价得分
        review_score_normalized = review_score / 5.0

        # 评价数量得分
        review_count_score = min(np.log(review_count + 1) / 8, 1.0)

        # 退货率得分（越低越好）
        return_score = max(0, 1 - return_rate)

        quality_score = (
            0.3 * sales_score +
            0.4 * review_score_normalized +
            0.2 * review_count_score +
            0.1 * return_score
        )

        return quality_score

    def calculate_seller_reputation(self, seller_data):
        """计算商家信誉得分"""
        seller_level = seller_data.get('seller_level', 0)  # 商家等级
        seller_score = seller_data.get('seller_score', 0)  # 商家评分
        years_in_business = seller_data.get('years_in_business', 0)
        dispute_rate = seller_data.get('dispute_rate', 0)

        # 商家等级得分
        level_score = min(seller_level / 20, 1.0)

        # 商家评分得分
        score_normalized = seller_score / 5.0

        # 经营年限得分
        years_score = min(years_in_business / 10, 1.0)

        # 纠纷率得分（越低越好）
        dispute_score = max(0, 1 - dispute_rate * 10)

        reputation_score = (
            0.3 * level_score +
            0.4 * score_normalized +
            0.2 * years_score +
            0.1 * dispute_score
        )

        return reputation_score

    def calculate_price_competitiveness(self, product_price, category_avg_price):
        """计算价格竞争力"""
        if category_avg_price == 0:
            return 0.5

        price_ratio = product_price / category_avg_price

        # 价格适中时得分最高
        if 0.7 <= price_ratio <= 1.3:
            return 1.0
        elif price_ratio < 0.7:
            # 价格过低可能质量有问题
            return 0.8
        else:
            # 价格过高竞争力下降
            return max(0, 1.5 - price_ratio)

    def calculate_user_preference(self, user_profile, product_data):
        """计算用户偏好匹配度"""
        # 用户历史购买类目偏好
        user_categories = user_profile.get('preferred_categories', [])
        product_category = product_data.get('category', '')

        category_match = 1.0 if product_category in user_categories else 0.3

        # 用户价格偏好
        user_price_range = user_profile.get('price_range', [0, float('inf')])
        product_price = product_data.get('price', 0)

        price_match = 1.0 if user_price_range[0] <= product_price <= user_price_range[1] else 0.5

        # 用户品牌偏好
        user_brands = user_profile.get('preferred_brands', [])
        product_brand = product_data.get('brand', '')

        brand_match = 1.0 if product_brand in user_brands else 0.7

        preference_score = (
            0.5 * category_match +
            0.3 * price_match +
            0.2 * brand_match
        )

        return preference_score

    def extract_features(self, query, products, user_profile, category_stats):
        """提取所有特征"""
        features = []

        for product in products:
            # 相关性特征
            relevance = self.calculate_text_relevance(
                query,
                product['title'],
                product['description']
            )

            # 质量特征
            quality = self.calculate_quality_score(product)

            # 商家信誉特征
            reputation = self.calculate_seller_reputation(product['seller'])

            # 价格竞争力特征
            category_avg_price = category_stats.get(product['category'], {}).get('avg_price', 0)
            price_comp = self.calculate_price_competitiveness(
                product['price'],
                category_avg_price
            )

            # 用户偏好特征
            user_pref = self.calculate_user_preference(user_profile, product)

            # 组合特征向量
            feature_vector = [
                relevance,
                quality,
                reputation,
                price_comp,
                user_pref,
                # 可以添加更多特征
                product.get('click_rate', 0),
                product.get('conversion_rate', 0),
                product.get('is_promoted', 0)
            ]

            features.append(feature_vector)

        return np.array(features)

    def train_ranking_model(self, training_data):
        """训练排序模型"""
        X = []
        y = []

        for query_data in training_data:
            query = query_data['query']
            products = query_data['products']
            user_profile = query_data['user_profile']
            category_stats = query_data['category_stats']
            relevance_labels = query_data['relevance_labels']  # 人工标注的相关性

            features = self.extract_features(query, products, user_profile, category_stats)

            X.extend(features)
            y.extend(relevance_labels)

        X = np.array(X)
        y = np.array(y)

        # 特征标准化
        X_scaled = self.scaler.fit_transform(X)

        # 训练模型
        self.ranking_model.fit(X_scaled, y)

        return self.ranking_model

    def rank_products(self, query, products, user_profile, category_stats):
        """对商品进行排序"""
        if not products:
            return []

        # 提取特征
        features = self.extract_features(query, products, user_profile, category_stats)

        # 特征标准化
        features_scaled = self.scaler.transform(features)

        # 预测相关性得分
        relevance_scores = self.ranking_model.predict(features_scaled)

        # 结合产品信息和得分
        ranked_products = []
        for i, product in enumerate(products):
            product_with_score = product.copy()
            product_with_score['relevance_score'] = relevance_scores[i]
            ranked_products.append(product_with_score)

        # 按得分排序
        ranked_products.sort(key=lambda x: x['relevance_score'], reverse=True)

        return ranked_products

# 使用示例
def taobao_search_demo():
    # 创建搜索排序系统
    search_system = TaobaoSearchRanking()

    # 示例查询和商品
    query = "苹果手机"

    products = [
        {
            'id': 1,
            'title': 'Apple iPhone 14 Pro 128GB',
            'description': '苹果官方正品手机，A16芯片，专业摄像系统',
            'price': 7999,
            'category': '手机通讯',
            'brand': 'Apple',
            'sales_volume': 10000,
            'review_score': 4.8,
            'review_count': 5000,
            'return_rate': 0.02,
            'seller': {
                'seller_level': 15,
                'seller_score': 4.9,
                'years_in_business': 8,
                'dispute_rate': 0.001
            },
            'click_rate': 0.15,
            'conversion_rate': 0.08,
            'is_promoted': 1
        },
        {
            'id': 2,
            'title': '华为Mate50 Pro 256GB',
            'description': '华为旗舰手机，麒麟芯片，超强拍照',
            'price': 6999,
            'category': '手机通讯',
            'brand': 'Huawei',
            'sales_volume': 8000,
            'review_score': 4.7,
            'review_count': 3000,
            'return_rate': 0.03,
            'seller': {
                'seller_level': 12,
                'seller_score': 4.8,
                'years_in_business': 6,
                'dispute_rate': 0.002
            },
            'click_rate': 0.12,
            'conversion_rate': 0.06,
            'is_promoted': 0
        }
    ]

    user_profile = {
        'preferred_categories': ['手机通讯', '数码配件'],
        'price_range': [5000, 10000],
        'preferred_brands': ['Apple', 'Samsung']
    }

    category_stats = {
        '手机通讯': {'avg_price': 3500}
    }

    # 对商品进行排序
    ranked_products = search_system.rank_products(
        query, products, user_profile, category_stats
    )

    print("=== 淘宝搜索排序结果 ===")
    for i, product in enumerate(ranked_products):
        print(f"{i+1}. {product['title']}")
        print(f"   价格: ¥{product['price']}")
        print(f"   相关性得分: {product['relevance_score']:.3f}")
        print()

taobao_search_demo()
```

## 8.3 腾讯算法题

### 题目29：微信朋友圈信息流排序
**公司来源**: 腾讯
**难度**: ⭐⭐⭐⭐

**题目**: 设计微信朋友圈的信息流排序算法，平衡时效性、社交关系和内容质量。

**推荐解答**:
```python
import numpy as np
from datetime import datetime, timedelta

class WeChatMomentsRanking:
    def __init__(self):
        self.time_decay_factor = 0.1  # 时间衰减因子
        self.social_weight = 0.4      # 社交关系权重
        self.content_weight = 0.3     # 内容质量权重
        self.engagement_weight = 0.3  # 互动权重

    def calculate_time_score(self, post_time):
        """计算时间得分（越新越高）"""
        now = datetime.now()
        time_diff = (now - post_time).total_seconds() / 3600  # 小时差

        # 指数衰减
        time_score = np.exp(-self.time_decay_factor * time_diff)
        return time_score

    def calculate_social_score(self, user_id, post_author_id, social_graph):
        """计算社交关系得分"""
        # 1. 直接好友关系
        if post_author_id in social_graph.get(user_id, {}).get('friends', []):
            friend_score = 1.0
        else:
            friend_score = 0.0

        # 2. 互动频率
        interaction_freq = social_graph.get(user_id, {}).get('interactions', {}).get(post_author_id, 0)
        interaction_score = min(interaction_freq / 10, 1.0)  # 标准化到[0,1]

        # 3. 共同好友数
        user_friends = set(social_graph.get(user_id, {}).get('friends', []))
        author_friends = set(social_graph.get(post_author_id, {}).get('friends', []))
        common_friends = len(user_friends & author_friends)
        common_score = min(common_friends / 20, 1.0)

        # 4. 亲密度（基于聊天频率、通话等）
        intimacy = social_graph.get(user_id, {}).get('intimacy', {}).get(post_author_id, 0)
        intimacy_score = min(intimacy / 100, 1.0)

        social_score = (
            0.4 * friend_score +
            0.3 * interaction_score +
            0.2 * common_score +
            0.1 * intimacy_score
        )

        return social_score

    def calculate_content_score(self, post_data):
        """计算内容质量得分"""
        # 1. 内容类型得分
        content_type = post_data.get('type', 'text')
        type_scores = {
            'text': 0.6,
            'image': 0.8,
            'video': 0.9,
            'link': 0.7
        }
        type_score = type_scores.get(content_type, 0.5)

        # 2. 内容长度得分
        content_length = len(post_data.get('content', ''))
        if 10 <= content_length <= 200:
            length_score = 1.0
        elif content_length < 10:
            length_score = 0.5
        else:
            length_score = max(0.3, 1.0 - (content_length - 200) / 1000)

        # 3. 是否包含@提及
        has_mentions = '@' in post_data.get('content', '')
        mention_score = 1.2 if has_mentions else 1.0

        # 4. 是否包含话题标签
        has_hashtags = '#' in post_data.get('content', '')
        hashtag_score = 1.1 if has_hashtags else 1.0

        content_score = type_score * length_score * mention_score * hashtag_score
        return min(content_score, 1.0)

    def calculate_engagement_score(self, post_data):
        """计算互动得分"""
        likes = post_data.get('likes', 0)
        comments = post_data.get('comments', 0)
        shares = post_data.get('shares', 0)

        # 不同互动的权重
        like_weight = 1.0
        comment_weight = 3.0  # 评论比点赞更有价值
        share_weight = 5.0    # 分享最有价值

        # 计算加权互动数
        weighted_engagement = (
            likes * like_weight +
            comments * comment_weight +
            shares * share_weight
        )

        # 对数变换避免热门内容过度占优
        engagement_score = np.log(weighted_engagement + 1) / 10
        return min(engagement_score, 1.0)

    def rank_moments(self, user_id, posts, social_graph):
        """对朋友圈动态进行排序"""
        scored_posts = []

        for post in posts:
            # 计算各项得分
            time_score = self.calculate_time_score(post['post_time'])
            social_score = self.calculate_social_score(
                user_id, post['author_id'], social_graph
            )
            content_score = self.calculate_content_score(post)
            engagement_score = self.calculate_engagement_score(post)

            # 综合得分
            final_score = (
                time_score * 0.2 +  # 时效性权重较低，因为朋友圈不完全按时间排序
                social_score * self.social_weight +
                content_score * self.content_weight +
                engagement_score * self.engagement_weight
            )

            post_with_score = post.copy()
            post_with_score.update({
                'time_score': time_score,
                'social_score': social_score,
                'content_score': content_score,
                'engagement_score': engagement_score,
                'final_score': final_score
            })

            scored_posts.append(post_with_score)

        # 按综合得分排序
        scored_posts.sort(key=lambda x: x['final_score'], reverse=True)

        return scored_posts

    def apply_diversity_filter(self, ranked_posts, max_posts_per_user=2):
        """应用多样性过滤，避免同一用户刷屏"""
        user_post_count = {}
        filtered_posts = []

        for post in ranked_posts:
            author_id = post['author_id']
            current_count = user_post_count.get(author_id, 0)

            if current_count < max_posts_per_user:
                filtered_posts.append(post)
                user_post_count[author_id] = current_count + 1

        return filtered_posts

# 使用示例
def wechat_moments_demo():
    ranking_system = WeChatMomentsRanking()

    # 示例数据
    current_user = 'user_123'

    posts = [
        {
            'id': 1,
            'author_id': 'friend_1',
            'content': '今天天气真好，出去爬山了！#户外运动',
            'type': 'image',
            'post_time': datetime.now() - timedelta(hours=2),
            'likes': 15,
            'comments': 3,
            'shares': 1
        },
        {
            'id': 2,
            'author_id': 'friend_2',
            'content': '分享一篇很有意思的文章',
            'type': 'link',
            'post_time': datetime.now() - timedelta(hours=1),
            'likes': 8,
            'comments': 2,
            'shares': 0
        },
        {
            'id': 3,
            'author_id': 'friend_1',
            'content': '晚餐时间到了@friend_3',
            'type': 'text',
            'post_time': datetime.now() - timedelta(minutes=30),
            'likes': 5,
            'comments': 1,
            'shares': 0
        }
    ]

    social_graph = {
        'user_123': {
            'friends': ['friend_1', 'friend_2', 'friend_3'],
            'interactions': {'friend_1': 15, 'friend_2': 8, 'friend_3': 20},
            'intimacy': {'friend_1': 80, 'friend_2': 40, 'friend_3': 95}
        }
    }

    # 排序朋友圈动态
    ranked_posts = ranking_system.rank_moments(current_user, posts, social_graph)

    # 应用多样性过滤
    final_posts = ranking_system.apply_diversity_filter(ranked_posts)

    print("=== 微信朋友圈排序结果 ===")
    for i, post in enumerate(final_posts):
        print(f"{i+1}. 作者: {post['author_id']}")
        print(f"   内容: {post['content'][:50]}...")
        print(f"   综合得分: {post['final_score']:.3f}")
        print(f"   (时效:{post['time_score']:.2f}, 社交:{post['social_score']:.2f}, "
              f"内容:{post['content_score']:.2f}, 互动:{post['engagement_score']:.2f})")
        print()

wechat_moments_demo()
```

### 题目30：王者荣耀匹配算法
**公司来源**: 腾讯
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计王者荣耀的玩家匹配算法，考虑技能水平、等待时间、网络延迟等因素。

**推荐解答**:
```python
import heapq
import random
from collections import defaultdict
from datetime import datetime, timedelta

class GameMatchmaking:
    def __init__(self):
        self.skill_tolerance = 200      # 技能差异容忍度
        self.max_wait_time = 300       # 最大等待时间（秒）
        self.latency_threshold = 100   # 延迟阈值（毫秒）
        self.team_size = 5             # 队伍大小

        # 等待队列：按技能等级分组
        self.waiting_queues = defaultdict(list)
        self.player_wait_start = {}

    def calculate_skill_rating(self, player_stats):
        """计算玩家技能评级"""
        # 基础段位分数
        rank_scores = {
            'bronze': 1000, 'silver': 1200, 'gold': 1400,
            'platinum': 1600, 'diamond': 1800, 'master': 2000,
            'grandmaster': 2200, 'king': 2400
        }

        base_score = rank_scores.get(player_stats.get('rank', 'bronze'), 1000)

        # 胜率调整
        win_rate = player_stats.get('win_rate', 0.5)
        win_rate_adjustment = (win_rate - 0.5) * 400

        # 近期表现调整
        recent_performance = player_stats.get('recent_performance', 0)  # -1到1
        performance_adjustment = recent_performance * 200

        # KDA调整
        kda = player_stats.get('kda', 1.0)
        kda_adjustment = min((kda - 1.0) * 100, 200)

        # 连胜/连败调整
        streak = player_stats.get('streak', 0)  # 正数连胜，负数连败
        streak_adjustment = streak * 20

        skill_rating = (
            base_score +
            win_rate_adjustment +
            performance_adjustment +
            kda_adjustment +
            streak_adjustment
        )

        return max(skill_rating, 500)  # 最低500分

    def calculate_match_quality(self, team1_players, team2_players):
        """计算匹配质量"""
        # 1. 技能平衡性
        team1_avg_skill = np.mean([p['skill_rating'] for p in team1_players])
        team2_avg_skill = np.mean([p['skill_rating'] for p in team2_players])
        skill_diff = abs(team1_avg_skill - team2_avg_skill)
        skill_balance = max(0, 1 - skill_diff / 500)

        # 2. 队内技能分布
        team1_skill_std = np.std([p['skill_rating'] for p in team1_players])
        team2_skill_std = np.std([p['skill_rating'] for p in team2_players])
        avg_std = (team1_skill_std + team2_skill_std) / 2
        skill_distribution = max(0, 1 - avg_std / 300)

        # 3. 位置平衡（如果有位置偏好）
        position_balance = self.calculate_position_balance(team1_players, team2_players)

        # 4. 网络延迟
        latency_quality = self.calculate_latency_quality(team1_players + team2_players)

        # 综合质量得分
        match_quality = (
            0.4 * skill_balance +
            0.2 * skill_distribution +
            0.2 * position_balance +
            0.2 * latency_quality
        )

        return match_quality

    def calculate_position_balance(self, team1_players, team2_players):
        """计算位置平衡性"""
        positions = ['top', 'jungle', 'mid', 'adc', 'support']

        def get_team_position_score(team_players):
            position_preferences = defaultdict(list)
            for player in team_players:
                for pos in player.get('preferred_positions', []):
                    position_preferences[pos].append(player['skill_rating'])

            # 检查是否每个位置都有合适的玩家
            position_score = 0
            for pos in positions:
                if pos in position_preferences:
                    # 选择该位置技能最高的玩家
                    position_score += max(position_preferences[pos])
                else:
                    # 没有该位置的玩家，扣分
                    position_score += 1000  # 基础分数

            return position_score / (len(positions) * 2000)  # 标准化

        team1_pos_score = get_team_position_score(team1_players)
        team2_pos_score = get_team_position_score(team2_players)

        return (team1_pos_score + team2_pos_score) / 2

    def calculate_latency_quality(self, all_players):
        """计算网络延迟质量"""
        latencies = [p.get('latency', 50) for p in all_players]
        avg_latency = np.mean(latencies)
        max_latency = max(latencies)

        # 平均延迟越低越好，最大延迟不能太高
        avg_quality = max(0, 1 - avg_latency / 200)
        max_quality = max(0, 1 - max_latency / 300)

        return 0.7 * avg_quality + 0.3 * max_quality

    def add_player_to_queue(self, player_data):
        """将玩家加入匹配队列"""
        player_id = player_data['player_id']
        skill_rating = self.calculate_skill_rating(player_data['stats'])

        player_data['skill_rating'] = skill_rating
        player_data['queue_time'] = datetime.now()

        # 根据技能等级分组（每200分一组）
        skill_group = skill_rating // 200
        self.waiting_queues[skill_group].append(player_data)
        self.player_wait_start[player_id] = datetime.now()

        print(f"玩家 {player_id} 加入队列，技能评级: {skill_rating}")

    def find_match(self):
        """寻找匹配"""
        current_time = datetime.now()

        # 遍历所有技能组
        for skill_group in list(self.waiting_queues.keys()):
            players_in_group = self.waiting_queues[skill_group]

            if len(players_in_group) >= self.team_size * 2:
                # 尝试在当前组内匹配
                match = self.try_match_within_group(players_in_group)
                if match:
                    return match

            # 如果等待时间过长，扩大搜索范围
            long_wait_players = [
                p for p in players_in_group
                if (current_time - p['queue_time']).seconds > 60
            ]

            if long_wait_players:
                match = self.try_cross_group_match(skill_group, long_wait_players)
                if match:
                    return match

        return None

    def try_match_within_group(self, players):
        """在技能组内尝试匹配"""
        if len(players) < self.team_size * 2:
            return None

        # 随机选择玩家进行匹配尝试
        random.shuffle(players)

        for i in range(len(players) - self.team_size * 2 + 1):
            candidate_players = players[i:i + self.team_size * 2]

            # 尝试不同的队伍分配
            for team_split in self.generate_team_splits(candidate_players):
                team1, team2 = team_split
                match_quality = self.calculate_match_quality(team1, team2)

                if match_quality > 0.7:  # 质量阈值
                    # 从队列中移除匹配的玩家
                    for player in candidate_players:
                        if player in self.waiting_queues[player['skill_rating'] // 200]:
                            self.waiting_queues[player['skill_rating'] // 200].remove(player)
                        if player['player_id'] in self.player_wait_start:
                            del self.player_wait_start[player['player_id']]

                    return {
                        'team1': team1,
                        'team2': team2,
                        'match_quality': match_quality,
                        'match_id': f"match_{datetime.now().timestamp()}"
                    }

        return None

    def try_cross_group_match(self, base_skill_group, long_wait_players):
        """跨技能组匹配（为等待时间长的玩家）"""
        # 扩大搜索范围到相邻技能组
        adjacent_groups = [base_skill_group - 1, base_skill_group, base_skill_group + 1]

        all_candidates = long_wait_players.copy()
        for group in adjacent_groups:
            if group != base_skill_group and group in self.waiting_queues:
                all_candidates.extend(self.waiting_queues[group])

        if len(all_candidates) >= self.team_size * 2:
            # 降低匹配质量要求
            return self.try_match_with_relaxed_criteria(all_candidates)

        return None

    def try_match_with_relaxed_criteria(self, players):
        """使用放宽的标准进行匹配"""
        if len(players) < self.team_size * 2:
            return None

        # 按技能评级排序
        players.sort(key=lambda x: x['skill_rating'])

        # 尝试平衡分配
        team1 = []
        team2 = []

        for i, player in enumerate(players[:self.team_size * 2]):
            if i % 2 == 0:
                team1.append(player)
            else:
                team2.append(player)

        match_quality = self.calculate_match_quality(team1, team2)

        if match_quality > 0.5:  # 降低质量要求
            # 从队列中移除匹配的玩家
            for player in team1 + team2:
                skill_group = player['skill_rating'] // 200
                if player in self.waiting_queues[skill_group]:
                    self.waiting_queues[skill_group].remove(player)
                if player['player_id'] in self.player_wait_start:
                    del self.player_wait_start[player['player_id']]

            return {
                'team1': team1,
                'team2': team2,
                'match_quality': match_quality,
                'match_id': f"match_{datetime.now().timestamp()}"
            }

        return None

    def generate_team_splits(self, players):
        """生成可能的队伍分配方案"""
        from itertools import combinations

        # 生成所有可能的5人组合作为team1
        for team1_indices in combinations(range(len(players)), self.team_size):
            team1 = [players[i] for i in team1_indices]
            team2 = [players[i] for i in range(len(players)) if i not in team1_indices]

            if len(team2) == self.team_size:
                yield team1, team2

# 使用示例
def game_matchmaking_demo():
    matchmaker = GameMatchmaking()

    # 模拟玩家数据
    sample_players = [
        {
            'player_id': f'player_{i}',
            'stats': {
                'rank': random.choice(['gold', 'platinum', 'diamond']),
                'win_rate': random.uniform(0.4, 0.7),
                'recent_performance': random.uniform(-0.5, 0.5),
                'kda': random.uniform(0.8, 2.5),
                'streak': random.randint(-3, 5)
            },
            'preferred_positions': random.sample(['top', 'jungle', 'mid', 'adc', 'support'], 2),
            'latency': random.randint(20, 120)
        }
        for i in range(20)
    ]

    # 将玩家加入队列
    for player in sample_players:
        matchmaker.add_player_to_queue(player)

    # 尝试匹配
    match_result = matchmaker.find_match()

    if match_result:
        print("\n=== 匹配成功 ===")
        print(f"匹配ID: {match_result['match_id']}")
        print(f"匹配质量: {match_result['match_quality']:.3f}")

        print("\n队伍1:")
        for player in match_result['team1']:
            print(f"  {player['player_id']} (技能: {player['skill_rating']:.0f})")

        print("\n队伍2:")
        for player in match_result['team2']:
            print(f"  {player['player_id']} (技能: {player['skill_rating']:.0f})")

        # 计算队伍平均技能
        team1_avg = np.mean([p['skill_rating'] for p in match_result['team1']])
        team2_avg = np.mean([p['skill_rating'] for p in match_result['team2']])
        print(f"\n队伍平均技能: 队伍1={team1_avg:.0f}, 队伍2={team2_avg:.0f}")
        print(f"技能差异: {abs(team1_avg - team2_avg):.0f}")
    else:
        print("暂时无法找到合适的匹配")

game_matchmaking_demo()
```

## 8.4 国际大厂算法题

### 题目31：Netflix个性化推荐系统
**公司来源**: Netflix
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计Netflix的个性化推荐系统，考虑用户观看历史、内容特征、协同过滤等多种因素。

**推荐解答**:
```python
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF
import pandas as pd

class NetflixRecommendationSystem:
    def __init__(self):
        self.user_item_matrix = None
        self.content_features = None
        self.collaborative_model = None
        self.content_model = None
        self.hybrid_weights = {'collaborative': 0.6, 'content': 0.4}

    def build_user_item_matrix(self, viewing_data):
        """构建用户-物品评分矩阵"""
        # viewing_data: [user_id, item_id, rating, watch_time, completion_rate]

        # 计算隐式反馈评分
        def calculate_implicit_rating(row):
            base_rating = row['rating'] if row['rating'] > 0 else 3.0

            # 观看时长调整
            watch_time_factor = min(row['watch_time'] / 60, 2.0)  # 最多2倍权重

            # 完成度调整
            completion_factor = 1 + row['completion_rate']

            # 重复观看调整
            repeat_factor = 1 + min(row.get('repeat_count', 0) * 0.5, 1.0)

            implicit_rating = base_rating * watch_time_factor * completion_factor * repeat_factor
            return min(implicit_rating, 5.0)

        viewing_data['implicit_rating'] = viewing_data.apply(calculate_implicit_rating, axis=1)

        # 构建评分矩阵
        self.user_item_matrix = viewing_data.pivot_table(
            index='user_id',
            columns='item_id',
            values='implicit_rating',
            fill_value=0
        )

        return self.user_item_matrix

    def extract_content_features(self, content_metadata):
        """提取内容特征"""
        features = {}

        for item_id, metadata in content_metadata.items():
            feature_vector = []

            # 1. 类型特征（one-hot编码）
            genres = metadata.get('genres', [])
            genre_features = self.encode_genres(genres)
            feature_vector.extend(genre_features)

            # 2. 演员特征（top演员的one-hot编码）
            actors = metadata.get('actors', [])
            actor_features = self.encode_actors(actors)
            feature_vector.extend(actor_features)

            # 3. 导演特征
            director = metadata.get('director', '')
            director_features = self.encode_director(director)
            feature_vector.extend(director_features)

            # 4. 数值特征
            feature_vector.extend([
                metadata.get('release_year', 2000) / 2024,  # 标准化年份
                metadata.get('duration', 90) / 180,         # 标准化时长
                metadata.get('imdb_rating', 7.0) / 10,      # 标准化评分
                len(metadata.get('languages', [])) / 10     # 语言数量
            ])

            # 5. 文本特征（简化的TF-IDF）
            description = metadata.get('description', '')
            text_features = self.extract_text_features(description)
            feature_vector.extend(text_features)

            features[item_id] = np.array(feature_vector)

        self.content_features = features
        return features

    def train_collaborative_filtering(self, n_components=50):
        """训练协同过滤模型（矩阵分解）"""
        # 使用非负矩阵分解
        self.collaborative_model = NMF(
            n_components=n_components,
            init='random',
            random_state=42,
            max_iter=200
        )

        # 训练模型
        user_factors = self.collaborative_model.fit_transform(self.user_item_matrix)
        item_factors = self.collaborative_model.components_

        return user_factors, item_factors

    def train_content_based_model(self):
        """训练基于内容的推荐模型"""
        if not self.content_features:
            raise ValueError("需要先提取内容特征")

        # 构建内容特征矩阵
        item_ids = list(self.content_features.keys())
        feature_matrix = np.array([self.content_features[item_id] for item_id in item_ids])

        # 计算内容相似度矩阵
        content_similarity = cosine_similarity(feature_matrix)

        self.content_model = {
            'item_ids': item_ids,
            'similarity_matrix': content_similarity
        }

        return content_similarity

    def get_collaborative_recommendations(self, user_id, n_recommendations=10):
        """基于协同过滤的推荐"""
        if user_id not in self.user_item_matrix.index:
            return []  # 冷启动用户

        # 获取用户因子
        user_idx = list(self.user_item_matrix.index).index(user_id)
        user_vector = self.collaborative_model.transform(
            self.user_item_matrix.iloc[user_idx:user_idx+1]
        )[0]

        # 计算对所有物品的预测评分
        item_factors = self.collaborative_model.components_
        predicted_ratings = np.dot(user_vector, item_factors)

        # 获取用户已观看的物品
        watched_items = set(self.user_item_matrix.columns[
            self.user_item_matrix.iloc[user_idx] > 0
        ])

        # 推荐未观看的高分物品
        item_scores = []
        for i, item_id in enumerate(self.user_item_matrix.columns):
            if item_id not in watched_items:
                item_scores.append((item_id, predicted_ratings[i]))

        # 按预测评分排序
        item_scores.sort(key=lambda x: x[1], reverse=True)

        return [item_id for item_id, score in item_scores[:n_recommendations]]

    def get_content_recommendations(self, user_id, n_recommendations=10):
        """基于内容的推荐"""
        if user_id not in self.user_item_matrix.index:
            return []

        # 获取用户观看历史
        user_idx = list(self.user_item_matrix.index).index(user_id)
        user_ratings = self.user_item_matrix.iloc[user_idx]
        watched_items = user_ratings[user_ratings > 0]

        if len(watched_items) == 0:
            return []

        # 计算用户偏好向量（基于观看历史的加权平均）
        user_profile = np.zeros(len(list(self.content_features.values())[0]))
        total_weight = 0

        for item_id, rating in watched_items.items():
            if item_id in self.content_features:
                weight = rating
                user_profile += weight * self.content_features[item_id]
                total_weight += weight

        if total_weight > 0:
            user_profile /= total_weight

        # 计算与所有物品的相似度
        item_scores = []
        for item_id, features in self.content_features.items():
            if item_id not in watched_items.index:
                similarity = cosine_similarity([user_profile], [features])[0][0]
                item_scores.append((item_id, similarity))

        # 按相似度排序
        item_scores.sort(key=lambda x: x[1], reverse=True)

        return [item_id for item_id, score in item_scores[:n_recommendations]]

    def get_hybrid_recommendations(self, user_id, n_recommendations=10):
        """混合推荐算法"""
        # 获取协同过滤推荐
        collab_recs = self.get_collaborative_recommendations(user_id, n_recommendations * 2)

        # 获取基于内容的推荐
        content_recs = self.get_content_recommendations(user_id, n_recommendations * 2)

        # 混合策略：加权融合
        hybrid_scores = {}

        # 协同过滤得分
        for i, item_id in enumerate(collab_recs):
            score = (len(collab_recs) - i) / len(collab_recs)  # 位置得分
            hybrid_scores[item_id] = hybrid_scores.get(item_id, 0) + \
                                   self.hybrid_weights['collaborative'] * score

        # 内容推荐得分
        for i, item_id in enumerate(content_recs):
            score = (len(content_recs) - i) / len(content_recs)
            hybrid_scores[item_id] = hybrid_scores.get(item_id, 0) + \
                                   self.hybrid_weights['content'] * score

        # 按混合得分排序
        sorted_items = sorted(hybrid_scores.items(), key=lambda x: x[1], reverse=True)

        return [item_id for item_id, score in sorted_items[:n_recommendations]]

    def handle_cold_start(self, user_demographics, n_recommendations=10):
        """处理冷启动问题"""
        # 基于人口统计学特征的推荐
        age_group = user_demographics.get('age_group', 'adult')
        gender = user_demographics.get('gender', 'unknown')
        location = user_demographics.get('location', 'unknown')

        # 获取相似用户群体的热门内容
        popular_items = self.get_popular_items_by_demographics(
            age_group, gender, location
        )

        return popular_items[:n_recommendations]

    def encode_genres(self, genres):
        """编码类型特征"""
        all_genres = ['Action', 'Comedy', 'Drama', 'Horror', 'Romance',
                     'Sci-Fi', 'Documentary', 'Animation', 'Thriller', 'Crime']
        return [1 if genre in genres else 0 for genre in all_genres]

    def encode_actors(self, actors):
        """编码演员特征（简化版）"""
        # 这里应该基于演员的受欢迎程度
        top_actors = ['Actor1', 'Actor2', 'Actor3', 'Actor4', 'Actor5']
        return [1 if actor in actors else 0 for actor in top_actors]

    def encode_director(self, director):
        """编码导演特征（简化版）"""
        top_directors = ['Director1', 'Director2', 'Director3']
        return [1 if director == d else 0 for d in top_directors]

    def extract_text_features(self, text):
        """提取文本特征（简化的TF-IDF）"""
        # 这里应该使用真实的TF-IDF或词嵌入
        keywords = ['love', 'action', 'mystery', 'family', 'adventure']
        text_lower = text.lower()
        return [1 if keyword in text_lower else 0 for keyword in keywords]

    def get_popular_items_by_demographics(self, age_group, gender, location):
        """根据人口统计学特征获取热门内容"""
        # 这里应该基于真实的统计数据
        # 简化实现
        return ['popular_item_1', 'popular_item_2', 'popular_item_3']

# 使用示例
def netflix_recommendation_demo():
    netflix_system = NetflixRecommendationSystem()

    # 模拟观看数据
    viewing_data = pd.DataFrame([
        {'user_id': 'user1', 'item_id': 'movie1', 'rating': 4, 'watch_time': 120, 'completion_rate': 0.9, 'repeat_count': 0},
        {'user_id': 'user1', 'item_id': 'movie2', 'rating': 5, 'watch_time': 90, 'completion_rate': 1.0, 'repeat_count': 1},
        {'user_id': 'user2', 'item_id': 'movie1', 'rating': 3, 'watch_time': 60, 'completion_rate': 0.5, 'repeat_count': 0},
        {'user_id': 'user2', 'item_id': 'movie3', 'rating': 4, 'watch_time': 110, 'completion_rate': 0.8, 'repeat_count': 0},
    ])

    # 模拟内容元数据
    content_metadata = {
        'movie1': {
            'genres': ['Action', 'Thriller'],
            'actors': ['Actor1', 'Actor2'],
            'director': 'Director1',
            'release_year': 2020,
            'duration': 120,
            'imdb_rating': 7.5,
            'languages': ['English'],
            'description': 'An action-packed thriller with amazing stunts'
        },
        'movie2': {
            'genres': ['Comedy', 'Romance'],
            'actors': ['Actor3', 'Actor4'],
            'director': 'Director2',
            'release_year': 2019,
            'duration': 95,
            'imdb_rating': 8.0,
            'languages': ['English', 'Spanish'],
            'description': 'A romantic comedy about love and family'
        }
    }

    # 构建推荐系统
    netflix_system.build_user_item_matrix(viewing_data)
    netflix_system.extract_content_features(content_metadata)
    netflix_system.train_collaborative_filtering()
    netflix_system.train_content_based_model()

    # 获取推荐
    user_id = 'user1'
    recommendations = netflix_system.get_hybrid_recommendations(user_id, 5)

    print(f"=== Netflix推荐系统演示 ===")
    print(f"用户 {user_id} 的推荐结果: {recommendations}")

netflix_recommendation_demo()
```

### 题目32：Uber动态定价算法
**公司来源**: Uber
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计Uber的动态定价算法，考虑供需平衡、地理位置、时间因素等。

**推荐解答**:
```python
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import math

class UberDynamicPricing:
    def __init__(self):
        self.base_price_per_km = 1.5
        self.base_price_per_minute = 0.3
        self.surge_multiplier_max = 5.0
        self.surge_multiplier_min = 1.0

        # 历史数据用于预测
        self.historical_demand = {}
        self.historical_supply = {}

    def calculate_supply_demand_ratio(self, location, current_time):
        """计算供需比例"""
        # 1. 获取当前区域的司机数量
        available_drivers = self.get_available_drivers(location)

        # 2. 预测当前时段的需求
        predicted_demand = self.predict_demand(location, current_time)

        # 3. 计算供需比
        if predicted_demand == 0:
            return float('inf')  # 无需求时供给充足

        supply_demand_ratio = available_drivers / predicted_demand
        return supply_demand_ratio

    def predict_demand(self, location, current_time):
        """预测需求量"""
        # 基于历史数据和多种因素预测需求

        # 1. 时间因素
        hour = current_time.hour
        day_of_week = current_time.weekday()

        # 工作日vs周末的需求模式
        if day_of_week < 5:  # 工作日
            if 7 <= hour <= 9 or 17 <= hour <= 19:
                time_factor = 2.0  # 高峰期
            elif 22 <= hour or hour <= 6:
                time_factor = 0.3  # 深夜
            else:
                time_factor = 1.0  # 正常时段
        else:  # 周末
            if 10 <= hour <= 14 or 19 <= hour <= 23:
                time_factor = 1.5  # 周末高峰
            else:
                time_factor = 0.8

        # 2. 地理位置因素
        location_factor = self.get_location_demand_factor(location)

        # 3. 天气因素
        weather_factor = self.get_weather_factor(location, current_time)

        # 4. 特殊事件因素
        event_factor = self.get_event_factor(location, current_time)

        # 5. 基础需求量（基于历史数据）
        base_demand = self.get_historical_demand(location, hour, day_of_week)

        # 综合预测
        predicted_demand = base_demand * time_factor * location_factor * weather_factor * event_factor

        return max(predicted_demand, 1)  # 最小需求为1

    def get_available_drivers(self, location):
        """获取可用司机数量"""
        # 模拟获取指定区域的可用司机数量
        # 实际实现中会查询实时数据库

        # 基于位置的司机密度
        location_type = self.classify_location(location)

        base_drivers = {
            'downtown': 50,
            'residential': 20,
            'airport': 30,
            'suburb': 15,
            'industrial': 10
        }

        # 添加随机波动
        base_count = base_drivers.get(location_type, 20)
        actual_count = max(1, int(base_count * np.random.uniform(0.7, 1.3)))

        return actual_count

    def calculate_surge_multiplier(self, supply_demand_ratio):
        """计算动态定价倍数"""
        # 供需比越低，价格倍数越高

        if supply_demand_ratio >= 1.5:
            # 供给充足，无需涨价
            surge_multiplier = self.surge_multiplier_min
        elif supply_demand_ratio >= 1.0:
            # 供需平衡，轻微涨价
            surge_multiplier = 1.0 + (1.5 - supply_demand_ratio) * 0.4
        elif supply_demand_ratio >= 0.5:
            # 供不应求，中等涨价
            surge_multiplier = 1.2 + (1.0 - supply_demand_ratio) * 2.0
        else:
            # 严重供不应求，大幅涨价
            surge_multiplier = 2.2 + (0.5 - supply_demand_ratio) * 4.0

        # 限制在合理范围内
        surge_multiplier = max(self.surge_multiplier_min,
                             min(self.surge_multiplier_max, surge_multiplier))

        return surge_multiplier

    def calculate_trip_price(self, start_location, end_location, current_time):
        """计算行程价格"""
        # 1. 计算基础价格
        distance_km = self.calculate_distance(start_location, end_location)
        estimated_time_minutes = self.estimate_travel_time(start_location, end_location, current_time)

        base_price = (distance_km * self.base_price_per_km +
                     estimated_time_minutes * self.base_price_per_minute)

        # 2. 计算动态定价倍数
        supply_demand_ratio = self.calculate_supply_demand_ratio(start_location, current_time)
        surge_multiplier = self.calculate_surge_multiplier(supply_demand_ratio)

        # 3. 应用其他调整因素

        # 路线复杂度调整
        route_complexity = self.calculate_route_complexity(start_location, end_location)
        complexity_multiplier = 1.0 + route_complexity * 0.1

        # 安全性调整（深夜、偏僻地区）
        safety_multiplier = self.calculate_safety_multiplier(start_location, end_location, current_time)

        # 4. 计算最终价格
        final_price = base_price * surge_multiplier * complexity_multiplier * safety_multiplier

        # 5. 价格平滑处理（避免价格剧烈波动）
        smoothed_price = self.apply_price_smoothing(start_location, final_price, current_time)

        return {
            'base_price': base_price,
            'surge_multiplier': surge_multiplier,
            'final_price': smoothed_price,
            'supply_demand_ratio': supply_demand_ratio,
            'estimated_time': estimated_time_minutes,
            'distance': distance_km
        }

    def apply_price_smoothing(self, location, new_price, current_time):
        """价格平滑处理"""
        # 获取最近的价格历史
        recent_prices = self.get_recent_prices(location, current_time)

        if not recent_prices:
            return new_price

        # 计算平均价格
        avg_recent_price = np.mean(recent_prices)

        # 限制价格变化幅度（最多30%的变化）
        max_change_ratio = 0.3

        if new_price > avg_recent_price * (1 + max_change_ratio):
            smoothed_price = avg_recent_price * (1 + max_change_ratio)
        elif new_price < avg_recent_price * (1 - max_change_ratio):
            smoothed_price = avg_recent_price * (1 - max_change_ratio)
        else:
            smoothed_price = new_price

        return smoothed_price

    def optimize_driver_allocation(self, current_time):
        """优化司机分配"""
        # 预测各区域的需求
        locations = ['downtown', 'airport', 'residential', 'suburb']
        demand_predictions = {}
        supply_current = {}

        for location in locations:
            demand_predictions[location] = self.predict_demand(location, current_time)
            supply_current[location] = self.get_available_drivers(location)

        # 计算需要重新分配的司机
        reallocation_suggestions = []

        for from_location in locations:
            for to_location in locations:
                if from_location != to_location:
                    from_ratio = supply_current[from_location] / demand_predictions[from_location]
                    to_ratio = supply_current[to_location] / demand_predictions[to_location]

                    # 如果源地区供给过剩，目标地区供给不足
                    if from_ratio > 1.5 and to_ratio < 0.8:
                        drivers_to_move = min(
                            int(supply_current[from_location] * 0.2),  # 最多移动20%
                            int(demand_predictions[to_location] - supply_current[to_location])
                        )

                        if drivers_to_move > 0:
                            reallocation_suggestions.append({
                                'from': from_location,
                                'to': to_location,
                                'drivers': drivers_to_move,
                                'incentive': self.calculate_driver_incentive(from_location, to_location)
                            })

        return reallocation_suggestions

    def calculate_driver_incentive(self, from_location, to_location):
        """计算司机激励金额"""
        # 基于距离和需求差异计算激励
        distance = self.calculate_distance(from_location, to_location)
        base_incentive = distance * 0.5  # 每公里0.5元基础激励

        # 根据需求紧急程度调整
        urgency_multiplier = 1.5  # 可以根据实际供需情况调整

        return base_incentive * urgency_multiplier

    # 辅助方法
    def classify_location(self, location):
        """分类地理位置"""
        # 简化实现，实际中会使用地理信息系统
        location_types = ['downtown', 'residential', 'airport', 'suburb', 'industrial']
        return np.random.choice(location_types)

    def get_location_demand_factor(self, location):
        """获取位置需求因子"""
        factors = {
            'downtown': 1.5,
            'airport': 1.3,
            'residential': 1.0,
            'suburb': 0.8,
            'industrial': 0.6
        }
        location_type = self.classify_location(location)
        return factors.get(location_type, 1.0)

    def get_weather_factor(self, location, current_time):
        """获取天气因子"""
        # 简化实现，实际中会调用天气API
        weather_conditions = ['sunny', 'rainy', 'snowy', 'cloudy']
        weather = np.random.choice(weather_conditions)

        factors = {
            'sunny': 1.0,
            'cloudy': 1.1,
            'rainy': 1.4,
            'snowy': 1.8
        }

        return factors.get(weather, 1.0)

    def get_event_factor(self, location, current_time):
        """获取特殊事件因子"""
        # 检查是否有大型活动、节假日等
        # 简化实现
        return 1.0

    def get_historical_demand(self, location, hour, day_of_week):
        """获取历史需求数据"""
        # 简化实现，实际中会查询历史数据库
        base_demand = 20
        return base_demand * np.random.uniform(0.8, 1.2)

    def calculate_distance(self, start, end):
        """计算距离"""
        # 简化实现，实际中会使用地图API
        return np.random.uniform(2, 20)  # 2-20公里

    def estimate_travel_time(self, start, end, current_time):
        """估算行程时间"""
        distance = self.calculate_distance(start, end)

        # 根据时间段调整速度
        hour = current_time.hour
        if 7 <= hour <= 9 or 17 <= hour <= 19:
            speed_factor = 0.6  # 高峰期速度慢
        else:
            speed_factor = 1.0

        # 假设平均速度30公里/小时
        travel_time_hours = distance / (30 * speed_factor)
        return travel_time_hours * 60  # 转换为分钟

    def calculate_route_complexity(self, start, end):
        """计算路线复杂度"""
        # 简化实现，实际中会分析路线的复杂程度
        return np.random.uniform(0, 0.3)

    def calculate_safety_multiplier(self, start, end, current_time):
        """计算安全性调整倍数"""
        hour = current_time.hour

        # 深夜时段增加安全费用
        if 22 <= hour or hour <= 5:
            return 1.2
        else:
            return 1.0

    def get_recent_prices(self, location, current_time):
        """获取最近的价格历史"""
        # 简化实现，返回模拟的历史价格
        return [15.5, 16.2, 14.8, 15.9, 16.5]

# 使用示例
def uber_pricing_demo():
    uber_pricing = UberDynamicPricing()

    current_time = datetime.now()
    start_location = "downtown_area_1"
    end_location = "airport_terminal_2"

    # 计算行程价格
    pricing_result = uber_pricing.calculate_trip_price(
        start_location, end_location, current_time
    )

    print("=== Uber动态定价演示 ===")
    print(f"起点: {start_location}")
    print(f"终点: {end_location}")
    print(f"时间: {current_time.strftime('%Y-%m-%d %H:%M')}")
    print(f"距离: {pricing_result['distance']:.1f} 公里")
    print(f"预估时间: {pricing_result['estimated_time']:.0f} 分钟")
    print(f"基础价格: ¥{pricing_result['base_price']:.2f}")
    print(f"动态倍数: {pricing_result['surge_multiplier']:.2f}x")
    print(f"最终价格: ¥{pricing_result['final_price']:.2f}")
    print(f"供需比: {pricing_result['supply_demand_ratio']:.2f}")

    # 司机分配优化
    allocation_suggestions = uber_pricing.optimize_driver_allocation(current_time)

    if allocation_suggestions:
        print(f"\n司机分配建议:")
        for suggestion in allocation_suggestions:
            print(f"  从 {suggestion['from']} 到 {suggestion['to']}: "
                  f"{suggestion['drivers']} 名司机, "
                  f"激励: ¥{suggestion['incentive']:.2f}")

uber_pricing_demo()
```

### 题目33：Spotify音乐推荐算法
**公司来源**: Spotify
**难度**: ⭐⭐⭐⭐

**题目**: 设计Spotify的音乐推荐算法，考虑用户听歌历史、音乐特征、协同过滤等因素。

**推荐解答**:
```python
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from collections import defaultdict
import librosa  # 音频特征提取库

class SpotifyRecommendationSystem:
    def __init__(self):
        self.user_profiles = {}
        self.track_features = {}
        self.listening_history = defaultdict(list)
        self.collaborative_matrix = None

    def extract_audio_features(self, audio_file_path):
        """提取音频特征"""
        try:
            # 加载音频文件
            y, sr = librosa.load(audio_file_path, duration=30)  # 只分析前30秒

            # 1. 节拍特征
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)

            # 2. 音色特征 (MFCC)
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            mfcc_mean = np.mean(mfccs, axis=1)

            # 3. 频谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)
            spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr)

            # 4. 零交叉率 (反映音乐的节奏感)
            zero_crossing_rate = librosa.feature.zero_crossing_rate(y)

            # 5. 色度特征 (反映和声内容)
            chroma = librosa.feature.chroma_stft(y=y, sr=sr)
            chroma_mean = np.mean(chroma, axis=1)

            # 6. 能量特征
            rms = librosa.feature.rms(y=y)

            # 组合所有特征
            audio_features = np.concatenate([
                [tempo],
                mfcc_mean,
                [np.mean(spectral_centroids)],
                [np.mean(spectral_rolloff)],
                [np.mean(spectral_bandwidth)],
                [np.mean(zero_crossing_rate)],
                chroma_mean,
                [np.mean(rms)]
            ])

            return audio_features

        except Exception as e:
            print(f"音频特征提取失败: {e}")
            return np.zeros(30)  # 返回零向量作为默认值

    def extract_metadata_features(self, track_metadata):
        """提取元数据特征"""
        features = []

        # 1. 类型特征 (one-hot编码)
        genres = ['pop', 'rock', 'jazz', 'classical', 'electronic', 'hip-hop', 'country', 'folk']
        track_genre = track_metadata.get('genre', 'unknown')
        genre_features = [1 if genre == track_genre else 0 for genre in genres]
        features.extend(genre_features)

        # 2. 艺术家特征
        artist_popularity = track_metadata.get('artist_popularity', 50) / 100
        features.append(artist_popularity)

        # 3. 发行年份特征
        release_year = track_metadata.get('release_year', 2000)
        year_normalized = (release_year - 1950) / (2024 - 1950)
        features.append(year_normalized)

        # 4. 时长特征
        duration_ms = track_metadata.get('duration_ms', 180000)
        duration_normalized = min(duration_ms / 600000, 1.0)  # 标准化到10分钟
        features.append(duration_normalized)

        # 5. 流行度特征
        track_popularity = track_metadata.get('track_popularity', 50) / 100
        features.append(track_popularity)

        # 6. 能量和情感特征 (Spotify API提供)
        energy = track_metadata.get('energy', 0.5)
        valence = track_metadata.get('valence', 0.5)  # 音乐的积极程度
        danceability = track_metadata.get('danceability', 0.5)
        acousticness = track_metadata.get('acousticness', 0.5)
        instrumentalness = track_metadata.get('instrumentalness', 0.5)

        features.extend([energy, valence, danceability, acousticness, instrumentalness])

        return np.array(features)

    def build_user_profile(self, user_id, listening_history):
        """构建用户音乐偏好档案"""
        if not listening_history:
            return np.zeros(50)  # 默认特征维度

        # 收集用户听过的所有歌曲特征
        user_track_features = []
        play_counts = []

        for track_id, play_count in listening_history.items():
            if track_id in self.track_features:
                user_track_features.append(self.track_features[track_id])
                play_counts.append(play_count)

        if not user_track_features:
            return np.zeros(50)

        user_track_features = np.array(user_track_features)
        play_counts = np.array(play_counts)

        # 基于播放次数的加权平均
        weights = play_counts / np.sum(play_counts)
        user_profile = np.average(user_track_features, axis=0, weights=weights)

        return user_profile

    def analyze_listening_patterns(self, user_id, listening_history):
        """分析用户听歌模式"""
        patterns = {
            'preferred_times': defaultdict(int),
            'session_lengths': [],
            'skip_rate': 0,
            'repeat_rate': 0,
            'genre_distribution': defaultdict(int)
        }

        total_plays = 0
        total_skips = 0
        total_repeats = 0

        for session in listening_history:
            # 分析听歌时间偏好
            hour = session.get('timestamp', datetime.now()).hour
            patterns['preferred_times'][hour] += 1

            # 分析会话长度
            session_length = session.get('session_length_minutes', 30)
            patterns['session_lengths'].append(session_length)

            # 分析跳过率和重复率
            for track_play in session.get('tracks', []):
                total_plays += 1
                if track_play.get('skipped', False):
                    total_skips += 1
                if track_play.get('repeated', False):
                    total_repeats += 1

                # 分析类型偏好
                track_id = track_play.get('track_id')
                if track_id in self.track_features:
                    genre = self.get_track_genre(track_id)
                    patterns['genre_distribution'][genre] += 1

        if total_plays > 0:
            patterns['skip_rate'] = total_skips / total_plays
            patterns['repeat_rate'] = total_repeats / total_plays

        return patterns

    def collaborative_filtering_recommendations(self, user_id, n_recommendations=20):
        """基于协同过滤的推荐"""
        if user_id not in self.user_profiles:
            return []

        user_profile = self.user_profiles[user_id]

        # 找到相似用户
        similar_users = []
        for other_user_id, other_profile in self.user_profiles.items():
            if other_user_id != user_id:
                similarity = cosine_similarity([user_profile], [other_profile])[0][0]
                similar_users.append((other_user_id, similarity))

        # 按相似度排序
        similar_users.sort(key=lambda x: x[1], reverse=True)

        # 获取相似用户喜欢但当前用户没听过的歌曲
        user_listened = set(self.listening_history[user_id])
        recommendations = defaultdict(float)

        for similar_user_id, similarity in similar_users[:50]:  # 取前50个相似用户
            for track_id in self.listening_history[similar_user_id]:
                if track_id not in user_listened:
                    recommendations[track_id] += similarity

        # 按推荐分数排序
        sorted_recommendations = sorted(recommendations.items(),
                                      key=lambda x: x[1], reverse=True)

        return [track_id for track_id, score in sorted_recommendations[:n_recommendations]]

    def content_based_recommendations(self, user_id, n_recommendations=20):
        """基于内容的推荐"""
        if user_id not in self.user_profiles:
            return []

        user_profile = self.user_profiles[user_id]
        user_listened = set(self.listening_history[user_id])

        # 计算用户档案与所有歌曲的相似度
        track_similarities = []

        for track_id, track_features in self.track_features.items():
            if track_id not in user_listened:
                similarity = cosine_similarity([user_profile], [track_features])[0][0]
                track_similarities.append((track_id, similarity))

        # 按相似度排序
        track_similarities.sort(key=lambda x: x[1], reverse=True)

        return [track_id for track_id, similarity in track_similarities[:n_recommendations]]

    def context_aware_recommendations(self, user_id, context, n_recommendations=20):
        """基于上下文的推荐"""
        # 上下文包括：时间、地点、活动、情绪等

        current_hour = context.get('hour', 12)
        activity = context.get('activity', 'general')  # work, exercise, relax, party
        mood = context.get('mood', 'neutral')  # happy, sad, energetic, calm

        # 根据上下文调整推荐策略
        context_filters = {
            'work': {'energy': (0.3, 0.7), 'valence': (0.4, 0.8), 'instrumentalness': (0.3, 1.0)},
            'exercise': {'energy': (0.7, 1.0), 'danceability': (0.6, 1.0), 'tempo': (120, 180)},
            'relax': {'energy': (0.0, 0.5), 'valence': (0.3, 0.8), 'acousticness': (0.4, 1.0)},
            'party': {'energy': (0.6, 1.0), 'danceability': (0.7, 1.0), 'valence': (0.6, 1.0)}
        }

        # 获取基础推荐
        base_recommendations = self.content_based_recommendations(user_id, n_recommendations * 3)

        # 根据上下文过滤
        filtered_recommendations = []
        activity_filter = context_filters.get(activity, {})

        for track_id in base_recommendations:
            if self.matches_context_filter(track_id, activity_filter):
                filtered_recommendations.append(track_id)

                if len(filtered_recommendations) >= n_recommendations:
                    break

        return filtered_recommendations

    def generate_playlist(self, user_id, playlist_type='discover_weekly', length=30):
        """生成播放列表"""
        if playlist_type == 'discover_weekly':
            # 发现周刊：50%协同过滤 + 50%基于内容
            collab_recs = self.collaborative_filtering_recommendations(user_id, length // 2)
            content_recs = self.content_based_recommendations(user_id, length // 2)

            playlist = collab_recs + content_recs

        elif playlist_type == 'daily_mix':
            # 每日混合：基于用户最近听歌偏好
            recent_tracks = self.get_recent_tracks(user_id, days=7)
            similar_tracks = []

            for track_id in recent_tracks[:10]:  # 基于最近10首歌
                similar = self.find_similar_tracks(track_id, 3)
                similar_tracks.extend(similar)

            playlist = list(set(similar_tracks))[:length]

        elif playlist_type == 'release_radar':
            # 发行雷达：新发布的歌曲中符合用户偏好的
            new_releases = self.get_new_releases(days=7)
            user_profile = self.user_profiles.get(user_id, np.zeros(50))

            scored_releases = []
            for track_id in new_releases:
                if track_id in self.track_features:
                    similarity = cosine_similarity([user_profile],
                                                 [self.track_features[track_id]])[0][0]
                    scored_releases.append((track_id, similarity))

            scored_releases.sort(key=lambda x: x[1], reverse=True)
            playlist = [track_id for track_id, score in scored_releases[:length]]

        else:
            playlist = self.content_based_recommendations(user_id, length)

        # 确保播放列表的多样性
        diversified_playlist = self.diversify_playlist(playlist, user_id)

        return diversified_playlist[:length]

    def diversify_playlist(self, playlist, user_id):
        """增加播放列表的多样性"""
        if len(playlist) <= 1:
            return playlist

        diversified = [playlist[0]]  # 从第一首歌开始
        remaining = playlist[1:]

        while remaining and len(diversified) < len(playlist):
            # 选择与已选歌曲最不相似的歌曲
            best_candidate = None
            max_min_distance = -1

            for candidate in remaining:
                min_distance = float('inf')

                for selected in diversified:
                    if candidate in self.track_features and selected in self.track_features:
                        distance = 1 - cosine_similarity(
                            [self.track_features[candidate]],
                            [self.track_features[selected]]
                        )[0][0]
                        min_distance = min(min_distance, distance)

                if min_distance > max_min_distance:
                    max_min_distance = min_distance
                    best_candidate = candidate

            if best_candidate:
                diversified.append(best_candidate)
                remaining.remove(best_candidate)
            else:
                # 如果没找到合适的，随机选择一个
                diversified.append(remaining.pop(0))

        return diversified

    # 辅助方法
    def matches_context_filter(self, track_id, context_filter):
        """检查歌曲是否符合上下文过滤条件"""
        if track_id not in self.track_features:
            return True

        # 这里需要根据实际的特征结构来实现
        # 简化实现
        return True

    def get_track_genre(self, track_id):
        """获取歌曲类型"""
        # 简化实现
        genres = ['pop', 'rock', 'jazz', 'classical', 'electronic']
        return np.random.choice(genres)

    def get_recent_tracks(self, user_id, days=7):
        """获取用户最近听的歌曲"""
        # 简化实现
        return list(self.listening_history[user_id])[:20]

    def find_similar_tracks(self, track_id, n_similar=5):
        """找到相似的歌曲"""
        if track_id not in self.track_features:
            return []

        track_features = self.track_features[track_id]
        similarities = []

        for other_track_id, other_features in self.track_features.items():
            if other_track_id != track_id:
                similarity = cosine_similarity([track_features], [other_features])[0][0]
                similarities.append((other_track_id, similarity))

        similarities.sort(key=lambda x: x[1], reverse=True)
        return [track_id for track_id, sim in similarities[:n_similar]]

    def get_new_releases(self, days=7):
        """获取新发布的歌曲"""
        # 简化实现
        return ['new_track_1', 'new_track_2', 'new_track_3']

# 使用示例
def spotify_recommendation_demo():
    spotify_system = SpotifyRecommendationSystem()

    # 模拟用户数据
    user_id = 'user_123'

    # 模拟歌曲特征数据
    spotify_system.track_features = {
        'track_1': np.random.rand(50),
        'track_2': np.random.rand(50),
        'track_3': np.random.rand(50),
        'track_4': np.random.rand(50),
        'track_5': np.random.rand(50),
    }

    # 模拟用户听歌历史
    spotify_system.listening_history[user_id] = ['track_1', 'track_2', 'track_3']

    # 构建用户档案
    user_listening_history = {'track_1': 10, 'track_2': 5, 'track_3': 8}
    user_profile = spotify_system.build_user_profile(user_id, user_listening_history)
    spotify_system.user_profiles[user_id] = user_profile

    # 生成推荐
    context = {'hour': 14, 'activity': 'work', 'mood': 'focused'}

    print("=== Spotify音乐推荐演示 ===")

    # 基于内容的推荐
    content_recs = spotify_system.content_based_recommendations(user_id, 5)
    print(f"基于内容的推荐: {content_recs}")

    # 上下文感知推荐
    context_recs = spotify_system.context_aware_recommendations(user_id, context, 5)
    print(f"工作场景推荐: {context_recs}")

    # 生成播放列表
    discover_weekly = spotify_system.generate_playlist(user_id, 'discover_weekly', 10)
    print(f"发现周刊: {discover_weekly}")

spotify_recommendation_demo()
```

### 题目34：Airbnb搜索排序算法
**公司来源**: Airbnb
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计Airbnb的房源搜索排序算法，考虑价格、位置、评分、房东质量等因素。

**推荐解答**:
```python
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from geopy.distance import geodesic

class AirbnbSearchRanking:
    def __init__(self):
        self.ranking_weights = {
            'price_competitiveness': 0.25,
            'location_relevance': 0.20,
            'quality_score': 0.20,
            'host_quality': 0.15,
            'availability_match': 0.10,
            'user_preference': 0.10
        }

        self.price_elasticity = 0.8  # 价格敏感度

    def calculate_price_competitiveness(self, listing_price, search_filters, market_data):
        """计算价格竞争力"""
        # 1. 获取同类房源的价格分布
        similar_listings_prices = self.get_similar_listings_prices(
            listing_price, search_filters, market_data
        )

        if not similar_listings_prices:
            return 0.5  # 默认中等竞争力

        # 2. 计算价格百分位
        price_percentile = np.percentile(similar_listings_prices,
                                       [p for p in similar_listings_prices if p <= listing_price])
        price_rank = len(price_percentile) / len(similar_listings_prices)

        # 3. 价格竞争力得分（价格越低，竞争力越强）
        price_competitiveness = 1 - price_rank

        # 4. 考虑用户价格偏好
        user_budget = search_filters.get('max_price', float('inf'))
        if listing_price <= user_budget:
            budget_fit = 1.0
        else:
            # 超出预算的惩罚
            budget_fit = max(0, 1 - (listing_price - user_budget) / user_budget)

        # 5. 综合价格得分
        final_score = 0.7 * price_competitiveness + 0.3 * budget_fit

        return final_score

    def calculate_location_relevance(self, listing_location, search_location, search_filters):
        """计算位置相关性"""
        # 1. 地理距离得分
        distance_km = geodesic(listing_location, search_location).kilometers

        # 距离得分（指数衰减）
        distance_score = np.exp(-distance_km / 5)  # 5公里为衰减参数

        # 2. 交通便利性得分
        transport_score = self.calculate_transport_accessibility(
            listing_location, search_filters
        )

        # 3. 周边设施得分
        amenity_score = self.calculate_nearby_amenities(
            listing_location, search_filters
        )

        # 4. 安全性得分
        safety_score = self.get_area_safety_score(listing_location)

        # 5. 综合位置得分
        location_score = (
            0.4 * distance_score +
            0.25 * transport_score +
            0.25 * amenity_score +
            0.1 * safety_score
        )

        return location_score

    def calculate_quality_score(self, listing_data):
        """计算房源质量得分"""
        # 1. 评分得分
        avg_rating = listing_data.get('average_rating', 0)
        review_count = listing_data.get('review_count', 0)

        # 考虑评论数量的置信度调整
        confidence_factor = min(review_count / 50, 1.0)  # 50个评论达到满置信度
        rating_score = (avg_rating / 5.0) * confidence_factor

        # 2. 房源设施得分
        amenities = listing_data.get('amenities', [])
        essential_amenities = ['wifi', 'kitchen', 'heating', 'air_conditioning']
        luxury_amenities = ['pool', 'gym', 'parking', 'balcony']

        essential_score = sum(1 for amenity in essential_amenities if amenity in amenities) / len(essential_amenities)
        luxury_score = sum(1 for amenity in luxury_amenities if amenity in amenities) / len(luxury_amenities)

        amenity_score = 0.7 * essential_score + 0.3 * luxury_score

        # 3. 照片质量得分
        photo_count = listing_data.get('photo_count', 0)
        photo_score = min(photo_count / 20, 1.0)  # 20张照片为满分

        # 4. 描述完整性得分
        description_length = len(listing_data.get('description', ''))
        description_score = min(description_length / 500, 1.0)  # 500字符为满分

        # 5. 即时预订可用性
        instant_book = listing_data.get('instant_book', False)
        instant_book_score = 1.0 if instant_book else 0.8

        # 6. 综合质量得分
        quality_score = (
            0.4 * rating_score +
            0.25 * amenity_score +
            0.15 * photo_score +
            0.1 * description_score +
            0.1 * instant_book_score
        )

        return quality_score

    def calculate_host_quality(self, host_data):
        """计算房东质量得分"""
        # 1. 房东评分
        host_rating = host_data.get('host_rating', 0) / 5.0

        # 2. 响应率和响应时间
        response_rate = host_data.get('response_rate', 0) / 100.0
        response_time = host_data.get('response_time_hours', 24)
        response_time_score = max(0, 1 - response_time / 24)  # 24小时内响应为满分

        # 3. 房东经验
        host_since = host_data.get('host_since', datetime.now())
        years_hosting = (datetime.now() - host_since).days / 365
        experience_score = min(years_hosting / 5, 1.0)  # 5年经验为满分

        # 4. 超级房东状态
        is_superhost = host_data.get('is_superhost', False)
        superhost_score = 1.0 if is_superhost else 0.8

        # 5. 房源数量（多房源房东通常更专业）
        listing_count = host_data.get('listing_count', 1)
        portfolio_score = min(listing_count / 10, 1.0)  # 10个房源为满分

        # 6. 取消率（越低越好）
        cancellation_rate = host_data.get('cancellation_rate', 0)
        cancellation_score = max(0, 1 - cancellation_rate * 10)

        # 7. 综合房东得分
        host_score = (
            0.25 * host_rating +
            0.2 * response_rate +
            0.15 * response_time_score +
            0.15 * experience_score +
            0.1 * superhost_score +
            0.1 * portfolio_score +
            0.05 * cancellation_score
        )

        return host_score

    def calculate_availability_match(self, listing_data, search_filters):
        """计算可用性匹配度"""
        check_in = search_filters.get('check_in')
        check_out = search_filters.get('check_out')
        guest_count = search_filters.get('guests', 1)

        # 1. 日期可用性
        available_dates = listing_data.get('available_dates', [])
        requested_dates = self.generate_date_range(check_in, check_out)

        if all(date in available_dates for date in requested_dates):
            date_availability = 1.0
        else:
            # 部分可用的惩罚
            available_count = sum(1 for date in requested_dates if date in available_dates)
            date_availability = available_count / len(requested_dates)

        # 2. 容量匹配
        max_guests = listing_data.get('max_guests', 1)
        if guest_count <= max_guests:
            capacity_match = 1.0
        else:
            capacity_match = 0.0  # 超出容量直接排除

        # 3. 最少住宿天数要求
        min_nights = listing_data.get('minimum_nights', 1)
        stay_duration = (check_out - check_in).days

        if stay_duration >= min_nights:
            duration_match = 1.0
        else:
            duration_match = 0.0  # 不满足最少天数要求

        # 4. 综合可用性得分
        availability_score = date_availability * capacity_match * duration_match

        return availability_score

    def calculate_user_preference_match(self, listing_data, user_profile, search_history):
        """计算用户偏好匹配度"""
        # 1. 房型偏好
        property_type = listing_data.get('property_type', '')
        preferred_types = user_profile.get('preferred_property_types', [])

        if property_type in preferred_types:
            type_match = 1.0
        else:
            type_match = 0.7  # 非偏好类型的基础分

        # 2. 价格偏好
        listing_price = listing_data.get('price_per_night', 0)
        user_avg_price = user_profile.get('average_booking_price', listing_price)

        price_ratio = listing_price / user_avg_price if user_avg_price > 0 else 1
        if 0.8 <= price_ratio <= 1.2:
            price_preference = 1.0
        else:
            price_preference = max(0.3, 1 - abs(price_ratio - 1))

        # 3. 设施偏好
        listing_amenities = set(listing_data.get('amenities', []))
        preferred_amenities = set(user_profile.get('preferred_amenities', []))

        if preferred_amenities:
            amenity_match = len(listing_amenities & preferred_amenities) / len(preferred_amenities)
        else:
            amenity_match = 1.0

        # 4. 地理偏好
        listing_neighborhood = listing_data.get('neighborhood', '')
        preferred_neighborhoods = user_profile.get('preferred_neighborhoods', [])

        if listing_neighborhood in preferred_neighborhoods:
            location_preference = 1.0
        else:
            location_preference = 0.8

        # 5. 基于历史行为的相似性
        similarity_score = self.calculate_listing_similarity_to_history(
            listing_data, search_history
        )

        # 6. 综合偏好得分
        preference_score = (
            0.2 * type_match +
            0.25 * price_preference +
            0.25 * amenity_match +
            0.15 * location_preference +
            0.15 * similarity_score
        )

        return preference_score

    def rank_listings(self, listings, search_filters, user_profile, search_history, market_data):
        """对房源进行排序"""
        ranked_listings = []

        for listing in listings:
            # 计算各项得分
            price_score = self.calculate_price_competitiveness(
                listing['price_per_night'], search_filters, market_data
            )

            location_score = self.calculate_location_relevance(
                listing['location'], search_filters['location'], search_filters
            )

            quality_score = self.calculate_quality_score(listing)

            host_score = self.calculate_host_quality(listing['host_data'])

            availability_score = self.calculate_availability_match(listing, search_filters)

            preference_score = self.calculate_user_preference_match(
                listing, user_profile, search_history
            )

            # 如果不满足基本可用性要求，直接排除
            if availability_score == 0:
                continue

            # 计算综合得分
            final_score = (
                self.ranking_weights['price_competitiveness'] * price_score +
                self.ranking_weights['location_relevance'] * location_score +
                self.ranking_weights['quality_score'] * quality_score +
                self.ranking_weights['host_quality'] * host_score +
                self.ranking_weights['availability_match'] * availability_score +
                self.ranking_weights['user_preference'] * preference_score
            )

            # 添加随机因子以增加多样性
            diversity_factor = np.random.uniform(0.95, 1.05)
            final_score *= diversity_factor

            listing_with_score = listing.copy()
            listing_with_score.update({
                'ranking_score': final_score,
                'price_score': price_score,
                'location_score': location_score,
                'quality_score': quality_score,
                'host_score': host_score,
                'availability_score': availability_score,
                'preference_score': preference_score
            })

            ranked_listings.append(listing_with_score)

        # 按综合得分排序
        ranked_listings.sort(key=lambda x: x['ranking_score'], reverse=True)

        return ranked_listings

    # 辅助方法
    def get_similar_listings_prices(self, listing_price, search_filters, market_data):
        """获取相似房源的价格"""
        # 简化实现
        base_price = listing_price
        similar_prices = [
            base_price * np.random.uniform(0.8, 1.2) for _ in range(20)
        ]
        return similar_prices

    def calculate_transport_accessibility(self, location, search_filters):
        """计算交通便利性"""
        # 简化实现：基于到主要交通枢纽的距离
        return np.random.uniform(0.6, 1.0)

    def calculate_nearby_amenities(self, location, search_filters):
        """计算周边设施得分"""
        # 简化实现：基于周边餐厅、商店、景点数量
        return np.random.uniform(0.5, 1.0)

    def get_area_safety_score(self, location):
        """获取区域安全得分"""
        # 简化实现：基于犯罪率数据
        return np.random.uniform(0.7, 1.0)

    def generate_date_range(self, start_date, end_date):
        """生成日期范围"""
        dates = []
        current_date = start_date
        while current_date < end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates

    def calculate_listing_similarity_to_history(self, listing, search_history):
        """计算房源与历史记录的相似性"""
        # 简化实现
        return np.random.uniform(0.6, 1.0)

# 使用示例
def airbnb_search_demo():
    airbnb_ranking = AirbnbSearchRanking()

    # 模拟搜索参数
    search_filters = {
        'location': (37.7749, -122.4194),  # 旧金山
        'check_in': datetime(2024, 6, 1),
        'check_out': datetime(2024, 6, 5),
        'guests': 2,
        'max_price': 200
    }

    # 模拟用户档案
    user_profile = {
        'preferred_property_types': ['apartment', 'house'],
        'average_booking_price': 150,
        'preferred_amenities': ['wifi', 'kitchen', 'parking'],
        'preferred_neighborhoods': ['Mission', 'Castro']
    }

    # 模拟房源数据
    listings = [
        {
            'id': 'listing_1',
            'price_per_night': 120,
            'location': (37.7849, -122.4094),
            'property_type': 'apartment',
            'average_rating': 4.5,
            'review_count': 89,
            'amenities': ['wifi', 'kitchen', 'heating'],
            'photo_count': 15,
            'description': 'Beautiful apartment in the heart of the city',
            'instant_book': True,
            'max_guests': 4,
            'minimum_nights': 2,
            'available_dates': [datetime(2024, 6, 1), datetime(2024, 6, 2),
                              datetime(2024, 6, 3), datetime(2024, 6, 4)],
            'neighborhood': 'Mission',
            'host_data': {
                'host_rating': 4.8,
                'response_rate': 95,
                'response_time_hours': 2,
                'host_since': datetime(2020, 1, 1),
                'is_superhost': True,
                'listing_count': 3,
                'cancellation_rate': 0.01
            }
        },
        {
            'id': 'listing_2',
            'price_per_night': 180,
            'location': (37.7649, -122.4294),
            'property_type': 'house',
            'average_rating': 4.2,
            'review_count': 45,
            'amenities': ['wifi', 'kitchen', 'parking', 'pool'],
            'photo_count': 25,
            'description': 'Spacious house with garden and pool',
            'instant_book': False,
            'max_guests': 6,
            'minimum_nights': 3,
            'available_dates': [datetime(2024, 6, 1), datetime(2024, 6, 2),
                              datetime(2024, 6, 3), datetime(2024, 6, 4)],
            'neighborhood': 'Castro',
            'host_data': {
                'host_rating': 4.6,
                'response_rate': 88,
                'response_time_hours': 6,
                'host_since': datetime(2019, 5, 1),
                'is_superhost': False,
                'listing_count': 1,
                'cancellation_rate': 0.02
            }
        }
    ]

    # 模拟市场数据和搜索历史
    market_data = {}
    search_history = []

    # 执行排序
    ranked_listings = airbnb_ranking.rank_listings(
        listings, search_filters, user_profile, search_history, market_data
    )

    print("=== Airbnb搜索排序演示 ===")
    for i, listing in enumerate(ranked_listings):
        print(f"\n排名 {i+1}: {listing['id']}")
        print(f"  价格: ${listing['price_per_night']}/晚")
        print(f"  综合得分: {listing['ranking_score']:.3f}")
        print(f"  详细得分:")
        print(f"    价格竞争力: {listing['price_score']:.3f}")
        print(f"    位置相关性: {listing['location_score']:.3f}")
        print(f"    房源质量: {listing['quality_score']:.3f}")
        print(f"    房东质量: {listing['host_score']:.3f}")
        print(f"    用户偏好: {listing['preference_score']:.3f}")

airbnb_search_demo()
```

### 题目35：LinkedIn职位推荐算法
**公司来源**: LinkedIn
**难度**: ⭐⭐⭐⭐

**题目**: 设计LinkedIn的职位推荐算法，考虑用户技能、经验、职业发展轨迹等因素。

**推荐解答**:
```python
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from datetime import datetime, timedelta

class LinkedInJobRecommendation:
    def __init__(self):
        self.skill_embeddings = {}
        self.job_embeddings = {}
        self.user_profiles = {}
        self.job_market_trends = {}

    def extract_user_features(self, user_profile):
        """提取用户特征"""
        features = {}

        # 1. 技能特征
        skills = user_profile.get('skills', [])
        skill_levels = user_profile.get('skill_levels', {})

        # 技能向量化
        skill_vector = self.vectorize_skills(skills, skill_levels)
        features['skills'] = skill_vector

        # 2. 经验特征
        work_experience = user_profile.get('work_experience', [])
        experience_features = self.extract_experience_features(work_experience)
        features.update(experience_features)

        # 3. 教育背景特征
        education = user_profile.get('education', [])
        education_features = self.extract_education_features(education)
        features.update(education_features)

        # 4. 职业偏好特征
        preferences = user_profile.get('job_preferences', {})
        preference_features = self.extract_preference_features(preferences)
        features.update(preference_features)

        # 5. 活跃度特征
        activity_features = self.extract_activity_features(user_profile)
        features.update(activity_features)

        return features

    def vectorize_skills(self, skills, skill_levels):
        """技能向量化"""
        # 预定义技能分类
        skill_categories = {
            'programming': ['python', 'java', 'javascript', 'c++', 'sql'],
            'data_science': ['machine learning', 'data analysis', 'statistics', 'deep learning'],
            'management': ['project management', 'team leadership', 'strategic planning'],
            'design': ['ui/ux', 'graphic design', 'product design'],
            'marketing': ['digital marketing', 'seo', 'content marketing', 'social media']
        }

        skill_vector = {}

        for category, category_skills in skill_categories.items():
            category_score = 0
            category_count = 0

            for skill in skills:
                if skill.lower() in category_skills:
                    level = skill_levels.get(skill, 3)  # 默认中等水平
                    category_score += level
                    category_count += 1

            if category_count > 0:
                skill_vector[f'{category}_avg_level'] = category_score / category_count
                skill_vector[f'{category}_skill_count'] = category_count
            else:
                skill_vector[f'{category}_avg_level'] = 0
                skill_vector[f'{category}_skill_count'] = 0

        return skill_vector

    def extract_experience_features(self, work_experience):
        """提取工作经验特征"""
        features = {}

        if not work_experience:
            return {'total_experience_years': 0, 'current_level': 'entry', 'industry_experience': {}}

        # 计算总工作年限
        total_years = 0
        current_position = None
        industry_years = {}

        for exp in work_experience:
            start_date = exp.get('start_date', datetime.now())
            end_date = exp.get('end_date', datetime.now())

            years = (end_date - start_date).days / 365
            total_years += years

            # 行业经验
            industry = exp.get('industry', 'unknown')
            industry_years[industry] = industry_years.get(industry, 0) + years

            # 当前职位
            if exp.get('is_current', False):
                current_position = exp

        features['total_experience_years'] = total_years

        # 职业级别判断
        if total_years < 2:
            features['current_level'] = 'entry'
        elif total_years < 5:
            features['current_level'] = 'junior'
        elif total_years < 10:
            features['current_level'] = 'mid'
        else:
            features['current_level'] = 'senior'

        # 主要行业经验
        if industry_years:
            primary_industry = max(industry_years, key=industry_years.get)
            features['primary_industry'] = primary_industry
            features['primary_industry_years'] = industry_years[primary_industry]

        # 职业发展轨迹
        features['career_progression'] = self.analyze_career_progression(work_experience)

        return features

    def analyze_career_progression(self, work_experience):
        """分析职业发展轨迹"""
        if len(work_experience) < 2:
            return 'insufficient_data'

        # 按时间排序
        sorted_exp = sorted(work_experience, key=lambda x: x.get('start_date', datetime.now()))

        # 分析职位级别变化
        level_progression = []
        for exp in sorted_exp:
            title = exp.get('title', '').lower()
            if any(word in title for word in ['intern', 'trainee']):
                level_progression.append(1)
            elif any(word in title for word in ['junior', 'associate', 'analyst']):
                level_progression.append(2)
            elif any(word in title for word in ['senior', 'lead', 'principal']):
                level_progression.append(3)
            elif any(word in title for word in ['manager', 'director']):
                level_progression.append(4)
            elif any(word in title for word in ['vp', 'ceo', 'cto', 'cfo']):
                level_progression.append(5)
            else:
                level_progression.append(2)  # 默认中级

        # 判断发展趋势
        if len(level_progression) >= 2:
            if level_progression[-1] > level_progression[0]:
                return 'ascending'
            elif level_progression[-1] < level_progression[0]:
                return 'descending'
            else:
                return 'stable'

        return 'stable'

    def extract_job_features(self, job_posting):
        """提取职位特征"""
        features = {}

        # 1. 技能要求
        required_skills = job_posting.get('required_skills', [])
        preferred_skills = job_posting.get('preferred_skills', [])

        skill_requirements = self.vectorize_job_skills(required_skills, preferred_skills)
        features.update(skill_requirements)

        # 2. 经验要求
        min_experience = job_posting.get('min_experience_years', 0)
        max_experience = job_posting.get('max_experience_years', 20)
        features['experience_range'] = (min_experience, max_experience)

        # 3. 教育要求
        education_level = job_posting.get('education_requirement', 'bachelor')
        features['education_requirement'] = education_level

        # 4. 薪资范围
        salary_min = job_posting.get('salary_min', 0)
        salary_max = job_posting.get('salary_max', 200000)
        features['salary_range'] = (salary_min, salary_max)

        # 5. 公司特征
        company_size = job_posting.get('company_size', 'medium')
        company_industry = job_posting.get('company_industry', 'technology')
        features['company_size'] = company_size
        features['company_industry'] = company_industry

        # 6. 职位级别
        job_level = self.infer_job_level(job_posting.get('title', ''))
        features['job_level'] = job_level

        # 7. 工作类型
        job_type = job_posting.get('job_type', 'full_time')
        remote_option = job_posting.get('remote_option', False)
        features['job_type'] = job_type
        features['remote_option'] = remote_option

        return features

    def calculate_skill_match_score(self, user_skills, job_requirements):
        """计算技能匹配度"""
        required_skills = job_requirements.get('required_skills', [])
        preferred_skills = job_requirements.get('preferred_skills', [])
        user_skill_set = set(skill.lower() for skill in user_skills.get('skills', []))

        # 必需技能匹配
        required_skill_set = set(skill.lower() for skill in required_skills)
        required_match = len(user_skill_set & required_skill_set) / max(len(required_skill_set), 1)

        # 优选技能匹配
        preferred_skill_set = set(skill.lower() for skill in preferred_skills)
        preferred_match = len(user_skill_set & preferred_skill_set) / max(len(preferred_skill_set), 1)

        # 综合技能匹配度
        skill_match_score = 0.7 * required_match + 0.3 * preferred_match

        return skill_match_score

    def calculate_experience_match_score(self, user_experience, job_requirements):
        """计算经验匹配度"""
        user_years = user_experience.get('total_experience_years', 0)
        required_range = job_requirements.get('experience_range', (0, 20))

        min_required, max_required = required_range

        if min_required <= user_years <= max_required:
            return 1.0
        elif user_years < min_required:
            # 经验不足的惩罚
            gap = min_required - user_years
            return max(0, 1 - gap / 5)  # 每差1年扣0.2分
        else:
            # 经验过多的轻微惩罚
            excess = user_years - max_required
            return max(0.7, 1 - excess / 10)  # 轻微惩罚

    def calculate_career_growth_potential(self, user_profile, job_posting):
        """计算职业发展潜力"""
        current_level = user_profile.get('current_level', 'entry')
        job_level = job_posting.get('job_level', 'mid')

        level_mapping = {'entry': 1, 'junior': 2, 'mid': 3, 'senior': 4, 'executive': 5}

        current_level_num = level_mapping.get(current_level, 2)
        job_level_num = level_mapping.get(job_level, 3)

        # 理想的职业发展是向上一级或同级
        if job_level_num == current_level_num + 1:
            return 1.0  # 完美的下一步
        elif job_level_num == current_level_num:
            return 0.8  # 平级跳槽
        elif job_level_num == current_level_num + 2:
            return 0.6  # 跨级晋升，有挑战
        elif job_level_num < current_level_num:
            return 0.3  # 降级，通常不理想
        else:
            return 0.2  # 跨度太大

    def calculate_salary_attractiveness(self, user_profile, job_posting):
        """计算薪资吸引力"""
        current_salary = user_profile.get('current_salary', 0)
        job_salary_range = job_posting.get('salary_range', (0, 200000))

        if current_salary == 0:
            return 0.5  # 无法比较

        job_salary_mid = (job_salary_range[0] + job_salary_range[1]) / 2

        salary_increase_ratio = job_salary_mid / current_salary

        if salary_increase_ratio >= 1.2:
            return 1.0  # 20%以上涨幅很有吸引力
        elif salary_increase_ratio >= 1.1:
            return 0.8  # 10-20%涨幅有吸引力
        elif salary_increase_ratio >= 1.0:
            return 0.6  # 持平或小幅上涨
        else:
            return 0.2  # 降薪通常不吸引人

    def calculate_company_preference_score(self, user_profile, job_posting):
        """计算公司偏好得分"""
        # 公司规模偏好
        preferred_company_size = user_profile.get('preferred_company_size', [])
        job_company_size = job_posting.get('company_size', 'medium')

        if not preferred_company_size or job_company_size in preferred_company_size:
            size_match = 1.0
        else:
            size_match = 0.7

        # 行业偏好
        user_industry = user_profile.get('primary_industry', '')
        job_industry = job_posting.get('company_industry', '')

        if user_industry == job_industry:
            industry_match = 1.0
        else:
            industry_match = 0.8  # 跨行业也可以考虑

        # 工作类型偏好
        preferred_remote = user_profile.get('prefers_remote', False)
        job_remote_option = job_posting.get('remote_option', False)

        if preferred_remote == job_remote_option:
            remote_match = 1.0
        elif preferred_remote and not job_remote_option:
            remote_match = 0.5  # 想要远程但职位不支持
        else:
            remote_match = 0.9  # 不要求远程但职位支持

        company_score = 0.4 * size_match + 0.4 * industry_match + 0.2 * remote_match

        return company_score

    def recommend_jobs(self, user_id, job_postings, n_recommendations=10):
        """为用户推荐职位"""
        if user_id not in self.user_profiles:
            return []

        user_profile = self.user_profiles[user_id]
        user_features = self.extract_user_features(user_profile)

        job_scores = []

        for job in job_postings:
            job_features = self.extract_job_features(job)

            # 计算各项匹配度
            skill_score = self.calculate_skill_match_score(user_features, job_features)
            experience_score = self.calculate_experience_match_score(user_features, job_features)
            growth_score = self.calculate_career_growth_potential(user_features, job_features)
            salary_score = self.calculate_salary_attractiveness(user_profile, job_features)
            company_score = self.calculate_company_preference_score(user_profile, job_features)

            # 综合评分
            final_score = (
                0.3 * skill_score +
                0.25 * experience_score +
                0.2 * growth_score +
                0.15 * salary_score +
                0.1 * company_score
            )

            job_with_score = job.copy()
            job_with_score.update({
                'recommendation_score': final_score,
                'skill_match': skill_score,
                'experience_match': experience_score,
                'growth_potential': growth_score,
                'salary_attractiveness': salary_score,
                'company_preference': company_score
            })

            job_scores.append(job_with_score)

        # 按得分排序
        job_scores.sort(key=lambda x: x['recommendation_score'], reverse=True)

        return job_scores[:n_recommendations]

    # 辅助方法
    def vectorize_job_skills(self, required_skills, preferred_skills):
        """职位技能向量化"""
        all_skills = required_skills + preferred_skills
        return {'required_skills': required_skills, 'preferred_skills': preferred_skills}

    def infer_job_level(self, job_title):
        """推断职位级别"""
        title_lower = job_title.lower()

        if any(word in title_lower for word in ['intern', 'trainee', 'entry']):
            return 'entry'
        elif any(word in title_lower for word in ['junior', 'associate', 'analyst']):
            return 'junior'
        elif any(word in title_lower for word in ['senior', 'lead', 'principal']):
            return 'senior'
        elif any(word in title_lower for word in ['manager', 'director']):
            return 'executive'
        else:
            return 'mid'

    def extract_education_features(self, education):
        """提取教育特征"""
        if not education:
            return {'highest_degree': 'high_school'}

        degree_levels = {'high_school': 1, 'associate': 2, 'bachelor': 3, 'master': 4, 'phd': 5}

        highest_level = 0
        for edu in education:
            degree = edu.get('degree', 'bachelor').lower()
            level = degree_levels.get(degree, 3)
            highest_level = max(highest_level, level)

        degree_names = {1: 'high_school', 2: 'associate', 3: 'bachelor', 4: 'master', 5: 'phd'}

        return {'highest_degree': degree_names[highest_level]}

    def extract_preference_features(self, preferences):
        """提取偏好特征"""
        return {
            'preferred_company_size': preferences.get('company_size', []),
            'prefers_remote': preferences.get('remote_work', False),
            'preferred_salary_range': preferences.get('salary_range', (0, 200000))
        }

    def extract_activity_features(self, user_profile):
        """提取活跃度特征"""
        return {
            'profile_completeness': user_profile.get('profile_completeness', 0.5),
            'last_active': user_profile.get('last_active', datetime.now()),
            'job_search_activity': user_profile.get('job_search_activity', 'passive')
        }

# 使用示例
def linkedin_job_recommendation_demo():
    linkedin_system = LinkedInJobRecommendation()

    # 模拟用户档案
    user_profile = {
        'skills': ['Python', 'Machine Learning', 'Data Analysis', 'SQL'],
        'skill_levels': {'Python': 4, 'Machine Learning': 3, 'Data Analysis': 4, 'SQL': 3},
        'work_experience': [
            {
                'title': 'Data Analyst',
                'company': 'Tech Corp',
                'start_date': datetime(2020, 1, 1),
                'end_date': datetime(2022, 12, 31),
                'industry': 'technology',
                'is_current': False
            },
            {
                'title': 'Senior Data Scientist',
                'company': 'AI Startup',
                'start_date': datetime(2023, 1, 1),
                'end_date': datetime.now(),
                'industry': 'technology',
                'is_current': True
            }
        ],
        'education': [
            {'degree': 'bachelor', 'field': 'Computer Science'},
            {'degree': 'master', 'field': 'Data Science'}
        ],
        'current_salary': 120000,
        'job_preferences': {
            'company_size': ['medium', 'large'],
            'remote_work': True,
            'salary_range': (130000, 180000)
        }
    }

    linkedin_system.user_profiles['user_123'] = user_profile

    # 模拟职位数据
    job_postings = [
        {
            'id': 'job_1',
            'title': 'Senior Machine Learning Engineer',
            'company': 'Big Tech Co',
            'required_skills': ['Python', 'Machine Learning', 'Deep Learning'],
            'preferred_skills': ['TensorFlow', 'AWS', 'Docker'],
            'min_experience_years': 3,
            'max_experience_years': 7,
            'education_requirement': 'bachelor',
            'salary_min': 140000,
            'salary_max': 180000,
            'company_size': 'large',
            'company_industry': 'technology',
            'job_type': 'full_time',
            'remote_option': True
        },
        {
            'id': 'job_2',
            'title': 'Data Science Manager',
            'company': 'Finance Corp',
            'required_skills': ['Python', 'Data Analysis', 'Team Leadership'],
            'preferred_skills': ['SQL', 'Machine Learning', 'Project Management'],
            'min_experience_years': 5,
            'max_experience_years': 10,
            'education_requirement': 'master',
            'salary_min': 160000,
            'salary_max': 200000,
            'company_size': 'large',
            'company_industry': 'finance',
            'job_type': 'full_time',
            'remote_option': False
        }
    ]

    # 生成推荐
    recommendations = linkedin_system.recommend_jobs('user_123', job_postings, 5)

    print("=== LinkedIn职位推荐演示 ===")
    for i, job in enumerate(recommendations):
        print(f"\n推荐 {i+1}: {job['title']} @ {job['company']}")
        print(f"  综合得分: {job['recommendation_score']:.3f}")
        print(f"  技能匹配: {job['skill_match']:.3f}")
        print(f"  经验匹配: {job['experience_match']:.3f}")
        print(f"  发展潜力: {job['growth_potential']:.3f}")
        print(f"  薪资吸引力: {job['salary_attractiveness']:.3f}")
        print(f"  公司偏好: {job['company_preference']:.3f}")
        print(f"  薪资范围: ${job['salary_min']:,} - ${job['salary_max']:,}")

linkedin_job_recommendation_demo()
```

### 题目36：Pinterest视觉搜索算法
**公司来源**: Pinterest
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计Pinterest的视觉搜索算法，实现以图搜图功能，考虑图像特征提取、相似度计算等。

**推荐解答**:
```python
import numpy as np
import cv2
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import torch
import torchvision.transforms as transforms
from torchvision.models import resnet50
import hashlib

class PinterestVisualSearch:
    def __init__(self):
        # 预训练的特征提取模型
        self.feature_extractor = resnet50(pretrained=True)
        self.feature_extractor.eval()

        # 移除最后的分类层，只保留特征提取部分
        self.feature_extractor = torch.nn.Sequential(*list(self.feature_extractor.children())[:-1])

        # 图像预处理
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 特征数据库
        self.image_features = {}
        self.image_metadata = {}
        self.color_histograms = {}
        self.texture_features = {}

        # 索引结构
        self.feature_index = None
        self.hash_buckets = {}

    def extract_deep_features(self, image):
        """使用深度学习模型提取图像特征"""
        # 预处理图像
        if len(image.shape) == 3:
            image_tensor = self.transform(image).unsqueeze(0)
        else:
            # 如果是灰度图，转换为RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            image_tensor = self.transform(image_rgb).unsqueeze(0)

        # 提取特征
        with torch.no_grad():
            features = self.feature_extractor(image_tensor)
            features = features.squeeze().numpy()

        # L2标准化
        features = features / np.linalg.norm(features)

        return features

    def extract_color_features(self, image):
        """提取颜色特征"""
        # 1. 颜色直方图
        hist_b = cv2.calcHist([image], [0], None, [256], [0, 256])
        hist_g = cv2.calcHist([image], [1], None, [256], [0, 256])
        hist_r = cv2.calcHist([image], [2], None, [256], [0, 256])

        # 标准化直方图
        hist_b = hist_b.flatten() / np.sum(hist_b)
        hist_g = hist_g.flatten() / np.sum(hist_g)
        hist_r = hist_r.flatten() / np.sum(hist_r)

        color_hist = np.concatenate([hist_r, hist_g, hist_b])

        # 2. 主要颜色提取
        dominant_colors = self.extract_dominant_colors(image, k=5)

        # 3. 颜色矩特征
        color_moments = self.calculate_color_moments(image)

        return {
            'histogram': color_hist,
            'dominant_colors': dominant_colors,
            'moments': color_moments
        }

    def extract_dominant_colors(self, image, k=5):
        """提取主要颜色"""
        # 重塑图像为像素列表
        pixels = image.reshape(-1, 3)

        # K-means聚类
        kmeans = KMeans(n_clusters=k, random_state=42)
        kmeans.fit(pixels)

        # 获取聚类中心（主要颜色）
        dominant_colors = kmeans.cluster_centers_.astype(int)

        # 计算每种颜色的比例
        labels = kmeans.labels_
        color_percentages = []
        for i in range(k):
            percentage = np.sum(labels == i) / len(labels)
            color_percentages.append(percentage)

        return list(zip(dominant_colors.tolist(), color_percentages))

    def calculate_color_moments(self, image):
        """计算颜色矩特征"""
        moments = []

        for channel in range(3):  # RGB三个通道
            channel_data = image[:, :, channel].flatten()

            # 一阶矩（均值）
            mean = np.mean(channel_data)

            # 二阶矩（标准差）
            std = np.std(channel_data)

            # 三阶矩（偏度）
            skewness = np.mean(((channel_data - mean) / std) ** 3)

            moments.extend([mean, std, skewness])

        return moments

    def extract_texture_features(self, image):
        """提取纹理特征"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 1. LBP (Local Binary Pattern) 特征
        lbp_features = self.calculate_lbp(gray)

        # 2. Gabor滤波器特征
        gabor_features = self.calculate_gabor_features(gray)

        # 3. 灰度共生矩阵特征
        glcm_features = self.calculate_glcm_features(gray)

        return {
            'lbp': lbp_features,
            'gabor': gabor_features,
            'glcm': glcm_features
        }

    def calculate_lbp(self, gray_image):
        """计算LBP特征"""
        # 简化的LBP实现
        height, width = gray_image.shape
        lbp_image = np.zeros((height-2, width-2), dtype=np.uint8)

        for i in range(1, height-1):
            for j in range(1, width-1):
                center = gray_image[i, j]
                binary_string = ''

                # 8邻域
                neighbors = [
                    gray_image[i-1, j-1], gray_image[i-1, j], gray_image[i-1, j+1],
                    gray_image[i, j+1], gray_image[i+1, j+1], gray_image[i+1, j],
                    gray_image[i+1, j-1], gray_image[i, j-1]
                ]

                for neighbor in neighbors:
                    binary_string += '1' if neighbor >= center else '0'

                lbp_image[i-1, j-1] = int(binary_string, 2)

        # 计算LBP直方图
        lbp_hist, _ = np.histogram(lbp_image.flatten(), bins=256, range=(0, 256))
        lbp_hist = lbp_hist / np.sum(lbp_hist)  # 标准化

        return lbp_hist

    def calculate_gabor_features(self, gray_image):
        """计算Gabor滤波器特征"""
        gabor_responses = []

        # 不同方向和频率的Gabor滤波器
        orientations = [0, 45, 90, 135]
        frequencies = [0.1, 0.3, 0.5]

        for orientation in orientations:
            for frequency in frequencies:
                # 创建Gabor滤波器
                kernel = cv2.getGaborKernel((21, 21), 5, np.radians(orientation),
                                          2*np.pi*frequency, 0.5, 0, ktype=cv2.CV_32F)

                # 应用滤波器
                filtered = cv2.filter2D(gray_image, cv2.CV_8UC3, kernel)

                # 计算响应的统计特征
                mean_response = np.mean(filtered)
                std_response = np.std(filtered)

                gabor_responses.extend([mean_response, std_response])

        return gabor_responses

    def calculate_glcm_features(self, gray_image):
        """计算灰度共生矩阵特征（简化版）"""
        # 简化实现，实际中可以使用skimage.feature.greycomatrix

        # 计算水平方向的共生矩阵
        glcm = np.zeros((256, 256), dtype=np.float32)

        height, width = gray_image.shape
        for i in range(height):
            for j in range(width-1):
                pixel1 = gray_image[i, j]
                pixel2 = gray_image[i, j+1]
                glcm[pixel1, pixel2] += 1

        # 标准化
        glcm = glcm / np.sum(glcm)

        # 计算纹理特征
        contrast = np.sum(glcm * (np.arange(256)[:, None] - np.arange(256)[None, :]) ** 2)
        homogeneity = np.sum(glcm / (1 + (np.arange(256)[:, None] - np.arange(256)[None, :]) ** 2))
        energy = np.sum(glcm ** 2)
        correlation = self.calculate_correlation(glcm)

        return [contrast, homogeneity, energy, correlation]

    def calculate_correlation(self, glcm):
        """计算GLCM相关性"""
        # 简化实现
        i_indices, j_indices = np.meshgrid(np.arange(256), np.arange(256), indexing='ij')

        mu_i = np.sum(i_indices * glcm)
        mu_j = np.sum(j_indices * glcm)

        sigma_i = np.sqrt(np.sum((i_indices - mu_i) ** 2 * glcm))
        sigma_j = np.sqrt(np.sum((j_indices - mu_j) ** 2 * glcm))

        if sigma_i * sigma_j == 0:
            return 0

        correlation = np.sum((i_indices - mu_i) * (j_indices - mu_j) * glcm) / (sigma_i * sigma_j)

        return correlation

    def create_perceptual_hash(self, image):
        """创建感知哈希"""
        # 缩放到8x8
        resized = cv2.resize(image, (8, 8))

        # 转换为灰度
        gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)

        # 计算平均值
        avg = np.mean(gray)

        # 生成哈希
        hash_bits = []
        for pixel in gray.flatten():
            hash_bits.append('1' if pixel > avg else '0')

        return ''.join(hash_bits)

    def index_image(self, image_id, image, metadata=None):
        """为图像建立索引"""
        # 提取各种特征
        deep_features = self.extract_deep_features(image)
        color_features = self.extract_color_features(image)
        texture_features = self.extract_texture_features(image)
        perceptual_hash = self.create_perceptual_hash(image)

        # 存储特征
        self.image_features[image_id] = deep_features
        self.color_histograms[image_id] = color_features
        self.texture_features[image_id] = texture_features

        # 存储元数据
        if metadata:
            self.image_metadata[image_id] = metadata

        # 哈希索引
        if perceptual_hash not in self.hash_buckets:
            self.hash_buckets[perceptual_hash] = []
        self.hash_buckets[perceptual_hash].append(image_id)

        return {
            'deep_features': deep_features,
            'color_features': color_features,
            'texture_features': texture_features,
            'perceptual_hash': perceptual_hash
        }

    def search_similar_images(self, query_image, top_k=10, search_type='hybrid'):
        """搜索相似图像"""
        # 提取查询图像的特征
        query_deep_features = self.extract_deep_features(query_image)
        query_color_features = self.extract_color_features(query_image)
        query_texture_features = self.extract_texture_features(query_image)
        query_hash = self.create_perceptual_hash(query_image)

        similarities = []

        if search_type == 'deep' or search_type == 'hybrid':
            # 深度特征相似度搜索
            for image_id, features in self.image_features.items():
                similarity = cosine_similarity([query_deep_features], [features])[0][0]
                similarities.append((image_id, similarity, 'deep'))

        if search_type == 'color' or search_type == 'hybrid':
            # 颜色特征相似度搜索
            for image_id, color_features in self.color_histograms.items():
                # 颜色直方图相似度
                hist_similarity = self.calculate_histogram_similarity(
                    query_color_features['histogram'],
                    color_features['histogram']
                )

                # 主要颜色相似度
                color_similarity = self.calculate_dominant_color_similarity(
                    query_color_features['dominant_colors'],
                    color_features['dominant_colors']
                )

                combined_similarity = 0.7 * hist_similarity + 0.3 * color_similarity
                similarities.append((image_id, combined_similarity, 'color'))

        if search_type == 'texture' or search_type == 'hybrid':
            # 纹理特征相似度搜索
            for image_id, texture_features in self.texture_features.items():
                texture_similarity = self.calculate_texture_similarity(
                    query_texture_features, texture_features
                )
                similarities.append((image_id, texture_similarity, 'texture'))

        if search_type == 'hash':
            # 感知哈希快速搜索
            similar_hashes = self.find_similar_hashes(query_hash)
            for similar_hash in similar_hashes:
                for image_id in self.hash_buckets.get(similar_hash, []):
                    hamming_distance = self.calculate_hamming_distance(query_hash, similar_hash)
                    similarity = 1 - (hamming_distance / 64)  # 64位哈希
                    similarities.append((image_id, similarity, 'hash'))

        # 合并和排序结果
        if search_type == 'hybrid':
            # 混合搜索：合并不同类型的相似度
            combined_similarities = self.combine_similarities(similarities)
            similarities = combined_similarities

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        # 去重并返回top_k结果
        seen_images = set()
        unique_results = []

        for image_id, similarity, search_method in similarities:
            if image_id not in seen_images:
                seen_images.add(image_id)
                unique_results.append({
                    'image_id': image_id,
                    'similarity': similarity,
                    'search_method': search_method,
                    'metadata': self.image_metadata.get(image_id, {})
                })

                if len(unique_results) >= top_k:
                    break

        return unique_results

    def combine_similarities(self, similarities):
        """合并不同类型的相似度得分"""
        # 按图像ID分组
        image_scores = {}

        for image_id, similarity, method in similarities:
            if image_id not in image_scores:
                image_scores[image_id] = {}
            image_scores[image_id][method] = similarity

        # 计算加权平均
        weights = {'deep': 0.5, 'color': 0.3, 'texture': 0.2}
        combined_results = []

        for image_id, scores in image_scores.items():
            weighted_score = 0
            total_weight = 0

            for method, weight in weights.items():
                if method in scores:
                    weighted_score += scores[method] * weight
                    total_weight += weight

            if total_weight > 0:
                final_score = weighted_score / total_weight
                combined_results.append((image_id, final_score, 'hybrid'))

        return combined_results

    # 辅助方法
    def calculate_histogram_similarity(self, hist1, hist2):
        """计算直方图相似度"""
        return 1 - cv2.compareHist(hist1.astype(np.float32), hist2.astype(np.float32), cv2.HISTCMP_CHISQR)

    def calculate_dominant_color_similarity(self, colors1, colors2):
        """计算主要颜色相似度"""
        if not colors1 or not colors2:
            return 0

        total_similarity = 0
        for color1, percent1 in colors1:
            best_match = 0
            for color2, percent2 in colors2:
                # 计算颜色距离
                color_distance = np.linalg.norm(np.array(color1) - np.array(color2))
                color_similarity = 1 / (1 + color_distance / 100)  # 标准化

                # 考虑颜色比例
                weight = min(percent1, percent2)
                weighted_similarity = color_similarity * weight

                best_match = max(best_match, weighted_similarity)

            total_similarity += best_match

        return total_similarity

    def calculate_texture_similarity(self, texture1, texture2):
        """计算纹理相似度"""
        # LBP相似度
        lbp_sim = cosine_similarity([texture1['lbp']], [texture2['lbp']])[0][0]

        # Gabor相似度
        gabor_sim = cosine_similarity([texture1['gabor']], [texture2['gabor']])[0][0]

        # GLCM相似度
        glcm_sim = cosine_similarity([texture1['glcm']], [texture2['glcm']])[0][0]

        # 加权平均
        texture_similarity = 0.4 * lbp_sim + 0.4 * gabor_sim + 0.2 * glcm_sim

        return texture_similarity

    def find_similar_hashes(self, query_hash, max_distance=5):
        """找到相似的哈希值"""
        similar_hashes = []

        for stored_hash in self.hash_buckets.keys():
            distance = self.calculate_hamming_distance(query_hash, stored_hash)
            if distance <= max_distance:
                similar_hashes.append(stored_hash)

        return similar_hashes

    def calculate_hamming_distance(self, hash1, hash2):
        """计算汉明距离"""
        return sum(c1 != c2 for c1, c2 in zip(hash1, hash2))

# 使用示例
def pinterest_visual_search_demo():
    # 注意：这个演示需要实际的图像数据
    # 这里只展示系统的使用方式

    visual_search = PinterestVisualSearch()

    print("=== Pinterest视觉搜索演示 ===")
    print("注意：此演示需要实际图像数据")

    # 模拟图像索引过程
    print("\n1. 图像索引过程:")
    print("   - 提取深度学习特征 (ResNet50)")
    print("   - 提取颜色特征 (直方图、主要颜色)")
    print("   - 提取纹理特征 (LBP、Gabor、GLCM)")
    print("   - 生成感知哈希")

    # 模拟搜索过程
    print("\n2. 相似图像搜索:")
    print("   - 混合搜索: 结合多种特征")
    print("   - 深度特征搜索: 基于CNN特征")
    print("   - 颜色搜索: 基于颜色分布")
    print("   - 纹理搜索: 基于纹理模式")
    print("   - 哈希搜索: 快速近似匹配")

    print("\n3. 搜索结果排序:")
    print("   - 多特征融合")
    print("   - 相似度加权")
    print("   - 去重和排序")

pinterest_visual_search_demo()
```

## 8.5 硬件与芯片大厂算法题

### 题目37：Tesla自动驾驶感知算法
**公司来源**: Tesla
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计Tesla自动驾驶的多传感器融合感知算法，处理摄像头、雷达、超声波数据。

**推荐解答**:
```python
import numpy as np
import cv2
from sklearn.cluster import DBSCAN
from scipy.spatial.distance import euclidean
from collections import defaultdict

class TeslaPerceptionSystem:
    def __init__(self):
        # 传感器配置
        self.camera_config = {
            'front': {'fov': 120, 'range': 150, 'resolution': (1280, 960)},
            'rear': {'fov': 120, 'range': 50, 'resolution': (1280, 960)},
            'left': {'fov': 90, 'range': 80, 'resolution': (1280, 960)},
            'right': {'fov': 90, 'range': 80, 'resolution': (1280, 960)}
        }

        self.radar_config = {
            'front': {'fov': 60, 'range': 200, 'resolution': 0.1},
            'rear': {'fov': 60, 'range': 100, 'resolution': 0.1}
        }

        # 目标检测模型（简化）
        self.object_detector = None
        self.depth_estimator = None

        # 卡尔曼滤波器用于目标跟踪
        self.trackers = {}
        self.next_tracker_id = 0

    def process_camera_data(self, camera_images):
        """处理摄像头数据"""
        detected_objects = {}

        for camera_name, image in camera_images.items():
            # 1. 目标检测
            objects = self.detect_objects(image, camera_name)

            # 2. 深度估计
            depth_map = self.estimate_depth(image, camera_name)

            # 3. 3D位置估计
            objects_3d = self.estimate_3d_positions(objects, depth_map, camera_name)

            detected_objects[camera_name] = objects_3d

        return detected_objects

    def detect_objects(self, image, camera_name):
        """目标检测（简化实现）"""
        # 实际中会使用YOLO、SSD等深度学习模型
        objects = []

        # 模拟检测结果
        if camera_name == 'front':
            # 前方摄像头检测车辆、行人、交通标志等
            objects = [
                {'class': 'car', 'bbox': [100, 200, 300, 400], 'confidence': 0.95},
                {'class': 'pedestrian', 'bbox': [500, 300, 600, 500], 'confidence': 0.88},
                {'class': 'traffic_light', 'bbox': [800, 100, 850, 200], 'confidence': 0.92}
            ]
        elif camera_name == 'rear':
            objects = [
                {'class': 'car', 'bbox': [200, 250, 400, 450], 'confidence': 0.90}
            ]

        return objects

    def estimate_depth(self, image, camera_name):
        """深度估计"""
        # 实际中会使用立体视觉或单目深度估计网络
        height, width = image.shape[:2]

        # 简化的深度图生成
        depth_map = np.random.uniform(5, 100, (height, width))

        return depth_map

    def estimate_3d_positions(self, objects, depth_map, camera_name):
        """估计3D位置"""
        objects_3d = []

        camera_params = self.camera_config[camera_name]

        for obj in objects:
            bbox = obj['bbox']
            center_x = (bbox[0] + bbox[2]) // 2
            center_y = (bbox[1] + bbox[3]) // 2

            # 从深度图获取距离
            distance = depth_map[center_y, center_x]

            # 转换为3D坐标（车辆坐标系）
            x, y, z = self.pixel_to_3d(center_x, center_y, distance, camera_name)

            obj_3d = obj.copy()
            obj_3d.update({
                'position_3d': (x, y, z),
                'distance': distance,
                'camera': camera_name
            })

            objects_3d.append(obj_3d)

        return objects_3d

    def process_radar_data(self, radar_data):
        """处理雷达数据"""
        radar_objects = {}

        for radar_name, data in radar_data.items():
            objects = []

            # 雷达点云聚类
            if len(data['points']) > 0:
                clusters = self.cluster_radar_points(data['points'])

                for cluster in clusters:
                    # 计算聚类中心和速度
                    center = np.mean(cluster['points'], axis=0)
                    velocity = np.mean(cluster['velocities'], axis=0)

                    objects.append({
                        'position_3d': tuple(center),
                        'velocity': tuple(velocity),
                        'rcs': cluster['rcs'],  # 雷达截面积
                        'radar': radar_name
                    })

            radar_objects[radar_name] = objects

        return radar_objects

    def cluster_radar_points(self, points):
        """雷达点聚类"""
        if len(points) < 2:
            return []

        # 使用DBSCAN聚类
        clustering = DBSCAN(eps=2.0, min_samples=2)
        labels = clustering.fit_predict(points)

        clusters = []
        for label in set(labels):
            if label == -1:  # 噪声点
                continue

            cluster_points = points[labels == label]
            cluster = {
                'points': cluster_points,
                'velocities': np.random.uniform(-20, 20, (len(cluster_points), 2)),  # 简化
                'rcs': np.mean(np.random.uniform(0.1, 10, len(cluster_points)))
            }
            clusters.append(cluster)

        return clusters

    def sensor_fusion(self, camera_objects, radar_objects, ultrasonic_data=None):
        """多传感器融合"""
        fused_objects = []

        # 1. 数据关联：匹配不同传感器检测到的同一目标
        associations = self.associate_detections(camera_objects, radar_objects)

        # 2. 融合关联的检测结果
        for association in associations:
            fused_obj = self.fuse_detections(association)
            fused_objects.append(fused_obj)

        # 3. 添加未关联的检测结果
        unassociated_camera = self.get_unassociated_camera_objects(camera_objects, associations)
        unassociated_radar = self.get_unassociated_radar_objects(radar_objects, associations)

        fused_objects.extend(unassociated_camera)
        fused_objects.extend(unassociated_radar)

        return fused_objects

    def associate_detections(self, camera_objects, radar_objects):
        """检测结果关联"""
        associations = []

        # 收集所有摄像头检测结果
        all_camera_objects = []
        for camera_name, objects in camera_objects.items():
            for obj in objects:
                all_camera_objects.append(obj)

        # 收集所有雷达检测结果
        all_radar_objects = []
        for radar_name, objects in radar_objects.items():
            for obj in objects:
                all_radar_objects.append(obj)

        # 基于3D位置进行关联
        for cam_obj in all_camera_objects:
            best_match = None
            min_distance = float('inf')

            cam_pos = np.array(cam_obj['position_3d'])

            for radar_obj in all_radar_objects:
                radar_pos = np.array(radar_obj['position_3d'])
                distance = np.linalg.norm(cam_pos - radar_pos)

                # 关联阈值
                if distance < 5.0 and distance < min_distance:
                    min_distance = distance
                    best_match = radar_obj

            if best_match:
                associations.append({
                    'camera': cam_obj,
                    'radar': best_match,
                    'distance': min_distance
                })

        return associations

    def fuse_detections(self, association):
        """融合检测结果"""
        cam_obj = association['camera']
        radar_obj = association['radar']

        # 位置融合（加权平均）
        cam_pos = np.array(cam_obj['position_3d'])
        radar_pos = np.array(radar_obj['position_3d'])

        # 摄像头在近距离更准确，雷达在远距离更准确
        distance = np.linalg.norm(cam_pos)
        if distance < 30:
            pos_weight_cam = 0.7
        else:
            pos_weight_cam = 0.3

        fused_position = pos_weight_cam * cam_pos + (1 - pos_weight_cam) * radar_pos

        # 速度信息主要来自雷达
        velocity = radar_obj.get('velocity', (0, 0))

        # 分类信息主要来自摄像头
        object_class = cam_obj.get('class', 'unknown')
        confidence = cam_obj.get('confidence', 0.5)

        fused_object = {
            'class': object_class,
            'position_3d': tuple(fused_position),
            'velocity': velocity,
            'confidence': confidence,
            'fusion_type': 'camera_radar',
            'distance': np.linalg.norm(fused_position)
        }

        return fused_object

    def track_objects(self, fused_objects):
        """目标跟踪"""
        # 简化的跟踪实现
        tracked_objects = []

        for obj in fused_objects:
            # 寻找最近的跟踪器
            best_tracker = None
            min_distance = float('inf')

            obj_pos = np.array(obj['position_3d'])

            for tracker_id, tracker in self.trackers.items():
                predicted_pos = tracker['predicted_position']
                distance = np.linalg.norm(obj_pos - predicted_pos)

                if distance < 10.0 and distance < min_distance:
                    min_distance = distance
                    best_tracker = tracker_id

            if best_tracker:
                # 更新现有跟踪器
                self.update_tracker(best_tracker, obj)
                obj['track_id'] = best_tracker
            else:
                # 创建新跟踪器
                track_id = self.create_new_tracker(obj)
                obj['track_id'] = track_id

            tracked_objects.append(obj)

        return tracked_objects

    def predict_trajectories(self, tracked_objects):
        """轨迹预测"""
        predictions = []

        for obj in tracked_objects:
            if 'track_id' in obj and obj['track_id'] in self.trackers:
                tracker = self.trackers[obj['track_id']]

                # 简单的线性预测
                current_pos = np.array(obj['position_3d'])
                velocity = np.array(obj.get('velocity', (0, 0, 0)))

                # 预测未来3秒的轨迹
                future_positions = []
                for t in np.arange(0.1, 3.1, 0.1):
                    future_pos = current_pos + velocity * t
                    future_positions.append(tuple(future_pos))

                predictions.append({
                    'track_id': obj['track_id'],
                    'current_position': obj['position_3d'],
                    'predicted_trajectory': future_positions,
                    'confidence': obj.get('confidence', 0.5)
                })

        return predictions

    # 辅助方法
    def pixel_to_3d(self, pixel_x, pixel_y, distance, camera_name):
        """像素坐标转3D坐标"""
        # 简化的坐标转换
        camera_params = self.camera_config[camera_name]

        # 假设的内参矩阵
        fx, fy = 800, 800  # 焦距
        cx, cy = 640, 480  # 主点

        # 转换为相机坐标系
        x_cam = (pixel_x - cx) * distance / fx
        y_cam = (pixel_y - cy) * distance / fy
        z_cam = distance

        # 转换为车辆坐标系（简化）
        if camera_name == 'front':
            x_vehicle = z_cam
            y_vehicle = -x_cam
            z_vehicle = -y_cam
        else:
            # 其他摄像头的坐标转换
            x_vehicle = z_cam
            y_vehicle = -x_cam
            z_vehicle = -y_cam

        return x_vehicle, y_vehicle, z_vehicle

    def create_new_tracker(self, obj):
        """创建新的跟踪器"""
        tracker_id = self.next_tracker_id
        self.next_tracker_id += 1

        self.trackers[tracker_id] = {
            'position': np.array(obj['position_3d']),
            'velocity': np.array(obj.get('velocity', (0, 0, 0))),
            'predicted_position': np.array(obj['position_3d']),
            'last_update': 0,
            'class': obj.get('class', 'unknown')
        }

        return tracker_id

    def update_tracker(self, tracker_id, obj):
        """更新跟踪器"""
        tracker = self.trackers[tracker_id]

        # 简单的卡尔曼滤波更新
        measured_pos = np.array(obj['position_3d'])
        predicted_pos = tracker['predicted_position']

        # 融合预测和测量
        alpha = 0.7  # 测量权重
        updated_pos = alpha * measured_pos + (1 - alpha) * predicted_pos

        # 更新速度估计
        if 'velocity' in obj:
            measured_vel = np.array(obj['velocity'])
            tracker['velocity'] = 0.8 * measured_vel + 0.2 * tracker['velocity']

        tracker['position'] = updated_pos
        tracker['predicted_position'] = updated_pos + tracker['velocity'] * 0.1  # 预测下一帧
        tracker['last_update'] = 0

    def get_unassociated_camera_objects(self, camera_objects, associations):
        """获取未关联的摄像头目标"""
        associated_camera = set()
        for assoc in associations:
            # 这里需要更复杂的逻辑来标识已关联的对象
            pass

        # 简化实现
        return []

    def get_unassociated_radar_objects(self, radar_objects, associations):
        """获取未关联的雷达目标"""
        # 简化实现
        return []

# 使用示例
def tesla_perception_demo():
    tesla_system = TeslaPerceptionSystem()

    # 模拟传感器数据
    camera_images = {
        'front': np.random.randint(0, 255, (960, 1280, 3), dtype=np.uint8),
        'rear': np.random.randint(0, 255, (960, 1280, 3), dtype=np.uint8)
    }

    radar_data = {
        'front': {
            'points': np.random.uniform(-50, 50, (20, 3)),  # 20个雷达点
            'velocities': np.random.uniform(-20, 20, (20, 2))
        }
    }

    print("=== Tesla自动驾驶感知系统演示 ===")

    # 1. 处理摄像头数据
    camera_objects = tesla_system.process_camera_data(camera_images)
    print(f"摄像头检测到 {sum(len(objs) for objs in camera_objects.values())} 个目标")

    # 2. 处理雷达数据
    radar_objects = tesla_system.process_radar_data(radar_data)
    print(f"雷达检测到 {sum(len(objs) for objs in radar_objects.values())} 个目标")

    # 3. 传感器融合
    fused_objects = tesla_system.sensor_fusion(camera_objects, radar_objects)
    print(f"融合后共 {len(fused_objects)} 个目标")

    # 4. 目标跟踪
    tracked_objects = tesla_system.track_objects(fused_objects)
    print(f"跟踪 {len(tracked_objects)} 个目标")

    # 5. 轨迹预测
    predictions = tesla_system.predict_trajectories(tracked_objects)
    print(f"预测 {len(predictions)} 个目标的轨迹")

    # 显示结果
    for i, obj in enumerate(tracked_objects):
        print(f"\n目标 {i+1}:")
        print(f"  类别: {obj.get('class', 'unknown')}")
        print(f"  位置: {obj['position_3d']}")
        print(f"  距离: {obj.get('distance', 0):.1f}m")
        print(f"  跟踪ID: {obj.get('track_id', 'N/A')}")

tesla_perception_demo()
```

### 题目38：NVIDIA GPU并行计算优化
**公司来源**: NVIDIA
**难度**: ⭐⭐⭐⭐⭐

**题目**: 设计GPU并行计算框架优化深度学习训练，考虑内存管理、计算调度、多GPU协调。

**推荐解答**:
```python
import numpy as np
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import DataLoader, DistributedSampler
import time
from collections import defaultdict

class NVIDIAGPUOptimizer:
    def __init__(self, num_gpus=4):
        self.num_gpus = num_gpus
        self.device_ids = list(range(num_gpus))
        self.memory_stats = defaultdict(dict)
        self.compute_stats = defaultdict(dict)

        # 初始化分布式训练
        if torch.cuda.is_available():
            self.setup_distributed_training()

    def setup_distributed_training(self):
        """设置分布式训练环境"""
        # 初始化进程组
        if not dist.is_initialized():
            dist.init_process_group(
                backend='nccl',  # NVIDIA GPU推荐使用NCCL
                init_method='env://',
                world_size=self.num_gpus,
                rank=0  # 简化示例
            )

    def optimize_memory_usage(self, model, batch_size):
        """优化GPU内存使用"""
        optimizations = {}

        # 1. 梯度累积优化
        effective_batch_size = batch_size * self.num_gpus
        if effective_batch_size > 1024:  # 大批次时使用梯度累积
            accumulation_steps = effective_batch_size // 512
            optimized_batch_size = 512 // self.num_gpus

            optimizations['gradient_accumulation'] = {
                'steps': accumulation_steps,
                'batch_size_per_gpu': optimized_batch_size
            }

        # 2. 混合精度训练
        optimizations['mixed_precision'] = {
            'enabled': True,
            'loss_scale': 'dynamic',
            'opt_level': 'O1'  # 保守的混合精度设置
        }

        # 3. 内存映射优化
        optimizations['memory_mapping'] = {
            'pin_memory': True,
            'non_blocking': True,
            'prefetch_factor': 2
        }

        # 4. 模型并行策略
        model_size = self.estimate_model_size(model)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory

        if model_size > gpu_memory * 0.8:  # 模型太大，需要模型并行
            optimizations['model_parallel'] = {
                'strategy': 'pipeline',
                'num_stages': min(4, self.num_gpus)
            }
        else:
            optimizations['data_parallel'] = {
                'strategy': 'DDP',
                'find_unused_parameters': False
            }

        return optimizations

    def optimize_compute_scheduling(self, model, dataloader):
        """优化计算调度"""
        scheduling_config = {}

        # 1. 计算图优化
        scheduling_config['graph_optimization'] = {
            'fusion': True,  # 算子融合
            'constant_folding': True,  # 常量折叠
            'dead_code_elimination': True  # 死代码消除
        }

        # 2. 内核调度优化
        scheduling_config['kernel_scheduling'] = {
            'stream_count': 4,  # 多流并行
            'priority_high': True,  # 高优先级流
            'overlap_computation_communication': True
        }

        # 3. 数据加载优化
        scheduling_config['data_loading'] = {
            'num_workers': min(8, self.num_gpus * 2),
            'persistent_workers': True,
            'prefetch_factor': 4
        }

        # 4. 动态批次大小调整
        scheduling_config['dynamic_batching'] = {
            'enabled': True,
            'min_batch_size': 16,
            'max_batch_size': 128,
            'adjustment_factor': 1.2
        }

        return scheduling_config

    def implement_multi_gpu_training(self, model, train_loader, optimizer, num_epochs):
        """实现多GPU训练"""
        # 1. 模型分布式包装
        if torch.cuda.device_count() > 1:
            model = DDP(model, device_ids=self.device_ids)

        # 2. 混合精度训练设置
        scaler = torch.cuda.amp.GradScaler()

        # 3. 训练循环
        training_stats = {
            'epoch_times': [],
            'memory_usage': [],
            'throughput': []
        }

        for epoch in range(num_epochs):
            epoch_start = time.time()

            # 设置分布式采样器
            if hasattr(train_loader.sampler, 'set_epoch'):
                train_loader.sampler.set_epoch(epoch)

            model.train()
            total_loss = 0
            num_batches = 0

            for batch_idx, (data, target) in enumerate(train_loader):
                # 数据移动到GPU
                data = data.cuda(non_blocking=True)
                target = target.cuda(non_blocking=True)

                optimizer.zero_grad()

                # 前向传播（混合精度）
                with torch.cuda.amp.autocast():
                    output = model(data)
                    loss = nn.CrossEntropyLoss()(output, target)

                # 反向传播
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()

                total_loss += loss.item()
                num_batches += 1

                # 记录内存使用情况
                if batch_idx % 100 == 0:
                    self.log_memory_usage(epoch, batch_idx)

            epoch_time = time.time() - epoch_start
            training_stats['epoch_times'].append(epoch_time)

            # 计算吞吐量
            samples_per_second = len(train_loader.dataset) / epoch_time
            training_stats['throughput'].append(samples_per_second)

            print(f"Epoch {epoch+1}/{num_epochs}, "
                  f"Loss: {total_loss/num_batches:.4f}, "
                  f"Time: {epoch_time:.2f}s, "
                  f"Throughput: {samples_per_second:.1f} samples/s")

        return training_stats

    def optimize_inference(self, model, input_shape):
        """推理优化"""
        optimization_config = {}

        # 1. TensorRT优化
        optimization_config['tensorrt'] = {
            'enabled': True,
            'precision': 'fp16',
            'max_batch_size': 64,
            'workspace_size': 1 << 30  # 1GB
        }

        # 2. 图优化
        optimization_config['graph_optimization'] = {
            'constant_folding': True,
            'operator_fusion': True,
            'layout_optimization': True
        }

        # 3. 内存优化
        optimization_config['memory_optimization'] = {
            'memory_pool': True,
            'memory_reuse': True,
            'garbage_collection': False  # 推理时关闭GC
        }

        # 4. 批处理优化
        optimization_config['batching'] = {
            'dynamic_batching': True,
            'max_delay_ms': 10,
            'preferred_batch_sizes': [1, 4, 8, 16, 32]
        }

        return optimization_config

    def profile_gpu_performance(self, model, dataloader, num_iterations=100):
        """GPU性能分析"""
        profiling_results = {
            'kernel_times': [],
            'memory_transfers': [],
            'compute_utilization': [],
            'memory_utilization': []
        }

        # 使用PyTorch Profiler
        with torch.profiler.profile(
            activities=[
                torch.profiler.ProfilerActivity.CPU,
                torch.profiler.ProfilerActivity.CUDA,
            ],
            schedule=torch.profiler.schedule(wait=1, warmup=1, active=3, repeat=2),
            on_trace_ready=torch.profiler.tensorboard_trace_handler('./log/profiler'),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:

            for step, (data, target) in enumerate(dataloader):
                if step >= num_iterations:
                    break

                data = data.cuda()
                target = target.cuda()

                # 前向传播
                output = model(data)
                loss = nn.CrossEntropyLoss()(output, target)

                # 反向传播
                loss.backward()

                prof.step()

                # 记录性能指标
                if step % 10 == 0:
                    gpu_util = self.get_gpu_utilization()
                    memory_util = self.get_memory_utilization()

                    profiling_results['compute_utilization'].append(gpu_util)
                    profiling_results['memory_utilization'].append(memory_util)

        return profiling_results

    def auto_tune_hyperparameters(self, model, train_loader, val_loader):
        """自动调优超参数"""
        tuning_config = {
            'batch_size': [16, 32, 64, 128],
            'learning_rate': [1e-4, 5e-4, 1e-3, 5e-3],
            'num_workers': [2, 4, 8, 16],
            'mixed_precision': [True, False]
        }

        best_config = None
        best_throughput = 0

        for batch_size in tuning_config['batch_size']:
            for lr in tuning_config['learning_rate']:
                for num_workers in tuning_config['num_workers']:
                    for mixed_precision in tuning_config['mixed_precision']:

                        # 测试配置
                        config = {
                            'batch_size': batch_size,
                            'learning_rate': lr,
                            'num_workers': num_workers,
                            'mixed_precision': mixed_precision
                        }

                        throughput = self.benchmark_config(model, train_loader, config)

                        if throughput > best_throughput:
                            best_throughput = throughput
                            best_config = config

        return best_config, best_throughput

    # 辅助方法
    def estimate_model_size(self, model):
        """估算模型大小"""
        param_size = 0
        buffer_size = 0

        for param in model.parameters():
            param_size += param.nelement() * param.element_size()

        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()

        return param_size + buffer_size

    def log_memory_usage(self, epoch, batch_idx):
        """记录内存使用情况"""
        for gpu_id in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(gpu_id)
            memory_reserved = torch.cuda.memory_reserved(gpu_id)

            self.memory_stats[gpu_id][f'epoch_{epoch}_batch_{batch_idx}'] = {
                'allocated': memory_allocated,
                'reserved': memory_reserved,
                'utilization': memory_allocated / memory_reserved if memory_reserved > 0 else 0
            }

    def get_gpu_utilization(self):
        """获取GPU利用率"""
        # 简化实现，实际中会使用nvidia-ml-py
        return np.random.uniform(70, 95)

    def get_memory_utilization(self):
        """获取内存利用率"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated()
            reserved = torch.cuda.memory_reserved()
            return allocated / reserved if reserved > 0 else 0
        return 0

    def benchmark_config(self, model, dataloader, config):
        """基准测试配置"""
        # 简化的基准测试
        start_time = time.time()

        # 模拟训练几个批次
        for i, (data, target) in enumerate(dataloader):
            if i >= 10:  # 只测试10个批次
                break

            data = data.cuda()
            target = target.cuda()

            output = model(data)
            loss = nn.CrossEntropyLoss()(output, target)
            loss.backward()

        end_time = time.time()
        throughput = 10 * config['batch_size'] / (end_time - start_time)

        return throughput

# 使用示例
def nvidia_gpu_optimization_demo():
    # 创建优化器
    gpu_optimizer = NVIDIAGPUOptimizer(num_gpus=4)

    # 创建示例模型
    model = nn.Sequential(
        nn.Linear(1000, 512),
        nn.ReLU(),
        nn.Linear(512, 256),
        nn.ReLU(),
        nn.Linear(256, 10)
    ).cuda()

    print("=== NVIDIA GPU并行计算优化演示 ===")

    # 1. 内存优化
    memory_opts = gpu_optimizer.optimize_memory_usage(model, batch_size=64)
    print(f"内存优化配置: {memory_opts}")

    # 2. 计算调度优化
    # 创建模拟数据加载器
    dummy_data = torch.randn(1000, 1000)
    dummy_targets = torch.randint(0, 10, (1000,))
    dataset = torch.utils.data.TensorDataset(dummy_data, dummy_targets)
    dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

    compute_opts = gpu_optimizer.optimize_compute_scheduling(model, dataloader)
    print(f"计算调度优化: {compute_opts}")

    # 3. 推理优化
    inference_opts = gpu_optimizer.optimize_inference(model, input_shape=(1, 1000))
    print(f"推理优化配置: {inference_opts}")

    # 4. 自动调优
    print("开始自动调优...")
    best_config, best_throughput = gpu_optimizer.auto_tune_hyperparameters(
        model, dataloader, dataloader
    )
    print(f"最佳配置: {best_config}")
    print(f"最佳吞吐量: {best_throughput:.1f} samples/s")

nvidia_gpu_optimization_demo()
```

## 8.6 传统IT与云服务大厂算法题

### 题目39：IBM Watson自然语言处理
**公司来源**: IBM
**难度**: ⭐⭐⭐⭐

**题目**: 设计IBM Watson风格的问答系统，实现自然语言理解、知识检索和答案生成。

**推荐解答**:
```python
import numpy as np
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from collections import defaultdict
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer

class IBMWatsonQASystem:
    def __init__(self):
        self.knowledge_base = {}
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=10000,
            stop_words='english',
            ngram_range=(1, 3)
        )
        self.document_vectors = None
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english'))

        # 问题类型分类器
        self.question_types = {
            'factual': ['what', 'who', 'when', 'where', 'which'],
            'definition': ['define', 'meaning', 'what is', 'what are'],
            'comparison': ['compare', 'difference', 'versus', 'vs'],
            'procedural': ['how', 'steps', 'process', 'procedure'],
            'causal': ['why', 'because', 'cause', 'reason'],
            'quantitative': ['how many', 'how much', 'number', 'amount']
        }

    def preprocess_text(self, text):
        """文本预处理"""
        # 1. 转换为小写
        text = text.lower()

        # 2. 去除特殊字符
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)

        # 3. 分词
        tokens = word_tokenize(text)

        # 4. 去停用词和词形还原
        processed_tokens = []
        for token in tokens:
            if token not in self.stop_words and len(token) > 2:
                lemmatized = self.lemmatizer.lemmatize(token)
                processed_tokens.append(lemmatized)

        return ' '.join(processed_tokens)

    def build_knowledge_base(self, documents):
        """构建知识库"""
        processed_docs = []

        for doc_id, content in documents.items():
            # 预处理文档
            processed_content = self.preprocess_text(content)
            processed_docs.append(processed_content)

            # 存储原始和处理后的内容
            self.knowledge_base[doc_id] = {
                'original': content,
                'processed': processed_content,
                'sentences': sent_tokenize(content)
            }

        # 构建TF-IDF向量
        self.document_vectors = self.tfidf_vectorizer.fit_transform(processed_docs)

        return len(self.knowledge_base)

    def classify_question_type(self, question):
        """分类问题类型"""
        question_lower = question.lower()

        for q_type, keywords in self.question_types.items():
            for keyword in keywords:
                if keyword in question_lower:
                    return q_type

        return 'general'

    def extract_key_entities(self, text):
        """提取关键实体"""
        # 简化的命名实体识别
        entities = {
            'PERSON': [],
            'ORGANIZATION': [],
            'LOCATION': [],
            'DATE': [],
            'NUMBER': []
        }

        # 使用正则表达式进行简单的实体识别
        # 人名模式（大写字母开头的连续单词）
        person_pattern = r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        entities['PERSON'] = re.findall(person_pattern, text)

        # 日期模式
        date_pattern = r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}\b'
        entities['DATE'] = re.findall(date_pattern, text)

        # 数字模式
        number_pattern = r'\b\d+(?:,\d{3})*(?:\.\d+)?\b'
        entities['NUMBER'] = re.findall(number_pattern, text)

        return entities

    def retrieve_relevant_documents(self, question, top_k=5):
        """检索相关文档"""
        # 预处理问题
        processed_question = self.preprocess_text(question)

        # 转换为TF-IDF向量
        question_vector = self.tfidf_vectorizer.transform([processed_question])

        # 计算相似度
        similarities = cosine_similarity(question_vector, self.document_vectors).flatten()

        # 获取最相关的文档
        top_indices = np.argsort(similarities)[::-1][:top_k]

        relevant_docs = []
        for idx in top_indices:
            doc_id = list(self.knowledge_base.keys())[idx]
            relevant_docs.append({
                'doc_id': doc_id,
                'similarity': similarities[idx],
                'content': self.knowledge_base[doc_id]['original']
            })

        return relevant_docs

    def extract_candidate_answers(self, question, relevant_docs):
        """从相关文档中提取候选答案"""
        question_type = self.classify_question_type(question)
        question_entities = self.extract_key_entities(question)

        candidates = []

        for doc in relevant_docs:
            sentences = self.knowledge_base[doc['doc_id']]['sentences']

            for sentence in sentences:
                # 计算句子与问题的相似度
                sentence_processed = self.preprocess_text(sentence)
                question_processed = self.preprocess_text(question)

                sentence_vector = self.tfidf_vectorizer.transform([sentence_processed])
                question_vector = self.tfidf_vectorizer.transform([question_processed])

                similarity = cosine_similarity(sentence_vector, question_vector)[0][0]

                # 根据问题类型评分
                type_score = self.score_by_question_type(sentence, question_type)

                # 实体匹配评分
                entity_score = self.score_by_entity_match(sentence, question_entities)

                # 综合评分
                final_score = 0.5 * similarity + 0.3 * type_score + 0.2 * entity_score

                candidates.append({
                    'text': sentence,
                    'score': final_score,
                    'doc_id': doc['doc_id'],
                    'similarity': similarity,
                    'type_score': type_score,
                    'entity_score': entity_score
                })

        # 按评分排序
        candidates.sort(key=lambda x: x['score'], reverse=True)

        return candidates[:10]  # 返回前10个候选答案

    def score_by_question_type(self, sentence, question_type):
        """根据问题类型评分"""
        sentence_lower = sentence.lower()

        type_indicators = {
            'factual': ['is', 'was', 'are', 'were', 'called', 'named'],
            'definition': ['defined as', 'means', 'refers to', 'is a', 'is an'],
            'comparison': ['than', 'compared to', 'versus', 'while', 'whereas'],
            'procedural': ['first', 'then', 'next', 'finally', 'step'],
            'causal': ['because', 'due to', 'caused by', 'results in', 'leads to'],
            'quantitative': ['percent', 'million', 'billion', 'approximately', 'about']
        }

        if question_type in type_indicators:
            indicators = type_indicators[question_type]
            score = sum(1 for indicator in indicators if indicator in sentence_lower)
            return min(score / len(indicators), 1.0)

        return 0.5  # 默认评分

    def score_by_entity_match(self, sentence, question_entities):
        """根据实体匹配评分"""
        sentence_entities = self.extract_key_entities(sentence)

        total_matches = 0
        total_entities = 0

        for entity_type, entities in question_entities.items():
            if entities:
                total_entities += len(entities)
                sentence_entities_set = set(sentence_entities.get(entity_type, []))
                question_entities_set = set(entities)

                matches = len(sentence_entities_set & question_entities_set)
                total_matches += matches

        if total_entities == 0:
            return 0.5

        return total_matches / total_entities

    def generate_answer(self, question, candidates):
        """生成最终答案"""
        if not candidates:
            return {
                'answer': "I don't have enough information to answer this question.",
                'confidence': 0.0,
                'sources': []
            }

        # 选择最佳候选答案
        best_candidate = candidates[0]

        # 如果最佳答案的评分太低，返回不确定的回答
        if best_candidate['score'] < 0.3:
            return {
                'answer': "I'm not confident about the answer to this question.",
                'confidence': best_candidate['score'],
                'sources': [best_candidate['doc_id']]
            }

        # 答案后处理
        answer = self.post_process_answer(best_candidate['text'], question)

        # 收集支持证据
        supporting_evidence = []
        for candidate in candidates[:3]:  # 前3个候选答案作为支持证据
            if candidate['score'] > 0.2:
                supporting_evidence.append({
                    'text': candidate['text'],
                    'source': candidate['doc_id'],
                    'confidence': candidate['score']
                })

        return {
            'answer': answer,
            'confidence': best_candidate['score'],
            'sources': [c['doc_id'] for c in supporting_evidence],
            'supporting_evidence': supporting_evidence
        }

    def post_process_answer(self, raw_answer, question):
        """答案后处理"""
        # 去除多余的空格和标点
        answer = re.sub(r'\s+', ' ', raw_answer.strip())

        # 如果答案太长，尝试提取关键部分
        if len(answer) > 200:
            sentences = sent_tokenize(answer)
            if len(sentences) > 1:
                # 选择与问题最相关的句子
                question_processed = self.preprocess_text(question)
                best_sentence = ""
                best_similarity = 0

                for sentence in sentences:
                    sentence_processed = self.preprocess_text(sentence)

                    # 简单的词重叠相似度
                    question_words = set(question_processed.split())
                    sentence_words = set(sentence_processed.split())

                    if len(question_words) > 0:
                        similarity = len(question_words & sentence_words) / len(question_words)
                        if similarity > best_similarity:
                            best_similarity = similarity
                            best_sentence = sentence

                if best_sentence:
                    answer = best_sentence

        return answer

    def answer_question(self, question):
        """回答问题的主要接口"""
        # 1. 检索相关文档
        relevant_docs = self.retrieve_relevant_documents(question)

        # 2. 提取候选答案
        candidates = self.extract_candidate_answers(question, relevant_docs)

        # 3. 生成最终答案
        result = self.generate_answer(question, candidates)

        # 4. 添加问题分析信息
        result['question_type'] = self.classify_question_type(question)
        result['key_entities'] = self.extract_key_entities(question)

        return result

    def batch_answer_questions(self, questions):
        """批量回答问题"""
        results = []

        for question in questions:
            result = self.answer_question(question)
            results.append({
                'question': question,
                'result': result
            })

        return results

# 使用示例
def ibm_watson_qa_demo():
    # 创建问答系统
    watson_qa = IBMWatsonQASystem()

    # 构建知识库
    knowledge_documents = {
        'doc1': """
        Artificial Intelligence (AI) is the simulation of human intelligence in machines
        that are programmed to think like humans and mimic their actions. The term may
        also be applied to any machine that exhibits traits associated with a human mind
        such as learning and problem-solving.
        """,
        'doc2': """
        Machine Learning is a subset of artificial intelligence that provides systems
        the ability to automatically learn and improve from experience without being
        explicitly programmed. Machine learning focuses on the development of computer
        programs that can access data and use it to learn for themselves.
        """,
        'doc3': """
        Deep Learning is part of a broader family of machine learning methods based on
        artificial neural networks with representation learning. Learning can be
        supervised, semi-supervised or unsupervised. Deep learning architectures such
        as deep neural networks have been applied to fields including computer vision,
        speech recognition, and natural language processing.
        """
    }

    # 构建知识库
    num_docs = watson_qa.build_knowledge_base(knowledge_documents)
    print(f"=== IBM Watson问答系统演示 ===")
    print(f"知识库构建完成，包含 {num_docs} 个文档")

    # 测试问题
    test_questions = [
        "What is artificial intelligence?",
        "How does machine learning work?",
        "What are the applications of deep learning?",
        "What is the difference between AI and machine learning?"
    ]

    # 批量回答问题
    results = watson_qa.batch_answer_questions(test_questions)

    for i, result in enumerate(results):
        print(f"\n问题 {i+1}: {result['question']}")
        print(f"答案: {result['result']['answer']}")
        print(f"置信度: {result['result']['confidence']:.3f}")
        print(f"问题类型: {result['result']['question_type']}")
        print(f"来源文档: {result['result']['sources']}")

ibm_watson_qa_demo()
```

### 题目40：Oracle数据库机器学习
**公司来源**: Oracle
**难度**: ⭐⭐⭐⭐

**题目**: 设计Oracle数据库内置机器学习系统，实现SQL中的机器学习算法调用和模型管理。

**推荐解答**:
```python
import sqlite3
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, LabelEncoder
import pickle
import json
from datetime import datetime

class OracleMLSystem:
    def __init__(self, db_path="oracle_ml.db"):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path, check_same_thread=False)
        self.models = {}
        self.scalers = {}
        self.encoders = {}

        # 初始化数据库表
        self.init_ml_tables()

        # 支持的算法
        self.supported_algorithms = {
            'CLASSIFICATION': {
                'RANDOM_FOREST': RandomForestClassifier,
                'LOGISTIC_REGRESSION': LogisticRegression
            },
            'REGRESSION': {
                'RANDOM_FOREST': RandomForestRegressor,
                'LINEAR_REGRESSION': LinearRegression
            },
            'CLUSTERING': {
                'KMEANS': KMeans
            }
        }

    def init_ml_tables(self):
        """初始化机器学习相关表"""
        cursor = self.connection.cursor()

        # 模型元数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ML_MODELS (
                model_id TEXT PRIMARY KEY,
                model_name TEXT NOT NULL,
                algorithm_type TEXT NOT NULL,
                algorithm_name TEXT NOT NULL,
                target_column TEXT,
                feature_columns TEXT,
                model_data BLOB,
                scaler_data BLOB,
                encoder_data BLOB,
                performance_metrics TEXT,
                created_date TEXT,
                last_updated TEXT,
                status TEXT DEFAULT 'ACTIVE'
            )
        """)

        # 模型训练历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ML_TRAINING_HISTORY (
                training_id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id TEXT,
                training_data_query TEXT,
                hyperparameters TEXT,
                performance_metrics TEXT,
                training_time_seconds REAL,
                created_date TEXT,
                FOREIGN KEY (model_id) REFERENCES ML_MODELS (model_id)
            )
        """)

        # 预测结果表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ML_PREDICTIONS (
                prediction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id TEXT,
                input_data TEXT,
                prediction_result TEXT,
                confidence_score REAL,
                created_date TEXT,
                FOREIGN KEY (model_id) REFERENCES ML_MODELS (model_id)
            )
        """)

        self.connection.commit()

    def create_model(self, model_name, algorithm_type, algorithm_name, hyperparameters=None):
        """创建机器学习模型"""
        model_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        if algorithm_type not in self.supported_algorithms:
            raise ValueError(f"Unsupported algorithm type: {algorithm_type}")

        if algorithm_name not in self.supported_algorithms[algorithm_type]:
            raise ValueError(f"Unsupported algorithm: {algorithm_name}")

        # 创建模型实例
        algorithm_class = self.supported_algorithms[algorithm_type][algorithm_name]

        if hyperparameters:
            model = algorithm_class(**hyperparameters)
        else:
            model = algorithm_class()

        # 存储模型信息
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO ML_MODELS
            (model_id, model_name, algorithm_type, algorithm_name, created_date, status)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (model_id, model_name, algorithm_type, algorithm_name,
              datetime.now().isoformat(), 'CREATED'))

        self.connection.commit()
        self.models[model_id] = model

        return model_id

    def train_model(self, model_id, training_data_query, target_column, feature_columns=None):
        """训练模型"""
        start_time = datetime.now()

        # 获取训练数据
        training_data = pd.read_sql_query(training_data_query, self.connection)

        if target_column not in training_data.columns:
            raise ValueError(f"Target column '{target_column}' not found in training data")

        # 准备特征和目标变量
        if feature_columns is None:
            feature_columns = [col for col in training_data.columns if col != target_column]

        X = training_data[feature_columns]
        y = training_data[target_column]

        # 数据预处理
        X_processed, scaler, encoders = self.preprocess_features(X, model_id)
        y_processed, target_encoder = self.preprocess_target(y, model_id)

        # 获取模型
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not found")

        model = self.models[model_id]

        # 训练模型
        model.fit(X_processed, y_processed)

        # 计算性能指标
        performance_metrics = self.evaluate_model(model, X_processed, y_processed, model_id)

        # 序列化模型和预处理器
        model_data = pickle.dumps(model)
        scaler_data = pickle.dumps(scaler) if scaler else None
        encoder_data = pickle.dumps(encoders) if encoders else None

        # 更新数据库
        cursor = self.connection.cursor()

        # 更新模型表
        cursor.execute("""
            UPDATE ML_MODELS
            SET target_column = ?, feature_columns = ?, model_data = ?,
                scaler_data = ?, encoder_data = ?, performance_metrics = ?,
                last_updated = ?, status = ?
            WHERE model_id = ?
        """, (target_column, json.dumps(feature_columns), model_data,
              scaler_data, encoder_data, json.dumps(performance_metrics),
              datetime.now().isoformat(), 'TRAINED', model_id))

        # 记录训练历史
        training_time = (datetime.now() - start_time).total_seconds()
        cursor.execute("""
            INSERT INTO ML_TRAINING_HISTORY
            (model_id, training_data_query, performance_metrics, training_time_seconds, created_date)
            VALUES (?, ?, ?, ?, ?)
        """, (model_id, training_data_query, json.dumps(performance_metrics),
              training_time, datetime.now().isoformat()))

        self.connection.commit()

        return performance_metrics

    def predict(self, model_id, input_data_query=None, input_data=None):
        """使用模型进行预测"""
        # 获取模型信息
        cursor = self.connection.cursor()
        cursor.execute("SELECT * FROM ML_MODELS WHERE model_id = ?", (model_id,))
        model_info = cursor.fetchone()

        if not model_info:
            raise ValueError(f"Model {model_id} not found")

        # 反序列化模型和预处理器
        model = pickle.loads(model_info[6])  # model_data
        scaler = pickle.loads(model_info[7]) if model_info[7] else None  # scaler_data
        encoders = pickle.loads(model_info[8]) if model_info[8] else None  # encoder_data

        # 获取输入数据
        if input_data_query:
            input_data = pd.read_sql_query(input_data_query, self.connection)
        elif input_data is None:
            raise ValueError("Either input_data_query or input_data must be provided")

        # 预处理输入数据
        feature_columns = json.loads(model_info[5])  # feature_columns
        X = input_data[feature_columns]
        X_processed = self.apply_preprocessing(X, scaler, encoders)

        # 进行预测
        predictions = model.predict(X_processed)

        # 计算置信度（如果支持）
        confidence_scores = None
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(X_processed)
            confidence_scores = np.max(probabilities, axis=1)
        elif hasattr(model, 'decision_function'):
            decision_scores = model.decision_function(X_processed)
            confidence_scores = np.abs(decision_scores)

        # 存储预测结果
        results = []
        for i, pred in enumerate(predictions):
            confidence = confidence_scores[i] if confidence_scores is not None else None

            cursor.execute("""
                INSERT INTO ML_PREDICTIONS
                (model_id, input_data, prediction_result, confidence_score, created_date)
                VALUES (?, ?, ?, ?, ?)
            """, (model_id, json.dumps(X.iloc[i].to_dict()), str(pred),
                  confidence, datetime.now().isoformat()))

            results.append({
                'prediction': pred,
                'confidence': confidence,
                'input_data': X.iloc[i].to_dict()
            })

        self.connection.commit()

        return results

    def sql_ml_interface(self, sql_query):
        """SQL机器学习接口"""
        # 解析SQL中的机器学习函数
        if 'ML_TRAIN' in sql_query.upper():
            return self.parse_ml_train_query(sql_query)
        elif 'ML_PREDICT' in sql_query.upper():
            return self.parse_ml_predict_query(sql_query)
        elif 'ML_EVALUATE' in sql_query.upper():
            return self.parse_ml_evaluate_query(sql_query)
        else:
            # 普通SQL查询
            return pd.read_sql_query(sql_query, self.connection)

    def parse_ml_train_query(self, sql_query):
        """解析ML_TRAIN查询"""
        # 简化的SQL解析（实际实现会更复杂）
        # 示例: SELECT ML_TRAIN('model_name', 'CLASSIFICATION', 'RANDOM_FOREST', 'target_col') FROM training_table

        import re
        pattern = r"ML_TRAIN\s*\(\s*'([^']+)'\s*,\s*'([^']+)'\s*,\s*'([^']+)'\s*,\s*'([^']+)'\s*\)"
        match = re.search(pattern, sql_query, re.IGNORECASE)

        if match:
            model_name, algorithm_type, algorithm_name, target_column = match.groups()

            # 创建模型
            model_id = self.create_model(model_name, algorithm_type, algorithm_name)

            # 构建训练数据查询
            table_match = re.search(r'FROM\s+(\w+)', sql_query, re.IGNORECASE)
            if table_match:
                table_name = table_match.group(1)
                training_query = f"SELECT * FROM {table_name}"

                # 训练模型
                performance = self.train_model(model_id, training_query, target_column)

                return pd.DataFrame([{
                    'model_id': model_id,
                    'status': 'TRAINED',
                    'performance': json.dumps(performance)
                }])

        raise ValueError("Invalid ML_TRAIN query format")

    def parse_ml_predict_query(self, sql_query):
        """解析ML_PREDICT查询"""
        # 示例: SELECT ML_PREDICT('model_id', feature1, feature2) FROM test_table

        import re
        pattern = r"ML_PREDICT\s*\(\s*'([^']+)'\s*,"
        match = re.search(pattern, sql_query, re.IGNORECASE)

        if match:
            model_id = match.group(1)

            # 构建输入数据查询
            table_match = re.search(r'FROM\s+(\w+)', sql_query, re.IGNORECASE)
            if table_match:
                table_name = table_match.group(1)
                input_query = f"SELECT * FROM {table_name}"

                # 进行预测
                predictions = self.predict(model_id, input_data_query=input_query)

                return pd.DataFrame(predictions)

        raise ValueError("Invalid ML_PREDICT query format")

    # 辅助方法
    def preprocess_features(self, X, model_id):
        """特征预处理"""
        X_processed = X.copy()
        scaler = None
        encoders = {}

        # 数值特征标准化
        numeric_columns = X.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) > 0:
            scaler = StandardScaler()
            X_processed[numeric_columns] = scaler.fit_transform(X[numeric_columns])

        # 分类特征编码
        categorical_columns = X.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            encoder = LabelEncoder()
            X_processed[col] = encoder.fit_transform(X[col].astype(str))
            encoders[col] = encoder

        return X_processed, scaler, encoders

    def preprocess_target(self, y, model_id):
        """目标变量预处理"""
        if y.dtype == 'object':
            encoder = LabelEncoder()
            y_processed = encoder.fit_transform(y)
            return y_processed, encoder
        else:
            return y, None

    def apply_preprocessing(self, X, scaler, encoders):
        """应用预处理"""
        X_processed = X.copy()

        # 应用标准化
        if scaler:
            numeric_columns = X.select_dtypes(include=[np.number]).columns
            X_processed[numeric_columns] = scaler.transform(X[numeric_columns])

        # 应用编码
        if encoders:
            for col, encoder in encoders.items():
                if col in X_processed.columns:
                    X_processed[col] = encoder.transform(X_processed[col].astype(str))

        return X_processed

    def evaluate_model(self, model, X, y, model_id):
        """评估模型性能"""
        from sklearn.metrics import accuracy_score, mean_squared_error, r2_score

        predictions = model.predict(X)

        # 获取模型类型
        cursor = self.connection.cursor()
        cursor.execute("SELECT algorithm_type FROM ML_MODELS WHERE model_id = ?", (model_id,))
        algorithm_type = cursor.fetchone()[0]

        metrics = {}

        if algorithm_type == 'CLASSIFICATION':
            metrics['accuracy'] = accuracy_score(y, predictions)
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(X)
                # 可以添加更多分类指标
        elif algorithm_type == 'REGRESSION':
            metrics['mse'] = mean_squared_error(y, predictions)
            metrics['r2_score'] = r2_score(y, predictions)

        return metrics

# 使用示例
def oracle_ml_demo():
    # 创建Oracle ML系统
    oracle_ml = OracleMLSystem()

    # 创建示例数据表
    cursor = oracle_ml.connection.cursor()

    # 创建训练数据表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS customer_data (
            customer_id INTEGER,
            age INTEGER,
            income REAL,
            education TEXT,
            purchased INTEGER
        )
    """)

    # 插入示例数据
    sample_data = [
        (1, 25, 50000, 'Bachelor', 1),
        (2, 35, 75000, 'Master', 1),
        (3, 45, 60000, 'Bachelor', 0),
        (4, 30, 80000, 'PhD', 1),
        (5, 50, 45000, 'High School', 0)
    ]

    cursor.executemany("""
        INSERT OR REPLACE INTO customer_data
        (customer_id, age, income, education, purchased)
        VALUES (?, ?, ?, ?, ?)
    """, sample_data)

    oracle_ml.connection.commit()

    print("=== Oracle数据库机器学习演示 ===")

    # 1. 使用SQL接口训练模型
    train_sql = """
        SELECT ML_TRAIN('customer_model', 'CLASSIFICATION', 'RANDOM_FOREST', 'purchased')
        FROM customer_data
    """

    try:
        train_result = oracle_ml.sql_ml_interface(train_sql)
        print("模型训练结果:")
        print(train_result)

        # 2. 使用SQL接口进行预测
        predict_sql = """
            SELECT ML_PREDICT('customer_model_20250119_000000', age, income, education)
            FROM customer_data
        """

        # 注意：实际的model_id会根据创建时间生成
        # 这里需要从训练结果中获取实际的model_id

        print("\n模型预测功能已实现，可以通过SQL接口调用")

    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        print("这是一个简化的演示实现")

oracle_ml_demo()
```

---

# 第十部分：全球大厂覆盖确认清单

## 10.1 已包含公司完整清单

### 🇺🇸 美国科技巨头
✅ **Google** (题目1, 2, 5, 10, 14, 16, 22) - 7道题目
- 矩阵分解应用、特征值意义、梯度下降收敛性、激活函数选择、YouTube推荐系统、Google搜索引擎、技术挑战处理

✅ **Meta (Facebook)** (题目5, 8, 11, 15, 24, 29) - 6道题目
- 梯度下降收敛性、聚类算法选择、批量归一化、实时广告竞价、拓扑排序应用、微信朋友圈排序

✅ **Amazon** (题目7, 15, 22, 25) - 4道题目
- 特征选择方法、实时广告竞价、团队协作经验、背包问题变种

✅ **Microsoft** (题目2, 11, 24) - 3道题目
- 特征值意义、批量归一化、拓扑排序应用

✅ **Apple** (题目2, 7, 25) - 3道题目
- 特征值意义、特征选择方法、背包问题变种

✅ **OpenAI** (题目3, 13) - 2道题目
- 贝叶斯定理应用、LSTM vs GRU vs Transformer

✅ **NVIDIA** (题目3, 8, 38) - 3道题目
- 贝叶斯定理应用、聚类算法选择、GPU并行计算优化

✅ **Netflix** (题目31) - 1道题目
- 个性化推荐系统

✅ **Uber** (题目32) - 1道题目
- 动态定价算法

✅ **Spotify** (题目33) - 1道题目
- 音乐推荐算法

✅ **Airbnb** (题目34) - 1道题目
- 搜索排序算法

✅ **LinkedIn** (题目35) - 1道题目
- 职位推荐算法

✅ **Pinterest** (题目36) - 1道题目
- 视觉搜索算法

✅ **Tesla** (题目37) - 1道题目
- 自动驾驶感知算法

✅ **IBM** (题目39) - 1道题目
- Watson自然语言处理

✅ **Oracle** (题目40) - 1道题目
- 数据库机器学习

### 🇨🇳 中国科技巨头
✅ **字节跳动 (ByteDance)** (题目26, 27) - 2道题目
- 抖音推荐算法、今日头条新闻分类

✅ **阿里巴巴 (Alibaba)** (题目28) - 1道题目
- 淘宝商品搜索排序

✅ **腾讯 (Tencent)** (题目29, 30) - 2道题目
- 微信朋友圈信息流排序、王者荣耀匹配算法

## 10.2 需要补充的重要大厂

### 🔍 搜索发现遗漏的重要公司

经过全面搜索和分析，以下公司需要补充：

#### 硬件芯片类
- **Intel** - CPU AI加速、边缘计算
- **AMD** - GPU计算、ROCm平台
- **Qualcomm** - 移动AI、边缘推理
- **ARM** - 移动处理器AI优化

#### 中国科技公司
- **百度 (Baidu)** - 搜索算法、自动驾驶
- **华为 (Huawei)** - 移动AI、云计算
- **小米 (Xiaomi)** - IoT设备AI
- **美团 (Meituan)** - 配送优化、推荐系统

#### 其他重要公司
- **Salesforce** - CRM智能化、Einstein AI
- **Adobe** - 创意AI、图像处理
- **Shopify** - 电商推荐、智能营销

## 10.3 覆盖度分析

### 📊 当前覆盖统计
- **总题目数**: 40道
- **涵盖公司**: 18家
- **美国公司**: 15家 (83%)
- **中国公司**: 3家 (17%)
- **题目分布**:
  - 推荐系统: 8道题目 (20%)
  - 搜索排序: 6道题目 (15%)
  - 计算机视觉: 4道题目 (10%)
  - 自然语言处理: 4道题目 (10%)
  - 系统优化: 6道题目 (15%)
  - 算法基础: 12道题目 (30%)

### 🎯 覆盖质量评估

#### ✅ 优势领域
1. **互联网大厂**: Google, Meta, Amazon等核心公司全覆盖
2. **推荐系统**: Netflix, Spotify, LinkedIn等专业领域深度覆盖
3. **硬件优化**: Tesla, NVIDIA等前沿技术覆盖
4. **中国市场**: 字节跳动、阿里巴巴、腾讯三大巨头覆盖

#### 📈 补充建议
1. **芯片硬件**: 可补充Intel、AMD、Qualcomm相关题目
2. **企业服务**: 可补充Salesforce、Adobe等B端公司
3. **新兴领域**: 可补充更多AI创业公司的特色算法

## 10.4 面试准备建议

### 🎯 按公司类型准备

#### **互联网平台类** (Google, Meta, Amazon)
- **重点**: 大规模系统设计、分布式算法
- **核心技能**: 推荐系统、搜索排序、广告算法
- **准备策略**: 重点练习系统设计和算法优化

#### **专业服务类** (Netflix, Spotify, Uber)
- **重点**: 垂直领域专业算法
- **核心技能**: 个性化推荐、实时优化、用户行为分析
- **准备策略**: 深入理解业务场景和算法应用

#### **硬件芯片类** (Tesla, NVIDIA, Intel)
- **重点**: 硬件优化、并行计算、边缘AI
- **核心技能**: GPU编程、模型压缩、实时推理
- **准备策略**: 关注性能优化和硬件适配

#### **中国科技类** (字节跳动, 阿里巴巴, 腾讯)
- **重点**: 本土化应用、大规模用户服务
- **核心技能**: 推荐算法、搜索技术、社交网络
- **准备策略**: 了解中国市场特点和技术栈

### 📚 学习路径建议

#### **基础阶段** (1-2个月)
1. 数学基础: 线性代数、概率统计、优化理论
2. 机器学习: 监督学习、无监督学习、模型评估
3. 深度学习: 神经网络、CNN、RNN基础

#### **进阶阶段** (2-3个月)
1. 系统设计: 推荐系统、搜索引擎、广告系统
2. 算法优化: 分布式训练、模型压缩、推理加速
3. 实际项目: 端到端项目经验、A/B测试

#### **冲刺阶段** (1个月)
1. 公司特色: 针对目标公司的特色算法深入学习
2. 面试模拟: 完整面试流程练习
3. 项目准备: 准备详细的项目介绍和技术细节

---

# 第十一部分：面试技巧与准备策略

## 11.1 技术面试应对策略

### 策略1：问题分析框架
**适用场景**: 所有技术问题

**STAR-T框架**:
- **S (Situation)**: 理解问题背景和约束条件
- **T (Task)**: 明确要解决的具体任务
- **A (Approach)**: 设计解决方案和算法
- **R (Result)**: 实现代码并分析复杂度
- **T (Test)**: 测试和优化方案

**实际应用示例**:
```
面试官: "设计一个LRU缓存"

回答框架:
S: "我需要了解一下具体需求：
   - 缓存容量是多少？
   - 需要支持哪些操作？
   - 对时间复杂度有什么要求？"

T: "设计一个支持get和put操作的LRU缓存，
   要求时间复杂度O(1)"

A: "我考虑使用哈希表+双向链表的组合：
   - 哈希表提供O(1)查找
   - 双向链表维护访问顺序
   - 头部是最近访问，尾部是最久未访问"

R: [实现代码]

T: "测试边界情况：
   - 容量为0或1的情况
   - 重复插入相同key
   - 访问不存在的key"
```

### 策略2：代码实现最佳实践

**代码质量检查清单**:
- [ ] 变量命名清晰有意义
- [ ] 函数功能单一，职责明确
- [ ] 边界条件处理完整
- [ ] 错误处理机制
- [ ] 时间空间复杂度分析
- [ ] 代码可读性和可维护性

**示例对比**:
```python
# 不好的实现
def f(a, b):
    r = []
    for i in range(len(a)):
        if a[i] in b:
            r.append(a[i])
    return r

# 好的实现
def find_common_elements(list1, list2):
    """
    找到两个列表的公共元素

    Args:
        list1: 第一个列表
        list2: 第二个列表

    Returns:
        包含公共元素的列表

    Time Complexity: O(n + m)
    Space Complexity: O(min(n, m))
    """
    if not list1 or not list2:
        return []

    set2 = set(list2)  # O(m) 时间和空间
    common_elements = []

    for element in list1:  # O(n) 时间
        if element in set2:  # O(1) 平均时间
            common_elements.append(element)

    return common_elements
```

### 策略3：系统设计思路

**系统设计步骤**:
1. **需求澄清** (5分钟)
   - 功能需求：核心功能有哪些？
   - 非功能需求：QPS、延迟、一致性要求？
   - 规模估算：用户数、数据量、增长率？

2. **高层设计** (10分钟)
   - 画出主要组件
   - 定义API接口
   - 选择数据存储方案

3. **详细设计** (15分钟)
   - 数据库设计
   - 算法选择
   - 缓存策略
   - 负载均衡

4. **扩展讨论** (10分钟)
   - 性能优化
   - 可靠性保证
   - 监控告警
   - 安全考虑

## 11.2 行为面试准备

### 常见行为面试问题及回答策略

**问题1: "描述一次你解决技术难题的经历"**

**回答模板**:
```
背景: 在[项目名称]中，我们遇到了[具体技术问题]
挑战: 这个问题的难点在于[技术难点分析]
行动: 我采取了以下步骤：
      1. [问题分析和调研]
      2. [方案设计和选择]
      3. [实施和测试]
      4. [优化和改进]
结果: 最终[具体成果和数据]，并且[长期影响]
反思: 从这次经历中我学到了[经验教训]
```

**问题2: "如何处理与团队成员的技术分歧？"**

**回答要点**:
- 展示沟通能力和团队合作精神
- 强调数据驱动的决策过程
- 体现开放心态和学习能力
- 说明如何达成共识

**问题3: "为什么选择我们公司？"**

**准备策略**:
- 研究公司技术栈和业务
- 了解公司文化和价值观
- 准备具体的理由和例子
- 展示对公司发展的关注

## 11.3 不同公司面试特点

### Google面试特点
- **重点**: 算法和数据结构基础
- **风格**: 开放性问题，注重思维过程
- **准备**: LeetCode Hard题目，系统设计
- **建议**: 大声思考，展示解题思路

### Meta面试特点
- **重点**: 系统设计和工程能力
- **风格**: 实际业务场景问题
- **准备**: 大规模系统设计，产品思维
- **建议**: 关注用户体验和业务影响

### Amazon面试特点
- **重点**: Leadership Principles
- **风格**: 行为面试比重大
- **准备**: STAR方法，具体事例
- **建议**: 准备多个不同类型的项目经历

### 字节跳动面试特点
- **重点**: 算法能力和工程实践
- **风格**: 快节奏，多轮技术面试
- **准备**: 算法竞赛题目，实际项目经验
- **建议**: 展示学习能力和适应性

## 11.4 面试前最后准备

### 技术知识复习清单

**数学基础** (1-2天):
- [ ] 线性代数：矩阵运算、特征值分解
- [ ] 概率统计：贝叶斯定理、分布函数
- [ ] 微积分：梯度、偏导数、链式法则

**机器学习** (2-3天):
- [ ] 监督学习：线性回归、逻辑回归、SVM、决策树
- [ ] 无监督学习：K-means、PCA、聚类评估
- [ ] 模型评估：交叉验证、偏差方差、过拟合

**深度学习** (2-3天):
- [ ] 神经网络：前向传播、反向传播、激活函数
- [ ] CNN：卷积、池化、经典架构
- [ ] RNN：LSTM、GRU、序列建模

**算法与数据结构** (3-4天):
- [ ] 排序算法：快排、归并、堆排序
- [ ] 搜索算法：二分查找、DFS、BFS
- [ ] 动态规划：背包问题、最长子序列
- [ ] 图算法：最短路径、拓扑排序

### 模拟面试练习

**练习计划**:
- **第1周**: 每天1道算法题 + 1个概念复习
- **第2周**: 每天2道算法题 + 系统设计练习
- **第3周**: 模拟完整面试流程
- **面试前3天**: 复习错题和重点概念

**练习资源**:
- LeetCode: 算法题练习
- Pramp: 模拟面试平台
- InterviewBit: 系统化面试准备
- Glassdoor: 公司面试经验分享

### 心理准备和状态调整

**面试心态**:
- 保持自信但不自负
- 承认不知道的问题
- 展示学习能力和思考过程
- 把面试当作技术交流

**压力管理**:
- 充足睡眠和适度运动
- 提前熟悉面试环境
- 准备备选方案
- 正确看待面试结果

---

# 总结与建议

## 最终建议

### 技术能力提升
1. **扎实基础**: 数学、算法、数据结构是根本
2. **实践经验**: 多做项目，积累实际经验
3. **持续学习**: 关注技术发展，保持学习热情
4. **系统思维**: 培养全局观和工程思维

### 面试成功要素
1. **充分准备**: 技术知识、项目经历、公司研究
2. **清晰表达**: 逻辑清楚、条理分明
3. **积极态度**: 展示热情和学习能力
4. **团队合作**: 体现沟通能力和协作精神

### 长期职业发展
1. **技术深度**: 在某个领域成为专家
2. **技术广度**: 了解相关技术栈
3. **软技能**: 沟通、领导、项目管理
4. **行业洞察**: 关注技术趋势和商业应用

**记住：面试只是职业生涯的一个节点，重要的是持续成长和价值创造！**

**祝你面试成功，前程似锦！** 🚀✨

---

> **声明**: 本面试题库基于公开信息整理，题目来源于各大公司官方发布、面试经验分享和技术社区讨论。所有解答方案仅供参考，实际面试中请根据具体情况灵活应对。
```

**祝你面试成功！** 🚀

> **最后提醒**: 面试不仅是技术能力的考察，更是综合素质的体现。保持自信，展示你的学习能力和解决问题的思维过程。记住，面试官也希望你成功！记住，面试官也希望你成功！
