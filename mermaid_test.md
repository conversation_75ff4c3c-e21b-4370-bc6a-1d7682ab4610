# Mermaid图表测试

## 测试1: Timeline图表

```mermaid
timeline
    title 大语言模型发展历程
    
    2018 : BERT发布
         : Transformer架构确立
    
    2019 : GPT-2展示规模效应
         : 1.5B参数模型
    
    2020 : GPT-3证明规模定律
         : 175B参数
```

## 测试2: 流程图

```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作1]
    B -->|否| D[执行操作2]
    C --> E[结束]
    D --> E
    
    style A fill:#e8f5e8
    style E fill:#e3f2fd
```

## 测试3: 序列图

```mermaid
sequenceDiagram
    participant A as GPU0
    participant B as GPU1
    
    A->>B: 发送数据
    B->>A: 返回结果
    
    Note over A,B: 通信完成
```

## 测试4: 子图结构

```mermaid
graph TB
    subgraph "系统架构"
        subgraph "应用层"
            A1[应用1]
            A2[应用2]
        end
        
        subgraph "服务层"
            B1[服务1]
            B2[服务2]
        end
        
        A1 --> B1
        A2 --> B2
        
        style A1 fill:#e8f5e8
        style B1 fill:#fff3e0
    end
```

## 测试5: 复杂图表

```mermaid
graph LR
    subgraph "ZeRO优化阶段"
        A[ZeRO-1<br/>优化器分片] --> B[ZeRO-2<br/>梯度分片]
        B --> C[ZeRO-3<br/>参数分片]
        
        A --> A1[内存节省: 2x]
        B --> B1[内存节省: 4x]
        C --> C1[内存节省: 64x]
        
        style A fill:#ffcdd2
        style B fill:#fff3e0
        style C fill:#e8f5e8
    end
```

所有图表测试完成！如果这些图表能正确渲染，说明文档中的Mermaid语法都是正确的。
