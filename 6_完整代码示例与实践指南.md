# LLM训练完整代码示例与实践指南

> 📖 **术语说明**: 本文档中涉及的技术术语和缩略词请参考 [技术术语表.md](./技术术语表.md)

## 📋 目录

1. [环境配置与依赖安装](#1-环境配置与依赖安装)
2. [端到端训练流水线](#2-端到端训练流水线)
3. [生产级部署方案](#3-生产级部署方案)
4. [性能优化实践](#4-性能优化实践)
5. [故障排除指南](#5-故障排除指南)

## 🎯 学习目标

通过本文档，您将获得：
- **环境搭建**: 完整的开发和生产环境配置方案
- **代码实践**: 可直接运行的端到端训练代码
- **部署经验**: 生产级模型部署的最佳实践
- **优化技巧**: 性能调优和资源优化的实用方法
- **问题解决**: 常见问题的诊断和解决方案

---

## 1. 环境配置与依赖安装

### 1.1 环境配置概述

**为什么需要专门的环境配置？**

大语言模型训练对环境有严格要求，包括：

1. **CUDA版本兼容性**: 不同的深度学习框架对CUDA版本有特定要求
2. **Python依赖管理**: 复杂的依赖关系需要精确的版本控制
3. **系统资源优化**: 需要针对GPU、内存、存储进行系统级优化
4. **可重现性**: 确保在不同机器上能够重现相同的训练结果

**环境配置的最佳实践**

1. **容器化部署**: 使用Docker确保环境一致性
2. **版本锁定**: 精确指定所有依赖的版本号
3. **分层构建**: 将基础环境和应用环境分离
4. **资源监控**: 集成监控工具便于调试和优化

### 1.1 Docker环境配置

**Docker配置详解**

以下Dockerfile展示了如何构建一个完整的LLM训练环境。每个步骤都有其特定的目的：

```dockerfile
# Dockerfile for LLM Training Environment
# 基础镜像选择：使用NVIDIA官方CUDA镜像确保GPU支持
FROM nvidia/cuda:12.1-devel-ubuntu22.04

# 环境变量设置
# DEBIAN_FRONTEND=noninteractive: 避免安装过程中的交互提示
# PYTHONUNBUFFERED=1: 确保Python输出实时显示，便于调试
# CUDA_HOME: 指定CUDA安装路径，某些库编译时需要
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# 系统依赖安装
# 选择这些包的原因：
# - python3.10: 推荐的Python版本，兼容性好
# - python3.10-dev: 编译某些Python包时需要
# - git: 版本控制和下载代码
# - wget/curl: 下载数据和模型
# - vim: 文本编辑
# - htop: 系统监控
# - tmux: 会话管理，长时间训练必备
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3-pip \
    git \
    wget \
    curl \
    vim \
    htop \
    tmux \
    build-essential \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# 创建符号链接
RUN ln -s /usr/bin/python3.10 /usr/bin/python

# 升级pip
RUN python -m pip install --upgrade pip

# 安装PyTorch (CUDA 12.1)
RUN pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu121

# 安装核心训练库
RUN pip install \
    transformers==4.36.0 \
    datasets==2.14.0 \
    tokenizers==0.15.0 \
    accelerate==0.24.0 \
    peft==0.7.0 \
    trl==0.7.0 \
    deepspeed==0.12.0 \
    wandb==0.16.0 \
    tensorboard==2.15.0

# 安装额外工具
RUN pip install \
    numpy==1.24.0 \
    pandas==2.1.0 \
    scikit-learn==1.3.0 \
    matplotlib==3.7.0 \
    seaborn==0.12.0 \
    jupyter==1.0.0 \
    ipywidgets==8.1.0

# 设置工作目录
WORKDIR /workspace

# 复制训练脚本
COPY . /workspace/

# 设置权限
RUN chmod +x /workspace/scripts/*.sh

# 暴露端口
EXPOSE 8888 6006

# 启动命令
CMD ["bash"]
```

### 1.2 Conda环境配置

```bash
#!/bin/bash
# setup_environment.sh

# 创建conda环境
conda create -n llm-training python=3.10 -y
conda activate llm-training

# 安装CUDA工具包
conda install -c nvidia cuda-toolkit=12.1 -y

# 安装PyTorch
pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu121

# 安装训练框架
pip install transformers[torch]==4.36.0
pip install datasets==2.14.0
pip install accelerate==0.24.0
pip install peft==0.7.0
pip install trl==0.7.0
pip install deepspeed==0.12.0

# 安装监控工具
pip install wandb==0.16.0
pip install tensorboard==2.15.0

# 安装开发工具
pip install jupyter==1.0.0
pip install black==23.0.0
pip install flake8==6.0.0
pip install pytest==7.4.0

# 验证安装
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
python -c "import torch; print(f'GPU数量: {torch.cuda.device_count()}')"

echo "环境配置完成！"
```

### 1.3 依赖版本兼容性矩阵

**为什么需要版本兼容性检查？**

在LLM训练中，不同库之间的版本兼容性至关重要：

1. **API变化**: 新版本可能引入破坏性变化
2. **性能差异**: 不同版本的性能表现可能差异很大
3. **Bug修复**: 某些版本可能包含影响训练的bug
4. **功能支持**: 新功能可能只在特定版本中可用

**兼容性检查的重要性**

- **避免运行时错误**: 提前发现版本冲突
- **确保最佳性能**: 使用经过验证的版本组合
- **简化调试过程**: 排除版本问题导致的bug
- **提高可重现性**: 确保在不同环境中的一致性

```python
# requirements_compatibility.py
"""
LLM训练依赖版本兼容性检查

这个脚本的作用：
1. 检查当前环境中已安装包的版本
2. 验证版本是否在推荐范围内
3. 识别潜在的兼容性问题
4. 提供升级或降级建议

使用方法：
python requirements_compatibility.py
"""

import sys
import importlib
from typing import Dict, List, Tuple
import pkg_resources
from packaging import version

class CompatibilityChecker:
    """
    依赖兼容性检查器

    这个类维护了一个详细的版本兼容性矩阵，包括：
    - 最小支持版本
    - 推荐版本
    - 最大测试版本
    - 已知问题版本
    """

    def __init__(self):
        # 版本兼容性矩阵
        # 这些版本组合经过了大量测试，确保稳定性和性能
        self.requirements = {
            # PyTorch生态系统
            "torch": {
                "min_version": "2.0.0",  # 支持编译优化和新特性
                "recommended": "2.1.0",  # 最佳性能和稳定性
                "max_version": "2.2.0",  # 最新测试版本
                "known_issues": ["2.0.1"],  # 已知有bug的版本
                "description": "深度学习核心框架"
            },
            "transformers": {
                "min_version": "4.30.0",
                "recommended": "4.36.0",
                "max_version": "4.40.0"
            },
            "accelerate": {
                "min_version": "0.20.0",
                "recommended": "0.24.0",
                "max_version": "0.30.0"
            },
            "deepspeed": {
                "min_version": "0.10.0",
                "recommended": "0.12.0",
                "max_version": "0.15.0"
            },
            # PEFT相关
            "peft": {
                "min_version": "0.5.0",
                "recommended": "0.7.0",
                "max_version": "0.10.0"
            },
            "trl": {
                "min_version": "0.5.0",
                "recommended": "0.7.0",
                "max_version": "0.10.0"
            },
            # 数据处理
            "datasets": {
                "min_version": "2.10.0",
                "recommended": "2.14.0",
                "max_version": "2.20.0"
            },
            "tokenizers": {
                "min_version": "0.13.0",
                "recommended": "0.15.0",
                "max_version": "0.20.0"
            }
        }
    
    def check_version(self, package: str, version: str) -> Tuple[bool, str]:
        """检查单个包版本"""
        if package not in self.requirements:
            return True, f"{package}: 未在检查列表中"
        
        req = self.requirements[package]
        
        try:
            from packaging import version as pkg_version
            
            current = pkg_version.parse(version)
            min_ver = pkg_version.parse(req["min_version"])
            max_ver = pkg_version.parse(req["max_version"])
            recommended = pkg_version.parse(req["recommended"])
            
            if current < min_ver:
                return False, f"{package}: 版本过低 ({version} < {req['min_version']})"
            elif current > max_ver:
                return False, f"{package}: 版本过高 ({version} > {req['max_version']})"
            elif current == recommended:
                return True, f"{package}: 推荐版本 ({version})"
            else:
                return True, f"{package}: 兼容版本 ({version})"
                
        except Exception as e:
            return False, f"{package}: 版本检查失败 - {str(e)}"
    
    def check_all_dependencies(self) -> Dict[str, Tuple[bool, str]]:
        """检查所有依赖"""
        results = {}
        
        for package in self.requirements.keys():
            try:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
                results[package] = self.check_version(package, version)
            except ImportError:
                results[package] = (False, f"{package}: 未安装")
        
        return results
    
    def print_compatibility_report(self):
        """打印兼容性报告"""
        print("=" * 60)
        print("LLM训练环境兼容性检查报告")
        print("=" * 60)
        
        results = self.check_all_dependencies()
        
        compatible_count = 0
        total_count = len(results)
        
        for package, (is_compatible, message) in results.items():
            status = "✅" if is_compatible else "❌"
            print(f"{status} {message}")
            
            if is_compatible:
                compatible_count += 1
        
        print("=" * 60)
        print(f"兼容性总结: {compatible_count}/{total_count} 包兼容")
        
        if compatible_count == total_count:
            print("🎉 所有依赖都兼容，可以开始训练！")
        else:
            print("⚠️  存在兼容性问题，请更新相关包")
        
        return compatible_count == total_count

# 使用示例
if __name__ == "__main__":
    checker = CompatibilityChecker()
    checker.print_compatibility_report()
```

---

## 2. 端到端训练流水线

### 2.1 完整训练脚本

```python
#!/usr/bin/env python3
# train_llm_complete.py
"""
LLM端到端训练流水线
支持多种微调方法和分布式训练
"""

import os
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union

import torch
import torch.distributed as dist
from torch.utils.data import DataLoader
import transformers
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset, load_dataset
from peft import (
    get_peft_model,
    LoraConfig,
    TaskType,
    PeftModel
)
import wandb

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LLMTrainingPipeline:
    """LLM训练流水线"""
    
    def __init__(self, config_path: str):
        """初始化训练流水线"""
        self.config = self.load_config(config_path)
        self.setup_environment()
        self.model = None
        self.tokenizer = None
        self.trainer = None
    
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证必要配置
        required_keys = ['model', 'data', 'training', 'output']
        for key in required_keys:
            if key not in config:
                raise ValueError(f"配置文件缺少必要字段: {key}")
        
        return config
    
    def setup_environment(self):
        """设置训练环境"""
        # 设置随机种子
        seed = self.config.get('seed', 42)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
        
        # 设置CUDA
        if torch.cuda.is_available():
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
        
        # 初始化分布式训练
        if 'RANK' in os.environ:
            dist.init_process_group(backend='nccl')
            local_rank = int(os.environ['LOCAL_RANK'])
            torch.cuda.set_device(local_rank)
        
        # 创建输出目录
        output_dir = Path(self.config['output']['dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化wandb
        if self.config.get('wandb', {}).get('enabled', False):
            wandb.init(
                project=self.config['wandb']['project'],
                name=self.config['wandb'].get('name', f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                config=self.config
            )
    
    def load_model_and_tokenizer(self):
        """加载模型和分词器"""
        model_config = self.config['model']
        
        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_config['name'],
            trust_remote_code=model_config.get('trust_remote_code', False)
        )
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 加载模型
        model_kwargs = {
            'torch_dtype': getattr(torch, model_config.get('torch_dtype', 'float16')),
            'device_map': model_config.get('device_map', 'auto'),
            'trust_remote_code': model_config.get('trust_remote_code', False)
        }
        
        self.model = AutoModelForCausalLM.from_pretrained(
            model_config['name'],
            **model_kwargs
        )
        
        # 应用PEFT
        if model_config.get('use_peft', False):
            peft_config = LoraConfig(
                task_type=TaskType.CAUSAL_LM,
                inference_mode=False,
                r=model_config['peft']['r'],
                lora_alpha=model_config['peft']['lora_alpha'],
                lora_dropout=model_config['peft']['lora_dropout'],
                target_modules=model_config['peft']['target_modules']
            )
            self.model = get_peft_model(self.model, peft_config)
            self.model.print_trainable_parameters()
        
        logger.info(f"模型加载完成: {model_config['name']}")
    
    def prepare_dataset(self):
        """准备训练数据"""
        data_config = self.config['data']
        
        # 加载数据集
        if data_config['type'] == 'huggingface':
            dataset = load_dataset(
                data_config['name'],
                split=data_config.get('split', 'train')
            )
        elif data_config['type'] == 'local':
            dataset = Dataset.from_json(data_config['path'])
        else:
            raise ValueError(f"不支持的数据类型: {data_config['type']}")
        
        # 数据预处理
        def preprocess_function(examples):
            # 格式化文本
            texts = []
            for i in range(len(examples[data_config['text_column']])):
                if data_config.get('format_template'):
                    text = data_config['format_template'].format(
                        **{k: examples[k][i] for k in examples.keys()}
                    )
                else:
                    text = examples[data_config['text_column']][i]
                texts.append(text)
            
            # 分词
            tokenized = self.tokenizer(
                texts,
                truncation=True,
                padding=False,
                max_length=data_config.get('max_length', 512),
                return_tensors=None
            )
            
            # 设置labels
            tokenized['labels'] = tokenized['input_ids'].copy()
            
            return tokenized
        
        # 应用预处理
        tokenized_dataset = dataset.map(
            preprocess_function,
            batched=True,
            remove_columns=dataset.column_names,
            num_proc=data_config.get('num_proc', 4)
        )
        
        # 划分训练/验证集
        if data_config.get('validation_split', 0) > 0:
            split_dataset = tokenized_dataset.train_test_split(
                test_size=data_config['validation_split'],
                seed=self.config.get('seed', 42)
            )
            self.train_dataset = split_dataset['train']
            self.eval_dataset = split_dataset['test']
        else:
            self.train_dataset = tokenized_dataset
            self.eval_dataset = None
        
        logger.info(f"数据集准备完成: 训练集 {len(self.train_dataset)} 样本")
        if self.eval_dataset:
            logger.info(f"验证集: {len(self.eval_dataset)} 样本")
    
    def setup_trainer(self):
        """设置训练器"""
        training_config = self.config['training']
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=self.config['output']['dir'],
            overwrite_output_dir=True,
            
            # 训练设置
            num_train_epochs=training_config['num_epochs'],
            per_device_train_batch_size=training_config['batch_size'],
            per_device_eval_batch_size=training_config.get('eval_batch_size', training_config['batch_size']),
            gradient_accumulation_steps=training_config.get('gradient_accumulation_steps', 1),
            
            # 优化器设置
            learning_rate=training_config['learning_rate'],
            weight_decay=training_config.get('weight_decay', 0.01),
            warmup_steps=training_config.get('warmup_steps', 100),
            
            # 日志和保存
            logging_steps=training_config.get('logging_steps', 10),
            save_steps=training_config.get('save_steps', 500),
            eval_steps=training_config.get('eval_steps', 500),
            evaluation_strategy="steps" if self.eval_dataset else "no",
            save_strategy="steps",
            
            # 其他设置
            load_best_model_at_end=True if self.eval_dataset else False,
            metric_for_best_model="eval_loss" if self.eval_dataset else None,
            greater_is_better=False,
            
            # 硬件优化
            fp16=training_config.get('fp16', True),
            bf16=training_config.get('bf16', False),
            gradient_checkpointing=training_config.get('gradient_checkpointing', True),
            dataloader_num_workers=training_config.get('num_workers', 4),
            
            # 分布式设置
            ddp_find_unused_parameters=False,
            
            # 报告设置
            report_to="wandb" if self.config.get('wandb', {}).get('enabled', False) else None,
            run_name=self.config.get('wandb', {}).get('name'),
            
            # DeepSpeed
            deepspeed=training_config.get('deepspeed_config')
        )
        
        # 数据整理器
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False
        )
        
        # 创建训练器
        self.trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=self.train_dataset,
            eval_dataset=self.eval_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator
        )
        
        logger.info("训练器设置完成")
    
    def train(self):
        """开始训练"""
        logger.info("开始训练...")
        
        # 训练
        train_result = self.trainer.train()
        
        # 保存模型
        self.trainer.save_model()
        self.trainer.save_state()
        
        # 保存训练指标
        metrics = train_result.metrics
        self.trainer.log_metrics("train", metrics)
        self.trainer.save_metrics("train", metrics)
        
        # 最终评估
        if self.eval_dataset:
            eval_metrics = self.trainer.evaluate()
            self.trainer.log_metrics("eval", eval_metrics)
            self.trainer.save_metrics("eval", eval_metrics)
        
        logger.info("训练完成！")
        
        return train_result
    
    def run(self):
        """运行完整训练流水线"""
        try:
            logger.info("开始LLM训练流水线")
            
            # 1. 加载模型和分词器
            self.load_model_and_tokenizer()
            
            # 2. 准备数据集
            self.prepare_dataset()
            
            # 3. 设置训练器
            self.setup_trainer()
            
            # 4. 开始训练
            result = self.train()
            
            logger.info("训练流水线完成")
            return result
            
        except Exception as e:
            logger.error(f"训练过程中出现错误: {str(e)}")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LLM训练流水线")
    parser.add_argument(
        "--config",
        type=str,
        required=True,
        help="配置文件路径"
    )
    
    args = parser.parse_args()
    
    # 创建并运行训练流水线
    pipeline = LLMTrainingPipeline(args.config)
    pipeline.run()

if __name__ == "__main__":
    main()
```

### 2.2 配置文件模板

```json
{
  "model": {
    "name": "meta-llama/Llama-2-7b-hf",
    "torch_dtype": "float16",
    "device_map": "auto",
    "trust_remote_code": false,
    "use_peft": true,
    "peft": {
      "r": 16,
      "lora_alpha": 32,
      "lora_dropout": 0.1,
      "target_modules": ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    }
  },
  "data": {
    "type": "local",
    "path": "data/instruction_data.json",
    "text_column": "text",
    "format_template": "### Instruction:\n{instruction}\n\n### Response:\n{output}",
    "max_length": 2048,
    "validation_split": 0.1,
    "num_proc": 8
  },
  "training": {
    "num_epochs": 3,
    "batch_size": 4,
    "gradient_accumulation_steps": 4,
    "learning_rate": 2e-4,
    "weight_decay": 0.01,
    "warmup_steps": 100,
    "logging_steps": 10,
    "save_steps": 500,
    "eval_steps": 500,
    "fp16": true,
    "bf16": false,
    "gradient_checkpointing": true,
    "num_workers": 4,
    "deepspeed_config": "configs/deepspeed_zero2.json"
  },
  "output": {
    "dir": "./outputs/llama2-7b-instruct",
    "save_total_limit": 3
  },
  "wandb": {
    "enabled": true,
    "project": "llm-training",
    "name": "llama2-7b-instruct-v1"
  },
  "seed": 42
}
```

### 2.3 启动脚本

```bash
#!/bin/bash
# launch_training.sh

set -e

# 配置参数
CONFIG_FILE="configs/training_config.json"
NUM_GPUS=8
MASTER_PORT=29500

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

# 检查GPU
if ! command -v nvidia-smi &> /dev/null; then
    echo "错误: 未检测到NVIDIA GPU"
    exit 1
fi

AVAILABLE_GPUS=$(nvidia-smi --list-gpus | wc -l)
if [ $AVAILABLE_GPUS -lt $NUM_GPUS ]; then
    echo "警告: 可用GPU数量 ($AVAILABLE_GPUS) 少于配置数量 ($NUM_GPUS)"
    NUM_GPUS=$AVAILABLE_GPUS
fi

echo "开始LLM训练..."
echo "配置文件: $CONFIG_FILE"
echo "使用GPU数量: $NUM_GPUS"
echo "主端口: $MASTER_PORT"

# 单GPU训练
if [ $NUM_GPUS -eq 1 ]; then
    echo "启动单GPU训练..."
    python train_llm_complete.py --config $CONFIG_FILE

# 多GPU训练
else
    echo "启动多GPU分布式训练..."
    torchrun \
        --nproc_per_node=$NUM_GPUS \
        --master_port=$MASTER_PORT \
        train_llm_complete.py \
        --config $CONFIG_FILE
fi

echo "训练完成！"
```

---

## 3. 生产级部署方案

### 3.1 模型服务部署

```python
# model_server.py
"""
生产级LLM模型服务
支持批量推理、流式输出、负载均衡
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, AsyncGenerator
from contextlib import asynccontextmanager

import torch
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import transformers
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
import redis
from prometheus_client import Counter, Histogram, generate_latest

# 监控指标
REQUEST_COUNT = Counter('llm_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('llm_request_duration_seconds', 'Request duration')
GENERATION_TOKENS = Counter('llm_tokens_generated_total', 'Total tokens generated')

class GenerationRequest(BaseModel):
    """生成请求模型"""
    prompt: str = Field(..., description="输入提示")
    max_length: int = Field(512, ge=1, le=4096, description="最大生成长度")
    temperature: float = Field(0.7, ge=0.1, le=2.0, description="温度参数")
    top_p: float = Field(0.9, ge=0.1, le=1.0, description="Top-p采样")
    top_k: int = Field(50, ge=1, le=100, description="Top-k采样")
    repetition_penalty: float = Field(1.1, ge=1.0, le=2.0, description="重复惩罚")
    stream: bool = Field(False, description="是否流式输出")
    stop_sequences: List[str] = Field(default=[], description="停止序列")

class GenerationResponse(BaseModel):
    """生成响应模型"""
    id: str = Field(..., description="请求ID")
    text: str = Field(..., description="生成文本")
    tokens_generated: int = Field(..., description="生成token数量")
    generation_time: float = Field(..., description="生成时间(秒)")
    tokens_per_second: float = Field(..., description="生成速度(tokens/s)")

class ModelManager:
    """模型管理器"""

    def __init__(self, config: Dict):
        self.config = config
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.load_model()

    def load_model(self):
        """加载模型"""
        model_config = self.config['model']

        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_config['base_model'],
            trust_remote_code=model_config.get('trust_remote_code', False)
        )

        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # 加载基础模型
        self.model = AutoModelForCausalLM.from_pretrained(
            model_config['base_model'],
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=model_config.get('trust_remote_code', False)
        )

        # 加载PEFT适配器
        if model_config.get('peft_model'):
            self.model = PeftModel.from_pretrained(
                self.model,
                model_config['peft_model']
            )

        self.model.eval()
        print(f"模型加载完成: {model_config['base_model']}")

    @torch.no_grad()
    def generate(self, request: GenerationRequest) -> Dict:
        """生成文本"""
        start_time = time.time()

        # 编码输入
        inputs = self.tokenizer(
            request.prompt,
            return_tensors="pt",
            truncation=True,
            max_length=2048
        ).to(self.device)

        input_length = inputs.input_ids.shape[1]

        # 生成参数
        generation_kwargs = {
            "max_length": input_length + request.max_length,
            "temperature": request.temperature,
            "top_p": request.top_p,
            "top_k": request.top_k,
            "repetition_penalty": request.repetition_penalty,
            "do_sample": True,
            "pad_token_id": self.tokenizer.eos_token_id,
            "eos_token_id": self.tokenizer.eos_token_id
        }

        # 生成文本
        with torch.cuda.amp.autocast():
            outputs = self.model.generate(
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                **generation_kwargs
            )

        # 解码输出
        generated_text = self.tokenizer.decode(
            outputs[0][input_length:],
            skip_special_tokens=True
        )

        # 处理停止序列
        for stop_seq in request.stop_sequences:
            if stop_seq in generated_text:
                generated_text = generated_text.split(stop_seq)[0]
                break

        # 计算指标
        generation_time = time.time() - start_time
        tokens_generated = len(outputs[0]) - input_length
        tokens_per_second = tokens_generated / generation_time if generation_time > 0 else 0

        # 更新监控指标
        GENERATION_TOKENS.inc(tokens_generated)

        return {
            "text": generated_text,
            "tokens_generated": tokens_generated,
            "generation_time": generation_time,
            "tokens_per_second": tokens_per_second
        }

    @torch.no_grad()
    async def generate_stream(self, request: GenerationRequest) -> AsyncGenerator[str, None]:
        """流式生成文本"""
        # 编码输入
        inputs = self.tokenizer(
            request.prompt,
            return_tensors="pt",
            truncation=True,
            max_length=2048
        ).to(self.device)

        input_length = inputs.input_ids.shape[1]

        # 生成参数
        generation_kwargs = {
            "max_length": input_length + request.max_length,
            "temperature": request.temperature,
            "top_p": request.top_p,
            "top_k": request.top_k,
            "repetition_penalty": request.repetition_penalty,
            "do_sample": True,
            "pad_token_id": self.tokenizer.eos_token_id,
            "eos_token_id": self.tokenizer.eos_token_id
        }

        # 流式生成
        streamer = transformers.TextIteratorStreamer(
            self.tokenizer,
            skip_prompt=True,
            skip_special_tokens=True
        )

        generation_kwargs["streamer"] = streamer

        # 在后台线程中生成
        generation_thread = asyncio.create_task(
            asyncio.to_thread(
                self.model.generate,
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                **generation_kwargs
            )
        )

        # 流式输出
        for new_text in streamer:
            # 检查停止序列
            should_stop = False
            for stop_seq in request.stop_sequences:
                if stop_seq in new_text:
                    new_text = new_text.split(stop_seq)[0]
                    should_stop = True
                    break

            if new_text:
                yield new_text

            if should_stop:
                break

        # 等待生成完成
        await generation_thread

# 全局模型管理器
model_manager = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global model_manager

    # 启动时加载模型
    config = {
        "model": {
            "base_model": "meta-llama/Llama-2-7b-chat-hf",
            "peft_model": None,  # 可选的PEFT模型路径
            "trust_remote_code": False
        }
    }

    model_manager = ModelManager(config)
    yield

    # 关闭时清理资源
    if model_manager:
        del model_manager.model
        torch.cuda.empty_cache()

# 创建FastAPI应用
app = FastAPI(
    title="LLM Model Server",
    description="生产级大语言模型服务",
    version="1.0.0",
    lifespan=lifespan
)

@app.post("/generate", response_model=GenerationResponse)
async def generate_text(request: GenerationRequest):
    """生成文本接口"""
    REQUEST_COUNT.labels(method="POST", endpoint="/generate").inc()

    with REQUEST_DURATION.time():
        try:
            request_id = str(uuid.uuid4())

            if request.stream:
                # 流式响应
                async def generate_stream():
                    async for chunk in model_manager.generate_stream(request):
                        yield f"data: {json.dumps({'text': chunk})}\n\n"
                    yield f"data: {json.dumps({'done': True})}\n\n"

                return StreamingResponse(
                    generate_stream(),
                    media_type="text/plain",
                    headers={"X-Request-ID": request_id}
                )
            else:
                # 普通响应
                result = model_manager.generate(request)

                return GenerationResponse(
                    id=request_id,
                    text=result["text"],
                    tokens_generated=result["tokens_generated"],
                    generation_time=result["generation_time"],
                    tokens_per_second=result["tokens_per_second"]
                )

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "model_loaded": model_manager is not None,
        "gpu_available": torch.cuda.is_available(),
        "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
    }

@app.get("/metrics")
async def get_metrics():
    """Prometheus指标接口"""
    return generate_latest()

@app.get("/model/info")
async def get_model_info():
    """获取模型信息"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型未加载")

    return {
        "model_name": model_manager.config['model']['base_model'],
        "device": str(model_manager.device),
        "model_parameters": sum(p.numel() for p in model_manager.model.parameters()),
        "trainable_parameters": sum(p.numel() for p in model_manager.model.parameters() if p.requires_grad)
    }

if __name__ == "__main__":
    uvicorn.run(
        "model_server:app",
        host="0.0.0.0",
        port=8000,
        workers=1,  # 由于模型占用GPU内存，通常使用单worker
        log_level="info"
    )
```

### 3.2 Docker部署配置

```dockerfile
# Dockerfile.production
FROM nvidia/cuda:12.1-runtime-ubuntu22.04

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -u 1000 appuser

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY --chown=appuser:appuser . .

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "model_server.py"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  llm-server:
    build:
      context: .
      dockerfile: Dockerfile.production
    ports:
      - "8000:8000"
    environment:
      - CUDA_VISIBLE_DEVICES=0,1,2,3
      - MODEL_NAME=meta-llama/Llama-2-7b-chat-hf
      - MAX_WORKERS=1
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 4
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
```

---

## 4. 性能优化实践

### 4.1 内存优化工具

```python
# memory_optimizer.py
"""
LLM训练内存优化工具
"""

import gc
import torch
import psutil
import nvidia_ml_py3 as nvml
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import seaborn as sns

class MemoryProfiler:
    """内存分析器"""

    def __init__(self):
        self.gpu_available = torch.cuda.is_available()
        if self.gpu_available:
            nvml.nvmlInit()
            self.gpu_count = nvml.nvmlDeviceGetCount()
        else:
            self.gpu_count = 0

    def get_cpu_memory_info(self) -> Dict[str, float]:
        """获取CPU内存信息"""
        memory = psutil.virtual_memory()
        return {
            "total_gb": memory.total / 1e9,
            "available_gb": memory.available / 1e9,
            "used_gb": memory.used / 1e9,
            "percentage": memory.percent
        }

    def get_gpu_memory_info(self, device_id: int = 0) -> Dict[str, float]:
        """获取GPU内存信息"""
        if not self.gpu_available or device_id >= self.gpu_count:
            return {}

        handle = nvml.nvmlDeviceGetHandleByIndex(device_id)
        memory_info = nvml.nvmlDeviceGetMemoryInfo(handle)

        return {
            "total_gb": memory_info.total / 1e9,
            "used_gb": memory_info.used / 1e9,
            "free_gb": memory_info.free / 1e9,
            "percentage": (memory_info.used / memory_info.total) * 100
        }

    def get_torch_memory_info(self, device_id: int = 0) -> Dict[str, float]:
        """获取PyTorch内存信息"""
        if not self.gpu_available:
            return {}

        device = torch.device(f"cuda:{device_id}")

        return {
            "allocated_gb": torch.cuda.memory_allocated(device) / 1e9,
            "cached_gb": torch.cuda.memory_reserved(device) / 1e9,
            "max_allocated_gb": torch.cuda.max_memory_allocated(device) / 1e9,
            "max_cached_gb": torch.cuda.max_memory_reserved(device) / 1e9
        }

    def print_memory_summary(self):
        """打印内存摘要"""
        print("=" * 60)
        print("内存使用情况摘要")
        print("=" * 60)

        # CPU内存
        cpu_info = self.get_cpu_memory_info()
        print(f"CPU内存:")
        print(f"  总计: {cpu_info['total_gb']:.2f} GB")
        print(f"  已用: {cpu_info['used_gb']:.2f} GB ({cpu_info['percentage']:.1f}%)")
        print(f"  可用: {cpu_info['available_gb']:.2f} GB")

        # GPU内存
        if self.gpu_available:
            for i in range(self.gpu_count):
                gpu_info = self.get_gpu_memory_info(i)
                torch_info = self.get_torch_memory_info(i)

                print(f"\nGPU {i} 内存:")
                print(f"  总计: {gpu_info['total_gb']:.2f} GB")
                print(f"  已用: {gpu_info['used_gb']:.2f} GB ({gpu_info['percentage']:.1f}%)")
                print(f"  PyTorch分配: {torch_info['allocated_gb']:.2f} GB")
                print(f"  PyTorch缓存: {torch_info['cached_gb']:.2f} GB")
        else:
            print("\nGPU: 不可用")

    def clear_gpu_cache(self):
        """清理GPU缓存"""
        if self.gpu_available:
            torch.cuda.empty_cache()
            gc.collect()
            print("GPU缓存已清理")

class MemoryOptimizer:
    """内存优化器"""

    def __init__(self):
        self.profiler = MemoryProfiler()

    def estimate_model_memory(self, num_parameters: int, dtype: str = "float16") -> Dict[str, float]:
        """估算模型内存需求"""
        # 参数大小映射
        dtype_sizes = {
            "float32": 4,
            "float16": 2,
            "bfloat16": 2,
            "int8": 1,
            "int4": 0.5
        }

        param_size = dtype_sizes.get(dtype, 4)

        # 基础内存需求
        model_memory = num_parameters * param_size / 1e9  # 模型参数
        gradient_memory = num_parameters * 4 / 1e9  # 梯度 (通常FP32)
        optimizer_memory = num_parameters * 8 / 1e9  # Adam优化器状态

        # 激活值内存 (粗略估算)
        activation_memory = num_parameters * 0.1 * param_size / 1e9

        total_memory = model_memory + gradient_memory + optimizer_memory + activation_memory

        return {
            "model_gb": model_memory,
            "gradient_gb": gradient_memory,
            "optimizer_gb": optimizer_memory,
            "activation_gb": activation_memory,
            "total_gb": total_memory,
            "dtype": dtype
        }

    def suggest_optimizations(self, model_memory_gb: float, available_memory_gb: float) -> List[str]:
        """建议优化策略"""
        suggestions = []

        memory_ratio = model_memory_gb / available_memory_gb

        if memory_ratio > 1.0:
            suggestions.append("🔴 内存不足，必须优化:")

            if memory_ratio > 2.0:
                suggestions.extend([
                    "  • 使用ZeRO-3分片所有参数",
                    "  • 启用CPU/NVMe卸载",
                    "  • 使用int8/int4量化",
                    "  • 减小批次大小到1"
                ])
            else:
                suggestions.extend([
                    "  • 使用ZeRO-2分片梯度和优化器",
                    "  • 启用梯度检查点",
                    "  • 使用混合精度训练",
                    "  • 减小批次大小"
                ])

        elif memory_ratio > 0.8:
            suggestions.append("🟡 内存紧张，建议优化:")
            suggestions.extend([
                "  • 启用梯度检查点",
                "  • 使用混合精度训练",
                "  • 考虑ZeRO-1优化器分片"
            ])

        else:
            suggestions.append("🟢 内存充足，可选优化:")
            suggestions.extend([
                "  • 可以增大批次大小",
                "  • 启用混合精度提升速度",
                "  • 考虑更大的模型"
            ])

        return suggestions

    def optimize_training_config(self, config: Dict) -> Dict:
        """优化训练配置"""
        optimized_config = config.copy()

        # 获取可用内存
        gpu_info = self.profiler.get_gpu_memory_info(0)
        available_memory = gpu_info.get("total_gb", 0) * 0.9  # 保留10%缓冲

        # 估算模型内存
        model_params = config.get("model_parameters", 7e9)
        memory_estimate = self.estimate_model_memory(model_params)

        # 自动调整配置
        if memory_estimate["total_gb"] > available_memory:
            # 启用内存优化
            optimized_config["gradient_checkpointing"] = True
            optimized_config["fp16"] = True

            # 减小批次大小
            original_batch_size = config.get("batch_size", 8)
            optimized_config["batch_size"] = max(1, original_batch_size // 2)
            optimized_config["gradient_accumulation_steps"] = config.get("gradient_accumulation_steps", 1) * 2

            # 建议使用ZeRO
            optimized_config["use_deepspeed"] = True
            optimized_config["zero_stage"] = 3 if memory_estimate["total_gb"] > available_memory * 1.5 else 2

        return optimized_config

    def create_memory_report(self, model_configs: List[Dict]) -> str:
        """创建内存分析报告"""
        report = []
        report.append("LLM训练内存分析报告")
        report.append("=" * 50)

        # 系统信息
        cpu_info = self.profiler.get_cpu_memory_info()
        report.append(f"\n系统内存: {cpu_info['total_gb']:.1f} GB")

        if self.profiler.gpu_available:
            for i in range(self.profiler.gpu_count):
                gpu_info = self.profiler.get_gpu_memory_info(i)
                report.append(f"GPU {i} 内存: {gpu_info['total_gb']:.1f} GB")

        # 模型分析
        report.append(f"\n模型内存需求分析:")
        report.append("-" * 30)

        for i, config in enumerate(model_configs):
            model_name = config.get("name", f"模型{i+1}")
            num_params = config.get("parameters", 7e9)
            dtype = config.get("dtype", "float16")

            memory_est = self.estimate_model_memory(num_params, dtype)

            report.append(f"\n{model_name} ({num_params/1e9:.1f}B参数, {dtype}):")
            report.append(f"  模型参数: {memory_est['model_gb']:.2f} GB")
            report.append(f"  梯度: {memory_est['gradient_gb']:.2f} GB")
            report.append(f"  优化器: {memory_est['optimizer_gb']:.2f} GB")
            report.append(f"  激活值: {memory_est['activation_gb']:.2f} GB")
            report.append(f"  总计: {memory_est['total_gb']:.2f} GB")

            # 优化建议
            if self.profiler.gpu_available:
                gpu_memory = self.profiler.get_gpu_memory_info(0)["total_gb"]
                suggestions = self.suggest_optimizations(memory_est['total_gb'], gpu_memory)
                report.extend(suggestions)

        return "\n".join(report)

# 使用示例
def memory_optimization_example():
    """内存优化示例"""
    optimizer = MemoryOptimizer()

    # 打印当前内存状态
    optimizer.profiler.print_memory_summary()

    # 分析不同模型的内存需求
    model_configs = [
        {"name": "GPT-2", "parameters": 1.5e9, "dtype": "float16"},
        {"name": "LLaMA-7B", "parameters": 7e9, "dtype": "float16"},
        {"name": "LLaMA-13B", "parameters": 13e9, "dtype": "float16"},
        {"name": "LLaMA-70B", "parameters": 70e9, "dtype": "float16"}
    ]

    # 生成内存报告
    report = optimizer.create_memory_report(model_configs)
    print("\n" + report)

    # 优化训练配置
    training_config = {
        "model_parameters": 7e9,
        "batch_size": 8,
        "gradient_accumulation_steps": 1,
        "gradient_checkpointing": False,
        "fp16": False
    }

    optimized_config = optimizer.optimize_training_config(training_config)

    print(f"\n原始配置: {training_config}")
    print(f"优化配置: {optimized_config}")

    return optimizer

if __name__ == "__main__":
    memory_optimization_example()
```

### 4.2 性能监控工具

```python
# performance_monitor.py
"""
LLM训练性能监控工具
"""

import time
import threading
import psutil
import nvidia_ml_py3 as nvml
from collections import deque
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import pandas as pd

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, monitor_interval: float = 1.0, history_size: int = 1000):
        self.monitor_interval = monitor_interval
        self.history_size = history_size
        self.monitoring = False
        self.monitor_thread = None

        # 数据存储
        self.metrics_history = {
            "timestamp": deque(maxlen=history_size),
            "cpu_percent": deque(maxlen=history_size),
            "memory_percent": deque(maxlen=history_size),
            "gpu_utilization": deque(maxlen=history_size),
            "gpu_memory_percent": deque(maxlen=history_size),
            "gpu_temperature": deque(maxlen=history_size),
            "gpu_power": deque(maxlen=history_size)
        }

        # 初始化NVIDIA ML
        self.gpu_available = False
        try:
            nvml.nvmlInit()
            self.gpu_count = nvml.nvmlDeviceGetCount()
            self.gpu_available = True
        except:
            self.gpu_count = 0

    def collect_metrics(self) -> Dict:
        """收集性能指标"""
        timestamp = time.time()

        # CPU和内存指标
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_percent = memory.percent

        metrics = {
            "timestamp": timestamp,
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent
        }

        # GPU指标
        if self.gpu_available and self.gpu_count > 0:
            try:
                handle = nvml.nvmlDeviceGetHandleByIndex(0)  # 监控第一个GPU

                # GPU利用率
                utilization = nvml.nvmlDeviceGetUtilizationRates(handle)
                metrics["gpu_utilization"] = utilization.gpu

                # GPU内存
                memory_info = nvml.nvmlDeviceGetMemoryInfo(handle)
                metrics["gpu_memory_percent"] = (memory_info.used / memory_info.total) * 100

                # GPU温度
                temperature = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)
                metrics["gpu_temperature"] = temperature

                # GPU功耗
                power = nvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为瓦特
                metrics["gpu_power"] = power

            except Exception as e:
                print(f"GPU指标收集失败: {e}")
                metrics.update({
                    "gpu_utilization": 0,
                    "gpu_memory_percent": 0,
                    "gpu_temperature": 0,
                    "gpu_power": 0
                })
        else:
            metrics.update({
                "gpu_utilization": 0,
                "gpu_memory_percent": 0,
                "gpu_temperature": 0,
                "gpu_power": 0
            })

        return metrics

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self.collect_metrics()

                # 存储指标
                for key, value in metrics.items():
                    if key in self.metrics_history:
                        self.metrics_history[key].append(value)

                time.sleep(self.monitor_interval)

            except Exception as e:
                print(f"监控循环错误: {e}")
                time.sleep(self.monitor_interval)

    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("性能监控已启动")

    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join()
            print("性能监控已停止")

    def get_current_metrics(self) -> Dict:
        """获取当前指标"""
        return self.collect_metrics()

    def get_average_metrics(self, last_n: Optional[int] = None) -> Dict:
        """获取平均指标"""
        if not self.metrics_history["timestamp"]:
            return {}

        # 确定数据范围
        if last_n is None:
            data_slice = slice(None)
        else:
            data_slice = slice(-last_n, None)

        averages = {}
        for key, values in self.metrics_history.items():
            if key != "timestamp" and values:
                data = list(values)[data_slice]
                if data:
                    averages[f"avg_{key}"] = sum(data) / len(data)

        return averages

    def export_metrics(self, filepath: str):
        """导出指标到CSV"""
        if not self.metrics_history["timestamp"]:
            print("没有可导出的数据")
            return

        # 转换为DataFrame
        df_data = {}
        for key, values in self.metrics_history.items():
            df_data[key] = list(values)

        df = pd.DataFrame(df_data)
        df.to_csv(filepath, index=False)
        print(f"指标已导出到: {filepath}")

    def plot_metrics(self, save_path: Optional[str] = None):
        """绘制性能图表"""
        if not self.metrics_history["timestamp"]:
            print("没有可绘制的数据")
            return

        # 准备数据
        timestamps = list(self.metrics_history["timestamp"])
        if not timestamps:
            return

        # 转换时间戳为相对时间（秒）
        start_time = timestamps[0]
        relative_times = [(t - start_time) for t in timestamps]

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle("LLM训练性能监控", fontsize=16)

        # CPU使用率
        axes[0, 0].plot(relative_times, list(self.metrics_history["cpu_percent"]), 'b-', linewidth=2)
        axes[0, 0].set_title("CPU使用率")
        axes[0, 0].set_ylabel("使用率 (%)")
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].set_ylim(0, 100)

        # 内存使用率
        axes[0, 1].plot(relative_times, list(self.metrics_history["memory_percent"]), 'g-', linewidth=2)
        axes[0, 1].set_title("内存使用率")
        axes[0, 1].set_ylabel("使用率 (%)")
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].set_ylim(0, 100)

        # GPU使用率
        if self.gpu_available:
            axes[1, 0].plot(relative_times, list(self.metrics_history["gpu_utilization"]), 'r-', linewidth=2)
            axes[1, 0].set_title("GPU使用率")
            axes[1, 0].set_ylabel("使用率 (%)")
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].set_ylim(0, 100)

            # GPU内存使用率
            axes[1, 1].plot(relative_times, list(self.metrics_history["gpu_memory_percent"]), 'm-', linewidth=2)
            axes[1, 1].set_title("GPU内存使用率")
            axes[1, 1].set_ylabel("使用率 (%)")
            axes[1, 1].grid(True, alpha=0.3)
            axes[1, 1].set_ylim(0, 100)
        else:
            axes[1, 0].text(0.5, 0.5, "GPU不可用", ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 1].text(0.5, 0.5, "GPU不可用", ha='center', va='center', transform=axes[1, 1].transAxes)

        # 设置x轴标签
        for ax in axes.flat:
            ax.set_xlabel("时间 (秒)")

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        else:
            plt.show()

    def print_summary(self):
        """打印性能摘要"""
        current = self.get_current_metrics()
        averages = self.get_average_metrics()

        print("=" * 50)
        print("性能监控摘要")
        print("=" * 50)

        print(f"当前指标:")
        print(f"  CPU使用率: {current.get('cpu_percent', 0):.1f}%")
        print(f"  内存使用率: {current.get('memory_percent', 0):.1f}%")

        if self.gpu_available:
            print(f"  GPU使用率: {current.get('gpu_utilization', 0):.1f}%")
            print(f"  GPU内存使用率: {current.get('gpu_memory_percent', 0):.1f}%")
            print(f"  GPU温度: {current.get('gpu_temperature', 0):.1f}°C")
            print(f"  GPU功耗: {current.get('gpu_power', 0):.1f}W")

        if averages:
            print(f"\n平均指标:")
            print(f"  平均CPU使用率: {averages.get('avg_cpu_percent', 0):.1f}%")
            print(f"  平均内存使用率: {averages.get('avg_memory_percent', 0):.1f}%")

            if self.gpu_available:
                print(f"  平均GPU使用率: {averages.get('avg_gpu_utilization', 0):.1f}%")
                print(f"  平均GPU内存使用率: {averages.get('avg_gpu_memory_percent', 0):.1f}%")

# 使用示例
def performance_monitoring_example():
    """性能监控示例"""
    monitor = PerformanceMonitor(monitor_interval=0.5)

    try:
        # 开始监控
        monitor.start_monitoring()

        # 模拟训练过程
        print("开始模拟训练...")
        time.sleep(10)  # 监控10秒

        # 打印摘要
        monitor.print_summary()

        # 绘制图表
        monitor.plot_metrics("performance_chart.png")

        # 导出数据
        monitor.export_metrics("performance_metrics.csv")

    finally:
        # 停止监控
        monitor.stop_monitoring()

    return monitor

if __name__ == "__main__":
    performance_monitoring_example()
```

---

## 5. 故障排除指南

### 5.1 常见错误诊断工具

```python
# troubleshoot.py
"""
LLM训练故障排除工具
"""

import torch
import traceback
import logging
import sys
import os
from typing import Dict, List, Optional, Callable
import subprocess
import json

class TroubleshootingTool:
    """故障排除工具"""

    def __init__(self):
        self.checks = []
        self.solutions = {}
        self.setup_logging()
        self.register_common_checks()

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def register_check(self, name: str, check_func: Callable, solution: str):
        """注册检查项"""
        self.checks.append({
            "name": name,
            "func": check_func,
            "solution": solution
        })

    def register_common_checks(self):
        """注册常见检查项"""

        # CUDA检查
        def check_cuda():
            if not torch.cuda.is_available():
                return False, "CUDA不可用"
            return True, f"CUDA可用，版本: {torch.version.cuda}"

        self.register_check(
            "CUDA可用性",
            check_cuda,
            "安装CUDA和对应的PyTorch版本"
        )

        # GPU内存检查
        def check_gpu_memory():
            if not torch.cuda.is_available():
                return True, "跳过GPU内存检查"

            try:
                device = torch.device("cuda:0")
                total_memory = torch.cuda.get_device_properties(device).total_memory
                allocated_memory = torch.cuda.memory_allocated(device)

                if allocated_memory / total_memory > 0.95:
                    return False, f"GPU内存使用率过高: {allocated_memory/total_memory*100:.1f}%"

                return True, f"GPU内存正常: {allocated_memory/1e9:.1f}GB / {total_memory/1e9:.1f}GB"
            except Exception as e:
                return False, f"GPU内存检查失败: {str(e)}"

        self.register_check(
            "GPU内存",
            check_gpu_memory,
            "清理GPU缓存: torch.cuda.empty_cache()"
        )

        # 依赖版本检查
        def check_dependencies():
            try:
                import transformers
                import datasets
                import accelerate

                versions = {
                    "transformers": transformers.__version__,
                    "datasets": datasets.__version__,
                    "accelerate": accelerate.__version__
                }

                return True, f"依赖版本: {versions}"
            except ImportError as e:
                return False, f"依赖缺失: {str(e)}"

        self.register_check(
            "依赖版本",
            check_dependencies,
            "安装缺失的依赖包"
        )

        # 分布式环境检查
        def check_distributed():
            if "RANK" in os.environ:
                try:
                    rank = int(os.environ["RANK"])
                    world_size = int(os.environ["WORLD_SIZE"])
                    local_rank = int(os.environ.get("LOCAL_RANK", 0))

                    return True, f"分布式环境: rank={rank}, world_size={world_size}, local_rank={local_rank}"
                except Exception as e:
                    return False, f"分布式环境配置错误: {str(e)}"
            else:
                return True, "单机训练模式"

        self.register_check(
            "分布式环境",
            check_distributed,
            "检查分布式环境变量设置"
        )

    def run_all_checks(self) -> Dict[str, Dict]:
        """运行所有检查"""
        results = {}

        self.logger.info("开始系统检查...")

        for check in self.checks:
            try:
                success, message = check["func"]()
                results[check["name"]] = {
                    "success": success,
                    "message": message,
                    "solution": check["solution"] if not success else None
                }

                status = "✅" if success else "❌"
                self.logger.info(f"{status} {check['name']}: {message}")

            except Exception as e:
                results[check["name"]] = {
                    "success": False,
                    "message": f"检查失败: {str(e)}",
                    "solution": check["solution"]
                }
                self.logger.error(f"❌ {check['name']}: 检查失败 - {str(e)}")

        return results

    def diagnose_error(self, error_message: str, traceback_str: str) -> Dict[str, str]:
        """诊断错误"""
        diagnosis = {
            "error_type": "未知错误",
            "possible_causes": [],
            "solutions": []
        }

        # 常见错误模式匹配
        error_patterns = {
            "CUDA out of memory": {
                "type": "GPU内存不足",
                "causes": ["批次大小过大", "模型过大", "序列长度过长", "梯度累积"],
                "solutions": [
                    "减小批次大小",
                    "启用梯度检查点",
                    "使用混合精度训练",
                    "使用ZeRO优化",
                    "清理GPU缓存: torch.cuda.empty_cache()"
                ]
            },
            "RuntimeError: Expected all tensors to be on the same device": {
                "type": "设备不匹配",
                "causes": ["张量在不同设备上", "模型和数据设备不一致"],
                "solutions": [
                    "确保模型和数据在同一设备",
                    "使用.to(device)移动张量",
                    "检查device_map配置"
                ]
            },
            "ImportError": {
                "type": "依赖缺失",
                "causes": ["包未安装", "版本不兼容", "环境配置错误"],
                "solutions": [
                    "安装缺失的包",
                    "检查版本兼容性",
                    "重新创建虚拟环境"
                ]
            },
            "FileNotFoundError": {
                "type": "文件不存在",
                "causes": ["路径错误", "文件未下载", "权限问题"],
                "solutions": [
                    "检查文件路径",
                    "下载缺失文件",
                    "检查文件权限"
                ]
            },
            "KeyError": {
                "type": "配置错误",
                "causes": ["配置文件格式错误", "缺少必要字段", "键名错误"],
                "solutions": [
                    "检查配置文件格式",
                    "验证必要字段",
                    "参考配置模板"
                ]
            }
        }

        # 匹配错误模式
        for pattern, info in error_patterns.items():
            if pattern.lower() in error_message.lower():
                diagnosis.update({
                    "error_type": info["type"],
                    "possible_causes": info["causes"],
                    "solutions": info["solutions"]
                })
                break

        return diagnosis

    def generate_report(self, check_results: Dict, error_info: Optional[Dict] = None) -> str:
        """生成故障排除报告"""
        report = []
        report.append("LLM训练故障排除报告")
        report.append("=" * 50)

        # 系统检查结果
        report.append("\n系统检查结果:")
        report.append("-" * 30)

        failed_checks = []
        for check_name, result in check_results.items():
            status = "✅ 通过" if result["success"] else "❌ 失败"
            report.append(f"{status} {check_name}: {result['message']}")

            if not result["success"]:
                failed_checks.append((check_name, result))

        # 失败项的解决方案
        if failed_checks:
            report.append("\n解决方案:")
            report.append("-" * 30)

            for check_name, result in failed_checks:
                report.append(f"\n{check_name}:")
                report.append(f"  问题: {result['message']}")
                report.append(f"  解决方案: {result['solution']}")

        # 错误诊断
        if error_info:
            report.append(f"\n错误诊断:")
            report.append("-" * 30)
            report.append(f"错误类型: {error_info['error_type']}")

            if error_info['possible_causes']:
                report.append("可能原因:")
                for cause in error_info['possible_causes']:
                    report.append(f"  • {cause}")

            if error_info['solutions']:
                report.append("建议解决方案:")
                for solution in error_info['solutions']:
                    report.append(f"  • {solution}")

        # 通用建议
        report.append(f"\n通用故障排除步骤:")
        report.append("-" * 30)
        report.append("1. 检查错误日志的完整信息")
        report.append("2. 验证配置文件格式和内容")
        report.append("3. 确认环境依赖和版本兼容性")
        report.append("4. 检查硬件资源是否充足")
        report.append("5. 尝试简化配置进行测试")
        report.append("6. 查看官方文档和社区讨论")

        return "\n".join(report)

class ErrorHandler:
    """错误处理器"""

    def __init__(self):
        self.troubleshooter = TroubleshootingTool()

    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理异常"""
        if exc_type is KeyboardInterrupt:
            print("\n训练被用户中断")
            return

        # 获取错误信息
        error_message = str(exc_value)
        traceback_str = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))

        print(f"\n发生错误: {error_message}")
        print(f"错误类型: {exc_type.__name__}")

        # 运行系统检查
        check_results = self.troubleshooter.run_all_checks()

        # 诊断错误
        error_diagnosis = self.troubleshooter.diagnose_error(error_message, traceback_str)

        # 生成报告
        report = self.troubleshooter.generate_report(check_results, error_diagnosis)

        print("\n" + report)

        # 保存报告
        with open("troubleshooting_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
            f.write(f"\n\n完整错误信息:\n{traceback_str}")

        print(f"\n详细报告已保存到: troubleshooting_report.txt")

def install_error_handler():
    """安装全局错误处理器"""
    handler = ErrorHandler()
    sys.excepthook = handler.handle_exception

# 使用示例
def troubleshooting_example():
    """故障排除示例"""
    # 安装错误处理器
    install_error_handler()

    # 创建故障排除工具
    troubleshooter = TroubleshootingTool()

    # 运行系统检查
    results = troubleshooter.run_all_checks()

    # 生成报告
    report = troubleshooter.generate_report(results)
    print(report)

    # 模拟错误诊断
    error_message = "CUDA out of memory. Tried to allocate 2.00 GiB"
    diagnosis = troubleshooter.diagnose_error(error_message, "")

    print(f"\n错误诊断示例:")
    print(f"错误: {error_message}")
    print(f"诊断: {diagnosis}")

    return troubleshooter

if __name__ == "__main__":
    troubleshooting_example()
```

### 5.2 自动修复脚本

```bash
#!/bin/bash
# auto_fix.sh
# LLM训练环境自动修复脚本

set -e

echo "LLM训练环境自动修复工具"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查并修复CUDA环境
fix_cuda_environment() {
    log_info "检查CUDA环境..."

    if ! command -v nvidia-smi &> /dev/null; then
        log_error "NVIDIA驱动未安装"
        echo "请安装NVIDIA驱动: https://www.nvidia.com/drivers"
        return 1
    fi

    # 检查CUDA版本
    if ! command -v nvcc &> /dev/null; then
        log_warn "CUDA工具包未找到，尝试设置环境变量..."

        # 常见CUDA路径
        CUDA_PATHS=(
            "/usr/local/cuda"
            "/opt/cuda"
            "/usr/local/cuda-12.1"
            "/usr/local/cuda-11.8"
        )

        for path in "${CUDA_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export CUDA_HOME="$path"
                export PATH="$path/bin:$PATH"
                export LD_LIBRARY_PATH="$path/lib64:$LD_LIBRARY_PATH"
                log_info "设置CUDA路径: $path"
                break
            fi
        done
    fi

    # 验证CUDA
    if command -v nvcc &> /dev/null; then
        CUDA_VERSION=$(nvcc --version | grep "release" | awk '{print $6}' | cut -c2-)
        log_info "CUDA版本: $CUDA_VERSION"
    else
        log_error "CUDA工具包未正确安装"
        return 1
    fi

    return 0
}

# 修复Python环境
fix_python_environment() {
    log_info "检查Python环境..."

    # 检查Python版本
    PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

    if [ "$PYTHON_MAJOR" -lt 3 ] || [ "$PYTHON_MINOR" -lt 8 ]; then
        log_error "Python版本过低: $PYTHON_VERSION (需要 >= 3.8)"
        return 1
    fi

    log_info "Python版本: $PYTHON_VERSION"

    # 检查pip
    if ! command -v pip &> /dev/null; then
        log_warn "pip未安装，尝试安装..."
        python -m ensurepip --upgrade
    fi

    # 升级pip
    log_info "升级pip..."
    python -m pip install --upgrade pip

    return 0
}

# 修复PyTorch安装
fix_pytorch_installation() {
    log_info "检查PyTorch安装..."

    # 检查PyTorch是否安装
    if ! python -c "import torch" &> /dev/null; then
        log_warn "PyTorch未安装，开始安装..."

        # 检测CUDA版本并安装对应的PyTorch
        if command -v nvidia-smi &> /dev/null; then
            CUDA_VERSION=$(nvidia-smi | grep "CUDA Version" | awk '{print $9}' | cut -d. -f1,2)

            case $CUDA_VERSION in
                "12.1")
                    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
                    ;;
                "11.8")
                    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
                    ;;
                *)
                    log_warn "未知CUDA版本: $CUDA_VERSION，安装CPU版本"
                    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
                    ;;
            esac
        else
            log_warn "未检测到CUDA，安装CPU版本"
            pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
        fi
    fi

    # 验证PyTorch安装
    if python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')" &> /dev/null; then
        TORCH_VERSION=$(python -c "import torch; print(torch.__version__)")
        CUDA_AVAILABLE=$(python -c "import torch; print(torch.cuda.is_available())")
        log_info "PyTorch版本: $TORCH_VERSION"
        log_info "CUDA可用: $CUDA_AVAILABLE"
    else
        log_error "PyTorch安装验证失败"
        return 1
    fi

    return 0
}

# 修复训练库依赖
fix_training_dependencies() {
    log_info "检查训练库依赖..."

    # 核心依赖列表
    DEPENDENCIES=(
        "transformers>=4.30.0"
        "datasets>=2.10.0"
        "accelerate>=0.20.0"
        "peft>=0.5.0"
        "trl>=0.5.0"
    )

    # 可选依赖
    OPTIONAL_DEPENDENCIES=(
        "deepspeed>=0.10.0"
        "wandb>=0.15.0"
        "tensorboard>=2.10.0"
    )

    # 安装核心依赖
    for dep in "${DEPENDENCIES[@]}"; do
        log_info "安装: $dep"
        pip install "$dep" || log_warn "安装失败: $dep"
    done

    # 尝试安装可选依赖
    for dep in "${OPTIONAL_DEPENDENCIES[@]}"; do
        log_info "尝试安装: $dep"
        pip install "$dep" || log_warn "可选依赖安装失败: $dep"
    done

    return 0
}

# 清理GPU内存
clear_gpu_memory() {
    log_info "清理GPU内存..."

    # 杀死可能占用GPU的进程
    if command -v nvidia-smi &> /dev/null; then
        # 获取GPU进程
        GPU_PROCESSES=$(nvidia-smi --query-compute-apps=pid --format=csv,noheader,nounits)

        if [ -n "$GPU_PROCESSES" ]; then
            log_warn "发现GPU进程，尝试清理..."
            echo "$GPU_PROCESSES" | while read -r pid; do
                if [ -n "$pid" ] && [ "$pid" != "No running processes found" ]; then
                    log_info "终止进程: $pid"
                    kill -9 "$pid" 2>/dev/null || true
                fi
            done
        fi

        # 重置GPU
        nvidia-smi --gpu-reset || log_warn "GPU重置失败"
    fi

    return 0
}

# 修复权限问题
fix_permissions() {
    log_info "检查文件权限..."

    # 检查当前目录权限
    if [ ! -w "." ]; then
        log_error "当前目录无写权限"
        return 1
    fi

    # 检查模型缓存目录
    CACHE_DIRS=(
        "$HOME/.cache/huggingface"
        "$HOME/.cache/torch"
        "/tmp"
    )

    for dir in "${CACHE_DIRS[@]}"; do
        if [ -d "$dir" ] && [ ! -w "$dir" ]; then
            log_warn "修复目录权限: $dir"
            chmod -R u+w "$dir" 2>/dev/null || log_warn "权限修复失败: $dir"
        fi
    done

    return 0
}

# 生成诊断报告
generate_diagnostic_report() {
    log_info "生成诊断报告..."

    REPORT_FILE="diagnostic_report.txt"

    {
        echo "LLM训练环境诊断报告"
        echo "生成时间: $(date)"
        echo "=========================="
        echo

        echo "系统信息:"
        echo "--------"
        uname -a
        echo

        echo "Python环境:"
        echo "----------"
        python --version
        pip --version
        echo

        echo "CUDA环境:"
        echo "--------"
        if command -v nvidia-smi &> /dev/null; then
            nvidia-smi
        else
            echo "NVIDIA驱动未安装"
        fi
        echo

        echo "PyTorch信息:"
        echo "----------"
        python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA版本: {torch.version.cuda}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU数量: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
" 2>/dev/null || echo "PyTorch未正确安装"
        echo

        echo "已安装包:"
        echo "--------"
        pip list | grep -E "(torch|transformers|datasets|accelerate|peft|trl|deepspeed)"

    } > "$REPORT_FILE"

    log_info "诊断报告已保存到: $REPORT_FILE"
}

# 主修复流程
main() {
    log_info "开始自动修复流程..."

    # 修复步骤
    fix_python_environment || log_error "Python环境修复失败"
    fix_cuda_environment || log_warn "CUDA环境修复失败"
    fix_pytorch_installation || log_error "PyTorch安装修复失败"
    fix_training_dependencies || log_warn "训练库依赖修复失败"
    clear_gpu_memory || log_warn "GPU内存清理失败"
    fix_permissions || log_warn "权限修复失败"

    # 生成报告
    generate_diagnostic_report

    log_info "自动修复完成！"
    log_info "请查看诊断报告: diagnostic_report.txt"
}

# 运行主流程
main "$@"
```

### 5.3 性能调优检查清单

```markdown
# LLM训练性能调优检查清单

## 🔧 硬件优化

### GPU配置
- [ ] 使用最新的NVIDIA驱动
- [ ] 启用GPU ECC内存（如果支持）
- [ ] 设置GPU功耗限制为最大值
- [ ] 配置GPU风扇曲线确保散热
- [ ] 使用NVLink连接多GPU（如果可用）

### 内存优化
- [ ] 启用系统内存的XMP配置
- [ ] 确保足够的系统内存（推荐GPU内存的2-4倍）
- [ ] 使用高速SSD存储训练数据
- [ ] 配置足够的swap空间

### 网络优化
- [ ] 使用InfiniBand或高速以太网
- [ ] 优化网络拓扑减少通信延迟
- [ ] 配置RDMA（如果支持）
- [ ] 调整网络缓冲区大小

## ⚙️ 软件配置

### CUDA优化
- [ ] 使用最新的CUDA版本
- [ ] 启用CUDA图优化
- [ ] 配置CUDA内存池
- [ ] 使用cuDNN优化库

### PyTorch优化
- [ ] 启用torch.compile（PyTorch 2.0+）
- [ ] 使用torch.jit.script编译模型
- [ ] 启用混合精度训练
- [ ] 配置数据加载器的num_workers

### 训练框架优化
- [ ] 选择合适的分布式后端（NCCL/Gloo）
- [ ] 启用梯度压缩
- [ ] 使用异步通信
- [ ] 配置检查点保存策略

## 📊 训练参数调优

### 批次大小优化
- [ ] 找到最大可用批次大小
- [ ] 使用梯度累积模拟大批次
- [ ] 考虑动态批次大小
- [ ] 平衡内存使用和训练速度

### 学习率调优
- [ ] 使用学习率查找器
- [ ] 配置合适的warmup策略
- [ ] 选择适当的学习率调度器
- [ ] 考虑不同层的差异化学习率

### 数据处理优化
- [ ] 使用高效的数据格式（如Arrow）
- [ ] 启用数据预取和缓存
- [ ] 优化数据增强流水线
- [ ] 使用多进程数据加载

## 🔍 监控和调试

### 性能监控
- [ ] 监控GPU利用率（目标>90%）
- [ ] 跟踪内存使用情况
- [ ] 监控网络I/O
- [ ] 记录训练吞吐量

### 训练监控
- [ ] 跟踪损失收敛
- [ ] 监控梯度范数
- [ ] 检查权重更新幅度
- [ ] 验证模型性能

### 系统监控
- [ ] 监控CPU使用率
- [ ] 跟踪磁盘I/O
- [ ] 监控网络带宽
- [ ] 检查系统温度

## 🚀 高级优化

### 模型优化
- [ ] 使用参数高效微调（PEFT）
- [ ] 启用梯度检查点
- [ ] 考虑模型并行
- [ ] 使用激活重计算

### 内存优化
- [ ] 启用ZeRO优化
- [ ] 使用CPU/NVMe卸载
- [ ] 配置动态损失缩放
- [ ] 优化激活值存储

### 通信优化
- [ ] 使用梯度压缩
- [ ] 启用通信重叠
- [ ] 优化AllReduce策略
- [ ] 配置通信拓扑

## ✅ 验证检查

### 正确性验证
- [ ] 对比单GPU和多GPU结果
- [ ] 验证梯度同步正确性
- [ ] 检查数值稳定性
- [ ] 确认模型收敛

### 性能验证
- [ ] 测量端到端训练时间
- [ ] 计算GPU利用率
- [ ] 评估扩展效率
- [ ] 对比基准性能

### 稳定性验证
- [ ] 长时间训练测试
- [ ] 故障恢复测试
- [ ] 内存泄漏检查
- [ ] 异常处理验证
```

---

## 📝 总结

本文档提供了LLM训练的完整代码示例和实践指南，涵盖了从环境配置到故障排除的全流程。主要内容包括：

1. **环境配置**: Docker和Conda环境的完整配置方案
2. **训练流水线**: 端到端的训练代码和配置模板
3. **生产部署**: 模型服务和容器化部署方案
4. **性能优化**: 内存优化和性能监控工具
5. **故障排除**: 自动诊断和修复工具

通过这些工具和指南，您可以：
- 快速搭建LLM训练环境
- 实现高效的训练流水线
- 部署生产级模型服务
- 优化训练性能
- 快速诊断和解决问题

建议根据实际需求选择合适的工具和配置，并结合监控数据持续优化训练效果。
```
```
```
