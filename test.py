import numpy as np
import pandas as pd
from sklearn.metrics.pairwise import cosine_similarity
from scipy.sparse import csr_matrix 
import warnings
warnings.filterwarnings('ignore')

class CF:
    """
    协同过滤:
    基于用户的历史数据，发现相似用户或者相似物品，生成推荐
    1) UBCF
    2) IBCF
    步骤：
    1）计算相似度
    2）预测评分
    3）推荐
    """
    def __init__(self, method="UBCF"):
        ...
    def fit(self, rating_data):
        ...
    def predict_rating(self, user_id, item_id):
        ...
    def compute_similarity(self):
        ...
    def __compute_user_similarity(self):
        ...
    def __compute_item_similarity(self):
        ... 
    def recommend(self, user_id, n_recommendations=10, k=20):
        ...
    def __predict_user_based(self, user_id, item_id, k):
        ...        
    def __predict_item_based(self, user_id, item_id, k):
        ...

    def evaluate(self, test_data, k=20):
        ...

    def __str__(self):
        return f"CF(method={self.method})"


if __name__ == "__main__":
    user_cf, item_cf = test_cf()