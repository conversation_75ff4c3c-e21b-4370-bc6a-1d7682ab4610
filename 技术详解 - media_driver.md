# Intel Media Driver 详细技术分析

本文档对 Intel Media Driver 的架构设计、核心数据结构和实现模式进行详细分析，作为英特尔媒体加速方案技术分析的补充内容。

## 代码仓库信息

- **Git仓库地址**: https://github.com/intel/media-driver.git
- **本地路径**: /home/<USER>/INTEL_MEDIA/media-driver

## 目录

- [架构概述](#架构概述)
- [设计模式与实现](#设计模式与实现)
  - [工厂模式](#工厂模式)
  - [策略模式](#策略模式)
  - [观察者模式](#观察者模式)
  - [单例模式](#单例模式)
  - [命令模式](#命令模式)
- [核心数据结构](#核心数据结构)
  - [DDI层数据结构](#ddi层数据结构)
  - [HAL层数据结构](#hal层数据结构)
  - [MOS层数据结构](#mos层数据结构)
  - [硬件特定数据结构](#硬件特定数据结构)
- [关键组件分析](#关键组件分析)
  - [媒体接口层](#媒体接口层)
  - [编解码HAL](#编解码hal)
  - [媒体内存管理](#媒体内存管理)
  - [GPU命令构建](#gpu命令构建)
- [UML图表](#uml图表)
  - [架构层次图](#架构层次图)
  - [类关系图](#类关系图)
  - [处理流程图](#处理流程图)
  - [组件交互图](#组件交互图)

## 架构概述

Intel Media Driver是Intel GPU媒体加速能力的具体实现者，作为VA-API的用户模式驱动，它通过利用Intel图形硬件，提供硬件加速的解码、编码和视频后处理功能。Media Driver采用模块化架构，主要分为以下几层：

1. **DDI层**：直接与VA-API交互的接口层，实现VA-API定义的所有函数
2. **HAL层**：硬件抽象层，提供平台无关的接口
3. **MOS层**：媒体操作系统服务，提供操作系统相关功能
4. **硬件特定层**：针对不同一代Intel GPU的特定实现

这种分层设计使得Media Driver能够支持多代Intel图形架构，同时提供统一的接口给上层应用。

## 设计模式与实现

### 工厂模式

Media Driver广泛使用工厂模式创建各种组件，特别是在不同代GPU之间选择合适的实现：

```cpp
// 工厂模式示例：MediaLibvaCapsFactory
template <class T, class C>
class MediaLibvaCapsFactory
{
public:
    template <class U>
    static bool RegisterCaps(uint32_t key)
    {
        auto &capMap = GetCapMap();
        capMap[key] = CreateCapsFunc<U>;
        return true;
    }

    static T* CreateCaps(C* ctx, uint32_t key)
    {
        auto &capMap = GetCapMap();
        auto it = capMap.find(key);
        if (it == capMap.end())
            return nullptr;
        return it->second(ctx);
    }

private:
    template <class U>
    static T* CreateCapsFunc(C* ctx)
    {
        return new U(ctx);
    }

    static std::map<uint32_t, CreateCapsFunc>& GetCapMap()
    {
        static std::map<uint32_t, CreateCapsFunc> capMap;
        return capMap;
    }

    typedef T* (*CreateCapsFunc)(C*);
};
```

在Media Driver中，这种工厂模式使用注册机制来关联不同的GPU平台与相应的实现：

```cpp
// 注册不同平台的Caps实现
static bool iclRegistered = MediaLibvaCapsFactory<MediaLibvaCaps, DDI_MEDIA_CONTEXT>::
    RegisterCaps<MediaLibvaCapsG11>((uint32_t)IGFX_ICELAKE);
static bool tglRegistered = MediaLibvaCapsFactory<MediaLibvaCaps, DDI_MEDIA_CONTEXT>::
    RegisterCaps<MediaLibvaCapsG12>((uint32_t)IGFX_TIGERLAKE);
```

这种设计的优势：
- 根据硬件平台动态选择正确的实现
- 松散耦合，便于添加新的硬件支持
- 避免了大量的条件判断代码

### 策略模式

Media Driver通过策略模式选择不同的算法实现，特别是在编解码方面：

```cpp
// 编码策略选择
typedef struct _CODECHAL_ENCODE_SETTING
{
    // 编码设置参数
    uint32_t dwEncodeMode;                  // 编码模式
    union
    {
        struct
        {
            uint32_t CodingType          : 8;  // I/P/B帧类型
            uint32_t FrameType           : 8;  // 帧类型
            uint32_t PAKOnlyMultipassEnable : 1;  // PAK-only多pass使能
            // 更多标志...
        };
        uint32_t value;
    };
} CODECHAL_ENCODE_SETTING;
```

不同的编码策略可能包括：
- 不同的速率控制算法（CBR、VBR、CQP等）
- 不同的宏块处理方式
- 低功耗与高质量模式的选择

### 观察者模式

Media Driver中使用回调函数和事件通知机制实现观察者模式，特别是在异步操作完成时：

```cpp
// 异步执行结果回调
typedef struct _CODECHAL_EXECUTION_STATUS_PARAMS
{
    uint32_t        uiExecutionFlag;
    void            *pExecuteCallBackParams;
    void            (*pfnExecuteCallBack) (void *pExecuteCallBackParams, void *pvBEStatusParams);
} CODECHAL_EXECUTION_STATUS_PARAMS, *PCODECHAL_EXECUTION_STATUS_PARAMS;
```

这种设计允许：
- 异步处理编解码任务
- 在任务完成时通知应用程序
- 并行处理多个编解码会话

### 单例模式

Media Driver使用单例模式管理全局资源，如设备信息和上下文管理器：

```cpp
// 单例模式实现示例
class GpuContextMgr
{
public:
    static GpuContextMgr* GetObject();
    
    // 其他方法...
    
private:
    static GpuContextMgr* s_gpuContextMgr;
};

// 实现部分
GpuContextMgr* GpuContextMgr::s_gpuContextMgr = nullptr;

GpuContextMgr* GpuContextMgr::GetObject()
{
    if (s_gpuContextMgr == nullptr)
    {
        s_gpuContextMgr = MOS_New(GpuContextMgr);
    }
    return s_gpuContextMgr;
}
```

单例模式确保了资源的唯一性和共享性，适用于：
- 设备管理
- 内存管理
- 全局配置

### 命令模式

Media Driver使用命令模式构建和提交GPU命令：

```cpp
// 命令缓冲管理
class CmdBufMgr
{
public:
    MOS_STATUS Allocate(PMOS_ALLOC_GFXRES_PARAMS params, PMOS_RESOURCE resource);
    MOS_STATUS Free(PMOS_RESOURCE resource);
    
    // 其他方法...
};

// 命令构建示例
MOS_STATUS CodecHalEncode::SubmitCommandBuffer(
    PMOS_COMMAND_BUFFER cmdBuffer,
    bool                 nullRendering)
{
    // 构建命令
    // 提交到GPU
    return MOS_STATUS_SUCCESS;
}
```

命令模式的优势：
- 封装复杂的GPU指令序列
- 支持命令重用和批处理
- 优化命令提交性能

## 核心数据结构

### DDI层数据结构

DDI层是与VA-API直接交互的接口层，包含以下关键数据结构：

```cpp
// 媒体上下文，是整个驱动的核心数据结构
typedef struct _DDI_MEDIA_CONTEXT
{
    MOS_CONTEXT                MosCtx;                  // MOS上下文
    MEDIA_FEATURE_TABLE        SkuTable;                // SKU特性表
    MEDIA_WA_TABLE             WaTable;                 // WA表
    ALLOCATION_LIST            *pAllocationList;        // 分配列表
    HMODULE                    hDrmLib;                 // DRM库句柄
    bool                       bIsAtomSOC;              // 是否为Atom SOC
    bool                       bUseSwSwizzling;         // 是否使用软件swizzling
    HANDLE                     hLibVA;                  // LibVA句柄
    void                       *pSurfaceHeap;           // 表面堆
    MediaLibvaCaps             *pCapsDDI;               // 能力接口
    CodechalSetting           *codechalSettings;        // 编解码设置
    // 更多成员...
} DDI_MEDIA_CONTEXT, *PDDI_MEDIA_CONTEXT;

// 媒体表面
typedef struct _DDI_MEDIA_SURFACE
{
    PDDI_MEDIA_CONTEXT     pMediaCtx;        // 媒体上下文
    MOS_SURFACE            OsSurface;        // OS表面
    VASurfaceID            surfaceID;        // VA表面ID
    uint32_t               iPitch;           // 行间距
    uint32_t               iHeight;          // 高度
    uint32_t               dwHandle;         // 资源句柄
    bool                   bMapped;          // 是否映射
    void                   *pData;           // 数据指针
    // 更多成员...
} DDI_MEDIA_SURFACE, *PDDI_MEDIA_SURFACE;

// 媒体缓冲区
typedef struct _DDI_MEDIA_BUFFER
{
    PDDI_MEDIA_CONTEXT     pMediaCtx;        // 媒体上下文
    MOS_RESOURCE           resource;         // MOS资源
    VABufferID             bufferID;         // VA缓冲区ID
    uint32_t               iSize;            // 大小
    uint32_t               iFlags;           // 标志
    uint32_t               uiType;           // 类型
    bool                   bMapped;          // 是否映射
    void                   *pData;           // 数据指针
    // 更多成员...
} DDI_MEDIA_BUFFER, *PDDI_MEDIA_BUFFER;
```

这些数据结构的作用：
- 提供VA-API所需的资源管理
- 跟踪资源分配和映射状态
- 作为HAL层和应用程序之间的桥梁

### HAL层数据结构

HAL层提供平台无关的硬件抽象，包含以下关键数据结构：

```cpp
// 编解码器基类
class CodechalDevice
{
public:
    CodechalDevice(void *hwInterface);
    virtual ~CodechalDevice();

    // 虚函数，由特定平台实现
    virtual MOS_STATUS Initialize() = 0;
    
    // 更多方法...
    
protected:
    void *m_hwInterface;
    // 更多成员...
};

// 解码器基类
class CodechalDecode
{
public:
    CodechalDecode(CodechalSetting *settings);
    virtual ~CodechalDecode();
    
    virtual MOS_STATUS AllocateResources() = 0;
    virtual MOS_STATUS Execute(void *params) = 0;
    
    // 更多方法...
    
protected:
    CodechalHwInterface       *m_hwInterface;
    MOS_INTERFACE             *m_osInterface;
    CodechalDebugInterface    *m_debugInterface;
    // 更多成员...
};

// 编码器基类
class CodechalEncoderState
{
public:
    CodechalEncoderState(CodechalHwInterface *hwInterface);
    virtual ~CodechalEncoderState();
    
    virtual MOS_STATUS Initialize() = 0;
    virtual MOS_STATUS Execute() = 0;
    
    // 更多方法...
    
protected:
    CodechalHwInterface       *m_hwInterface;
    MOS_INTERFACE             *m_osInterface;
    PMOS_RESOURCE             m_presRawSurface;
    // 更多成员...
};
```

HAL层数据结构的特点：
- 使用面向对象设计提供清晰的接口
- 通过虚函数支持多态，适应不同GPU架构
- 封装硬件细节，简化上层实现

### MOS层数据结构

MOS层提供操作系统相关服务，是Media Driver的基础设施：

```cpp
// OS接口
typedef struct _MOS_INTERFACE
{
    MOS_GPU_CONTEXT          CurrentGpuContextOrdinal;    // 当前GPU上下文序号
    MOS_GPU_NODE             CurrentGpuContextHandle;     // 当前GPU上下文句柄
    uint32_t                 dwBufMgr;                    // 缓冲区管理器
    void                     *pvBufMgr;                   // 缓冲区管理器指针
    uint32_t                 Component;                   // 组件标识
    bool                     bDeallocateOnExit;           // 退出时释放标志
    bool                     bUsesCmdBufHeader;           // 使用命令缓冲头标志
    bool                     bUsesCmdBufHeaderInResize;   // 缩放时使用命令缓冲头标志
    // 更多成员...

    // 函数指针
    MOS_STATUS (* pfnSetGpuContext) (PMOS_INTERFACE, MOS_GPU_CONTEXT);
    MOS_STATUS (* pfnAllocateResource) (PMOS_INTERFACE, PMOS_ALLOC_GFXRES_PARAMS, PMOS_RESOURCE);
    void       (* pfnFreeResource) (PMOS_INTERFACE, PMOS_RESOURCE);
    // 更多函数指针...
} MOS_INTERFACE, *PMOS_INTERFACE;

// 命令缓冲区
typedef struct _MOS_COMMAND_BUFFER
{
    uint32_t       iSize;                  // 大小
    uint32_t       iRemaining;             // 剩余空间
    uint8_t        *pCmdBase;              // 命令基址
    uint8_t        *pCmdPtr;               // 命令指针
    int32_t        iCurrent;               // 当前位置
    MOS_RESOURCE   OsResource;             // OS资源
} MOS_COMMAND_BUFFER, *PMOS_COMMAND_BUFFER;

// 资源
typedef struct _MOS_RESOURCE
{
    MOS_RESOURCE_TYPE Type;                // 资源类型
    MOS_GFXRES_TYPE   Format;              // 格式
    uint32_t          iWidth;              // 宽度
    uint32_t          iHeight;             // 高度
    uint32_t          iPitch;              // 行间距
    uint32_t          iCount;              // 计数
    uint32_t          iAllocationIndex;    // 分配索引
    bool              bNotLockable;        // 不可锁定标志
    // 更多成员...
} MOS_RESOURCE, *PMOS_RESOURCE;
```

MOS层的重要特性：
- 提供资源分配和管理
- 封装操作系统差异
- 支持命令缓冲区管理和提交
- 实现内存管理和同步原语

### 硬件特定数据结构

不同代次的Intel GPU有特定的数据结构和实现：

```cpp
// Gen9特定实现
class CodechalDecodeAvcG9 : public CodechalDecodeAvc
{
public:
    CodechalDecodeAvcG9(CodechalHwInterface *hwInterface);
    virtual ~CodechalDecodeAvcG9();

    // 实现平台特定功能
    virtual MOS_STATUS AllocateResources() override;
    virtual MOS_STATUS InitMmcState() override;
    
    // 更多方法...
};

// Gen12特定实现
class CodechalDecodeAvcG12 : public CodechalDecodeAvcG9
{
public:
    CodechalDecodeAvcG12(CodechalHwInterface *hwInterface);
    virtual ~CodechalDecodeAvcG12();

    // 实现平台特定功能
    virtual MOS_STATUS AddPictureCmds(MOS_COMMAND_BUFFER *cmdBuffer) override;
    
    // 更多方法...
};
```

这种设计的优势：
- 最大限度复用公共代码
- 针对新平台只需实现差异部分
- 通过继承结构清晰表达平台间的演进关系

## 关键组件分析

### 媒体接口层

媒体接口层(MediaLibvaInterface)是VA-API与Media Driver之间的桥梁：

```cpp
// VA API入口点实现
VAStatus MediaLibvaInterfaceNext::CreateConfig(
    VADriverContextP ctx,
    VAProfile profile,
    VAEntrypoint entrypoint,
    VAConfigAttrib *attrib_list,
    int num_attribs,
    VAConfigID *config_id)
{
    DDI_FUNC_ENTER();
    
    PDDI_MEDIA_CONTEXT mediaCtx = DdiMedia_GetMediaContext(ctx);
    DDI_CHK_NULL(mediaCtx, "nullptr mediaCtx", VA_STATUS_ERROR_INVALID_CONTEXT);
    
    // 实现配置创建逻辑
    // ...
    
    return VA_STATUS_SUCCESS;
}
```

媒体接口层的功能：
- 验证参数
- 转换VA-API调用为内部接口调用
- 处理资源分配和释放
- 实现错误处理和日志记录

### 编解码HAL

编解码HAL层实现具体的编解码功能：

```cpp
// H.264解码执行示例
MOS_STATUS CodechalDecodeAvc::Execute(void *params)
{
    MOS_STATUS status = MOS_STATUS_SUCCESS;
    
    // 获取参数
    PCODECHAL_DECODE_PARAMS decodeParams = (PCODECHAL_DECODE_PARAMS)params;
    
    // 处理解码参数
    // ...
    
    // 设置图片参数
    status = SetPictureStructs();
    
    // 分配资源
    status = AllocateResources();
    
    // 发送命令
    status = SendPictureCmds();
    
    // 发送切片命令
    status = SendSliceCmds();
    
    return status;
}
```

编解码HAL层的职责：
- 解析编解码参数
- 配置硬件状态
- 构建GPU命令序列
- 管理编解码资源

### 媒体内存管理

Media Driver实现了复杂的内存管理系统：

```cpp
// 资源分配示例
MOS_STATUS MosUtilities::MosAllocateResource(
    PMOS_INTERFACE osInterface,
    PMOS_ALLOC_GFXRES_PARAMS params,
    PMOS_RESOURCE resource)
{
    MOS_OS_FUNCTION_ENTER;
    
    // 验证参数
    if (osInterface == nullptr || params == nullptr || resource == nullptr)
    {
        MOS_OS_ASSERTMESSAGE("Invalid parameters");
        return MOS_STATUS_INVALID_PARAMETER;
    }
    
    // 调用OS特定实现
    return osInterface->pfnAllocateResource(osInterface, params, resource);
}
```

内存管理功能包括：
- 管理图形内存分配
- 处理内存映射和解映射
- 支持压缩和平铺内存格式
- 优化内存布局和访问模式

### GPU命令构建

Media Driver通过构建命令序列控制GPU执行：

```cpp
// 命令构建示例
MOS_STATUS CodechalEncodeAvcBase::SendAvcImgStateCmd(PMOS_COMMAND_BUFFER cmdBuffer)
{
    MOS_STATUS status = MOS_STATUS_SUCCESS;
    
    // 创建MFX_AVC_IMG_STATE命令
    MHW_VDBOX_AVC_IMG_PARAMS imgParams;
    SetAvcImgStateParams(imgParams);
    
    // 发送命令
    status = m_mfxInterface->AddMfxAvcImgCmd(cmdBuffer, &imgParams);
    
    return status;
}
```

命令构建的关键特点：
- 针对不同代GPU优化命令序列
- 支持条件命令和参数计算
- 实现命令打包和批处理
- 处理命令依赖和同步点

## UML图表

### 架构层次图

```
+------------------+
| 应用程序         |
| (FFmpeg等)       |
+------------------+
         |
         | VA-API调用
         v
+------------------+
| libva库          |
| (VA-API实现)     |
+------------------+
         |
         | 驱动调用
         v
+------------------+
| Media Driver     |
| (DDI层)          |
+------------------+
         |
         v
+------------------+     +------------------+     +------------------+
| 解码HAL          |     | 编码HAL          |     | VP HAL           |
| (解码实现)       |     | (编码实现)       |     | (视频处理)       |
+------------------+     +------------------+     +------------------+
         |                        |                        |
         v                        v                        v
+------------------+     +------------------+     +------------------+
| MOS层            |     | MHW层            |     | 渲染引擎         |
| (系统服务)       |     | (硬件接口)       |     | (GPU渲染)        |
+------------------+     +------------------+     +------------------+
         |                        |                        |
         v                        v                        v
+---------------------------------------------------------------+
|                        Intel GPU硬件                           |
+---------------------------------------------------------------+
```

### 类关系图

```
+--------------------+       +--------------------+       +--------------------+
|                    |       |                    |       |                    |
| MediaLibvaInterface|------>| DDI_MEDIA_CONTEXT  |------>| MediaLibvaCaps     |
| (VA-API接口实现)   |       | (驱动上下文)       |       | (能力查询)         |
|                    |       |                    |       |                    |
+--------------------+       +--------------------+       +--------------------+
                                      |
                                      |
                                      v
+--------------------+       +--------------------+       +--------------------+
|                    |       |                    |       |                    |
| CodechalDecode     |<------| Codechal           |------>| CodechalEncode     |
| (解码实现)         |       | (编解码共享)       |       | (编码实现)         |
|                    |       |                    |       |                    |
+--------------------+       +--------------------+       +--------------------+
         |                           |                            |
         |                           |                            |
         v                           v                            v
+--------------------+       +--------------------+       +--------------------+
|                    |       |                    |       |                    |
| MhwVdboxMfxInterface|      | MOS_INTERFACE      |       |MhwVdboxHucInterface|
| (MFX硬件接口)      |       | (OS接口)           |       | (HUC硬件接口)      |
|                    |       |                    |       |                    |
+--------------------+       +--------------------+       +--------------------+
```

### 处理流程图

```
+-----------------+       +-----------------+       +-----------------+
| 1. 初始化上下文 |------>| 2. 配置编解码器 |------>| 3. 分配资源     |
+-----------------+       +-----------------+       +-----------------+
        |                          |                         |
        v                          v                         v
+-----------------+       +-----------------+       +-----------------+
| 4. 构建命令     |------>| 5. 提交命令     |------>| 6. 同步等待     |
+-----------------+       +-----------------+       +-----------------+
        |                          |                         |
        v                          v                         v
+-----------------+       +-----------------+       +-----------------+
| 7. 获取结果     |------>| 8. 资源清理     |------>| 9. 上下文销毁   |
+-----------------+       +-----------------+       +-----------------+
```

### 组件交互图

```
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
| VA-API应用     |---->| libva库        |---->| Media Driver   |
| (FFmpeg)       |     | (接口层)       |     | (DDI层)        |
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
                                                      |
                                                      v
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
| GPU执行        |<----| 命令缓冲区     |<----| HAL层          |
| (硬件)         |     | (命令序列)     |     | (抽象层)       |
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
       |                                             |
       |                                             |
       v                                             v
+----------------+                           +----------------+
|                |                           |                |
| 图形内存       |<------------------------->| MOS层          |
| (GPU内存)      |                           | (系统服务)     |
|                |                           |                |
+----------------+                           +----------------+
```

这些UML图表展示了Media Driver的整体架构和组件间的关系，帮助理解其设计理念和实现方式。
