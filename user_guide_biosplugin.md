# BIOS Configuration Management System User Guide

This guide provides instructions for deploying and using the BIOS Configuration Management System. This system automates server BIOS configuration management using Kubernetes and the Redfish API.

## Table of Contents
- [1. Prerequisites](#1-prerequisites)
- [2. Installation](#2-installation)
  - [2.1 Using Helm](#21-using-helm)
  - [2.2 Using Kustomize](#22-using-kustomize)
- [3. Using the System](#3-using-the-system)
  - [3.1 Creating BIOS Configurations](#31-creating-bios-configurations)
  - [3.2 Monitoring Status](#32-monitoring-status)
  - [3.3 Common Operations](#33-common-operations)
- [4. Configuration Examples](#4-configuration-examples)
  - [4.1 Basic Configuration](#41-basic-configuration)
  - [4.2 Multi-Server Configuration](#42-multi-server-configuration)
- [5. Troubleshooting](#5-troubleshooting)
- [6. Uninstallation](#6-uninstallation)

## 1. Prerequisites

Ensure your environment meets the following requirements:

- **Kubernetes Cluster**: v1.24 or higher
- **kubectl**: Configured to access your cluster
- **Helm v3**: For Helm-based installation
- **BMC Access**: Servers with Redfish-compatible BMCs
- **Network Access**: Connectivity from Kubernetes cluster to BMC interfaces
- **Credentials**: BMC username and password for each server

## 2. Installation

### 2.1 Using Helm

Helm is the recommended deployment method:

#### Installation Steps

1. **Add the Helm repository** (if using a published chart)
2. **Create a values file** with your configuration
3. **Install the chart**:
   ```
   helm install bios-controller ./helm/biosplugin -n iac-system --create-namespace
   ```
4. **Verify installation**:
   ```
   kubectl get pods -n iac-system
   kubectl get crd | grep biosplugin
   ```

#### Configuration Options

Key configuration parameters in `values.yaml`:
- `image.repository`: Container image repository
- `image.tag`: Image version
- `replicaCount`: Number of controller replicas
- `resources`: CPU and memory limits

### 2.2 Using Kustomize

Alternative deployment using Kubernetes native tools:

#### Installation Steps

1. **Deploy CRDs first**:
   ```
   kubectl apply -k config/crd
   ```
2. **Deploy the controller**:
   ```
   kubectl apply -k config/default
   ```
3. **Verify deployment**:
   ```
   kubectl get pods -n iac-system
   kubectl get biosplugin
   ```

## 3. Using the System

### 3.1 Creating BIOS Configurations

Create a BiosPlugin resource to manage server BIOS settings:

#### Basic BiosPlugin Resource

```yaml
apiVersion: iac.intel.com/v1
kind: BiosPlugin
metadata:
  name: server-01-bios
  namespace: iac-system
spec:
  bmc_connection:
    bmc_ipaddr: "*************"
    bmc_username: "admin"
    bmc_password: "password123"
    bmc_type: "idrac"
    bios_info: "Dell-1.7.4"
  bios_settings:
    bios_knobs_info:
      "Boot Mode": "Uefi"
      "Cpu Performance": "High Performance"
      "Memory Frequency": "Maximum Performance"
  compare_values:
    readable_value:
      "Boot Mode": "UEFI Boot Mode"
      "Cpu Performance": "High Performance Mode"
```

#### Resource Creation Steps

1. **Create the resource file** (e.g., `server-bios.yaml`)
2. **Apply the configuration**:
   ```
   kubectl apply -f server-bios.yaml
   ```
3. **Verify creation**:
   ```
   kubectl get biosplugin -n iac-system
   ```

### 3.2 Monitoring Status

Check the status of your BIOS configurations:

#### Status Commands

- **List all resources**:
  ```
  kubectl get biosplugin -n iac-system
  ```
- **View detailed status**:
  ```
  kubectl describe biosplugin server-01-bios -n iac-system
  ```
- **Check controller logs**:
  ```
  kubectl logs -n iac-system deployment/bios-controller
  ```

#### Status Indicators

- **Ready**: Configuration successfully applied
- **Updating**: Configuration changes in progress
- **Failed**: Configuration update failed
- **Pending**: Waiting for processing

### 3.3 Common Operations

#### Updating BIOS Settings

1. **Edit the resource**:
   ```
   kubectl edit biosplugin server-01-bios -n iac-system
   ```
2. **Or apply updated file**:
   ```
   kubectl apply -f updated-server-bios.yaml
   ```

#### Viewing Configuration

- **Get current configuration**:
  ```
  kubectl get biosplugin server-01-bios -n iac-system -o yaml
  ```
- **Check verification results**:
  ```
  kubectl get biosplugin server-01-bios -n iac-system -o jsonpath='{.status.verificationStatus}'
  ```

## 4. Configuration Examples

### 4.1 Basic Configuration

Example for a single Dell server:

```yaml
apiVersion: iac.intel.com/v1
kind: BiosPlugin
metadata:
  name: dell-server-01
  namespace: iac-system
spec:
  bmc_connection:
    bmc_ipaddr: "*************"
    bmc_username: "root"
    bmc_password: "calvin"
    bmc_type: "idrac"
    bios_info: "Dell-1.7.4"
  bios_settings:
    bios_knobs_info:
      "Boot Mode": "Uefi"
      "Virtualization Technology": "Enabled"
      "Hyper-Threading": "Enabled"
      "C-States": "Enabled"
  compare_values:
    readable_value:
      "Boot Mode": "UEFI Boot Mode"
      "Virtualization Technology": "Intel VT Enabled"
```

### 4.2 Multi-Server Configuration

Example for managing multiple servers:

Create separate BiosPlugin resources for each server:

**Server 1 (dell-server-01.yaml):**
```yaml
apiVersion: iac.intel.com/v1
kind: BiosPlugin
metadata:
  name: dell-server-01
  namespace: iac-system
  labels:
    server-role: compute
    vendor: dell
spec:
  bmc_connection:
    bmc_ipaddr: "*************"
    bmc_username: "root"
    bmc_password: "calvin"
    bmc_type: "idrac"
  bios_settings:
    bios_knobs_info:
      "Boot Mode": "Uefi"
      "Cpu Performance": "High Performance"
```

**Server 2 (dell-server-02.yaml):**
```yaml
apiVersion: iac.intel.com/v1
kind: BiosPlugin
metadata:
  name: dell-server-02
  namespace: iac-system
  labels:
    server-role: compute
    vendor: dell
spec:
  bmc_connection:
    bmc_ipaddr: "*************"
    bmc_username: "root"
    bmc_password: "calvin"
    bmc_type: "idrac"
  bios_settings:
    bios_knobs_info:
      "Boot Mode": "Uefi"
      "Cpu Performance": "High Performance"
```

Apply all configurations:
```bash
kubectl apply -f dell-server-01.yaml
kubectl apply -f dell-server-02.yaml
```

Monitor all servers:
```bash
kubectl get biosplugin -n iac-system -l server-role=compute
```

## 5. Troubleshooting

### Common Issues and Solutions

#### Controller Not Running
**Symptoms**: BiosPlugin resources remain in "Pending" status
**Check**:
```bash
kubectl get pods -n iac-system
kubectl logs -n iac-system deployment/bios-controller
```
**Solutions**:
- Verify controller pod is running and ready
- Check resource limits and node capacity
- Review controller logs for startup errors

#### BMC Connection Issues
**Symptoms**: "Failed to connect to BMC" errors in logs
**Check**:
```bash
kubectl describe biosplugin <resource-name> -n iac-system
```
**Solutions**:
- Verify BMC IP address is correct and reachable
- Test BMC credentials manually
- Check firewall rules for port 443 access
- Confirm BMC Redfish API is enabled

#### Authentication Failures
**Symptoms**: "Authentication failed" errors
**Solutions**:
- Verify BMC username and password are correct
- Check if BMC user has sufficient privileges
- Try logging into BMC web interface manually
- Reset BMC credentials if necessary

#### Parameter Validation Errors
**Symptoms**: "Invalid parameter" or "Parameter not found" errors
**Solutions**:
- Check BIOS parameter names against vendor documentation
- Verify parameter values are within acceptable ranges
- Use exact parameter names as shown in BMC interface
- Review vendor-specific parameter variations

#### Two-Phase Update Issues
**Symptoms**: Updates stuck with pending annotations
**Check**:
```bash
kubectl get biosplugin <resource-name> -n iac-system -o yaml | grep annotations
```
**Solutions**:
- Wait for automatic retry (controller handles this)
- Check controller logs for dependency resolution details
- Manually clear stuck annotations if needed:
  ```bash
  kubectl annotate biosplugin <resource-name> -n iac-system iac.intel.com/pendingSecondPhase-
  ```

## 6. Uninstallation

### Using Helm

To remove the system installed with Helm:

1. **Backup existing configurations** (optional):
   ```bash
   kubectl get biosplugin -n iac-system -o yaml > biosplugin-backup.yaml
   ```

2. **Uninstall the Helm release**:
   ```bash
   helm uninstall bios-controller -n iac-system
   ```

3. **Remove the namespace** (if no longer needed):
   ```bash
   kubectl delete namespace iac-system
   ```

### Using Kustomize

To remove the system installed with Kustomize:

1. **Backup existing configurations** (optional):
   ```bash
   kubectl get biosplugin -o yaml > biosplugin-backup.yaml
   ```

2. **Remove controller components**:
   ```bash
   kubectl delete -k config/default
   ```

3. **Remove CRDs** (⚠️ This will delete all BiosPlugin resources):
   ```bash
   kubectl delete -k config/crd
   ```

---

For more advanced usage scenarios and detailed technical information, please refer to the [Design Document](design_detail.md).
