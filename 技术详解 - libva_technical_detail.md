# libva 详细技术分析

本文档对 libva (Video Acceleration API) 的架构设计、核心数据结构和实现模式进行详细分析，作为英特尔媒体加速方案技术分析的补充内容。

## 代码仓库信息

- **Git仓库地址**: https://github.com/intel/libva.git
- **本地路径**: /home/<USER>/INTEL_MEDIA/libva

## 目录

- [设计模式与实现](#设计模式与实现)
  - [抽象工厂模式](#抽象工厂模式)
  - [适配器模式](#适配器模式)
  - [策略模式](#策略模式)
  - [驱动注册机制](#驱动注册机制)
  - [上下文管理设计](#上下文管理设计)
- [核心数据结构](#核心数据结构)
  - [基础数据类型](#基础数据类型)
  - [显示和设备抽象](#显示和设备抽象)
  - [配置结构体](#配置结构体)
  - [表面和缓冲区管理](#表面和缓冲区管理)
  - [上下文结构体](#上下文结构体)
- [UML图表](#uml图表)
  - [主要类关系图](#主要类关系图)
  - [接口层次图](#接口层次图)
  - [API调用序列图](#api调用序列图)
  - [后端实现类图](#后端实现类图)

## 设计模式与实现

libva 采用了多种设计模式，以实现良好的可扩展性、平台独立性和高性能。

### 抽象工厂模式

libva 使用抽象工厂模式来隔离平台相关的实现细节，允许不同的硬件驱动提供特定的功能实现。这种设计通过定义抽象接口和功能表来实现：

```c
// VA驱动虚函数表，代表所有驱动实现需要提供的功能接口
struct VADriverVTable {
    VAStatus(*vaTerminate)(VADriverContextP ctx);
    VAStatus(*vaQueryConfigProfiles)(VADriverContextP ctx, VAProfile *profile_list, int *num_profiles);
    VAStatus(*vaQueryConfigEntrypoints)(VADriverContextP ctx, VAProfile profile, VAEntrypoint *entrypoint_list, int *num_entrypoints);
    // 更多函数指针...
};
```

这种实现允许：
- 不同硬件厂商实现自己的驱动
- 应用程序通过统一API调用硬件加速功能
- 运行时选择合适的后端实现

media-driver 作为 Intel GPU 的 VA-API 实现，在其 `media_libva_interface.cpp` 中填充了这些函数表：

```cpp
pVTable->vaCreateConfig = MediaLibvaInterfaceNext::CreateConfig;
pVTable->vaDestroyConfig = MediaLibvaInterfaceNext::DestroyConfig;
pVTable->vaQueryConfigAttributes = MediaLibvaInterfaceNext::QueryConfigAttributes;
// 更多实现...
```

### 适配器模式

libva 使用适配器模式处理不同显示系统（X11、Wayland、DRM）之间的差异：

```c
// 不同显示系统的适配器实现
typedef struct VADisplayContext {
    VADisplayContextP pNext;
    VADisplay display;
    int flags; // VA_DISPLAY_*
    void *opaque; // window system dependent data
    VAStatus (*vaGetDriverName)(VADisplayContextP ctx, char **driver_name);
    VAStatus (*vaTerminate)(VADisplayContextP ctx);
    VAStatus (*vaInitialize)(VADisplayContextP ctx, int *major_version, int *minor_version);
} VADisplayContext;
```

不同的显示后端对应不同的库：
- `libva-x11.so` - X11窗口系统支持
- `libva-drm.so` - DRM (Direct Rendering Manager) 支持
- `libva-wayland.so` - Wayland窗口系统支持

这种设计的优势：
- 隔离平台相关代码
- 只加载必要的后端实现
- 简化跨平台开发

### 策略模式

libva 使用策略模式来处理不同的编解码实现和处理方法：

```c
// 通过VA_PICTURE_*标志选择不同的处理策略
#define VA_FRAME_PICTURE        0x00000000
#define VA_TOP_FIELD            0x00000001
#define VA_BOTTOM_FIELD         0x00000002
#define VA_TOP_FIELD_FIRST      0x00000004
#define VA_BOTTOM_FIELD_FIRST   0x00000008

// 通过VA_FILTER_*标志选择不同的缩放策略
#define VA_FILTER_SCALING_DEFAULT       0x00000000
#define VA_FILTER_SCALING_FAST          0x00000100
#define VA_FILTER_SCALING_HQ            0x00000200
#define VA_FILTER_SCALING_NL_ANAMORPHIC 0x00000300
```

这种设计允许应用程序根据需求选择合适的处理算法，而底层实现可以针对不同策略优化性能。

### 驱动注册机制

libva 提供了驱动发现和注册机制，允许系统自动加载适当的驱动实现：

1. 驱动实现提供 `__vaDriverInit_<version>` 函数作为入口点
2. libva 通过 `dlsym()` 动态加载驱动库并定位入口函数
3. 驱动初始化时注册其功能表到 VA 上下文

media-driver 在 `media_libva.cpp` 中实现了这一机制：

```cpp
// 入口函数，被libva动态加载
VAStatus __vaDriverInit_1_0(VADriverContextP ctx)
{
    return DdiMedia_LoadFuncion(ctx);
}

// 注册功能实现
VAStatus DdiMedia_LoadFuncion(VADriverContextP ctx)
{
    ctx->vtable = &vtable;
    ctx->vtable_vpp = &vtable_vpp;
    // 设置函数指针...
    return VA_STATUS_SUCCESS;
}
```

### 上下文管理设计

libva 采用上下文对象来管理资源生命周期和状态：

1. **VADisplay上下文**：表示与窗口系统的连接
2. **VADriverContext**：包含驱动实现和驱动私有数据
3. **VAContextID**：表示编解码会话

这种分层上下文设计实现了清晰的资源管理和生命周期控制：

```c
typedef struct VADriverContext {
    void *pDriverData; // 驱动私有数据
    struct VADriverVTable *vtable; // 主功能表
    struct VADriverVTableVPP *vtable_vpp; // VPP功能表
    // 其他成员...
} VADriverContext;
```

## 核心数据结构

### 基础数据类型

libva 定义了多种基础数据类型，用于标识和操作各种资源：

```c
// 不透明句柄类型
typedef void* VADisplay;
typedef unsigned int VAConfigID;
typedef unsigned int VAContextID;
typedef unsigned int VASurfaceID;
typedef unsigned int VABufferID;
typedef unsigned int VAImageID;
typedef unsigned int VASubpictureID;

// 返回状态码
typedef int VAStatus;
#define VA_STATUS_SUCCESS 0x00000000
#define VA_STATUS_ERROR_OPERATION_FAILED 0x00000001
// 更多状态码...
```

这些基础类型的特点：
- 使用不透明句柄隐藏实现细节
- 通过整数ID标识资源
- 详细的错误状态码帮助调试

### 显示和设备抽象

libva 通过显示抽象提供了平台无关的设备访问方式：

```c
// 显示类型枚举
enum {
    VA_DISPLAY_X11 = 0x10,
    VA_DISPLAY_GLX = (VA_DISPLAY_X11 | (1 << 0)),
    VA_DISPLAY_DRM = 0x30,
    VA_DISPLAY_WAYLAND = 0x40,
    VA_DISPLAY_WIN32 = 0x80,
    // 更多显示类型...
};

// 显示上下文结构
typedef struct VADisplayContext {
    VADisplayContextP pNext;
    VADisplay display;
    int flags; // VA_DISPLAY_*
    void *opaque; // 窗口系统相关数据
    // 函数指针...
} VADisplayContext;
```

对应的创建函数：
- `vaGetDisplay()` - 获取X11显示
- `vaGetDisplayDRM()` - 获取DRM显示
- `vaGetDisplayWl()` - 获取Wayland显示

### 配置结构体

libva 使用配置结构体描述编解码会话的参数：

```c
// 配置属性类型
typedef enum {
    VAConfigAttribRTFormat = 0,
    VAConfigAttribRateControl = 1,
    VAConfigAttribDecSliceMode = 2,
    // 更多属性类型...
} VAConfigAttribType;

// 配置属性结构
typedef struct _VAConfigAttrib {
    VAConfigAttribType type;
    uint32_t value;
} VAConfigAttrib;
```

配置系统特点：
- 使用键值对形式表示属性
- 可扩展设计支持新的属性类型
- 通过位标志表示组合选项

### 表面和缓冲区管理

libva 中的核心数据交换单元是表面和缓冲区：

```c
// 表面属性
typedef struct _VASurfaceAttrib {
    VASurfaceAttribType type;
    VAGenericValueType flags;
    VAGenericValue value;
} VASurfaceAttrib;

// 图像格式
typedef struct _VAImageFormat {
    uint32_t fourcc;
    uint32_t byte_order;
    uint32_t bits_per_pixel;
    // 其他字段...
} VAImageFormat;

// 图像结构
typedef struct _VAImage {
    VAImageID image_id;
    VAImageFormat format;
    uint32_t width;
    uint32_t height;
    uint32_t data_size;
    uint32_t num_planes;
    // 平面信息...
} VAImage;
```

缓冲区类型丰富，支持各种编解码数据：

```c
// 缓冲区类型
typedef enum {
    VAPictureParameterBufferType = 0,
    VAIQMatrixBufferType = 1,
    VABitPlaneBufferType = 2,
    VASliceParameterBufferType = 3,
    VASliceDataBufferType = 4,
    // 更多缓冲区类型...
} VABufferType;
```

### 上下文结构体

上下文是 libva 中编解码会话的核心，包含了所有会话相关信息：

```c
// 上下文是通过ID引用的不透明对象
typedef unsigned int VAContextID;

// 创建上下文的函数签名
VAStatus vaCreateContext(
    VADisplay dpy,
    VAConfigID config_id,
    int picture_width,
    int picture_height,
    int flag,
    VASurfaceID *render_targets,
    int num_render_targets,
    VAContextID *context
);
```

上下文相关操作包括：
- 创建和销毁上下文
- 开始和结束图像处理
- 提交缓冲区进行处理

## UML图表

### 主要类关系图

```
+-----------------+        +-----------------+        +------------------+
|                 |        |                 |        |                  |
|   VADisplay     |<------>| VADriverContext |<------>| VADriverVTable   |
| (显示连接)      |        | (驱动上下文)    |        | (功能函数表)     |
|                 |        |                 |        |                  |
+-----------------+        +-----------------+        +------------------+
         |                        |                           |
         |                        |                           |
         v                        v                           v
+-----------------+        +-----------------+        +------------------+
|                 |        |                 |        |                  |
| VADisplayContext|        |  VAConfigID     |------->|   VAContextID    |
| (显示上下文)    |        | (配置对象)      |        | (编解码上下文)   |
|                 |        |                 |        |                  |
+-----------------+        +-----------------+        +------------------+
                                    |                          |
                                    |                          |
                                    v                          v
                           +-----------------+        +------------------+
                           |                 |        |                  |
                           | VAConfigAttrib  |        |    VABufferID    |
                           | (配置属性)      |        | (数据缓冲区)     |
                           |                 |        |                  |
                           +-----------------+        +------------------+
                                                                |
                                                                |
                                                                v
                                                      +------------------+
                                                      |                  |
                                                      |   VASurfaceID    |
                                                      | (解码/编码表面)  |
                                                      |                  |
                                                      +------------------+
```

### 接口层次图

```
+---------------------+
|                     |
|  应用程序           |
|  (FFmpeg等)         |
|                     |
+---------------------+
           |
           | VA-API调用
           v
+---------------------+     +--------------------+     +-------------------+
|                     |     |                    |     |                   |
|  libva核心          |---->| libva-x11/drm/wl   |---->|  VA驱动实现       |
| (通用实现)          |     | (显示后端)         |     | (media-driver等)  |
|                     |     |                    |     |                   |
+---------------------+     +--------------------+     +-------------------+
           |                         |                         |
           |                         |                         |
           v                         v                         v
+---------------------+     +--------------------+     +-------------------+
|                     |     |                    |     |                   |
| 表面/缓冲区管理     |     | 窗口系统交互       |     | 硬件加速实现      |
| (通用抽象)          |     | (系统特定)         |     | (厂商特定)        |
|                     |     |                    |     |                   |
+---------------------+     +--------------------+     +-------------------+
```

### API调用序列图

```
+---------------+     +---------------+     +---------------+     +---------------+
| 1. vaInitialize() |---->| 2. vaCreateConfig() |---->| 3. vaCreateSurfaces() |---->| 4. vaCreateContext() |
+---------------+     +---------------+     +---------------+     +---------------+
        |                     |                     |                     |
        v                     v                     v                     v
+---------------+     +---------------+     +---------------+     +---------------+
| 5. vaCreateBuffer() |---->| 6. vaBeginPicture() |---->| 7. vaRenderPicture() |---->| 8. vaEndPicture() |
+---------------+     +---------------+     +---------------+     +---------------+
        |                     |                     |                     |
        v                     v                     v                     v
+---------------+     +---------------+     +---------------+     +---------------+
| 9. vaSyncSurface() |---->| 10. vaGetImage() |---->| 11. vaDestroyXXX() |---->| 12. vaTerminate() |
+---------------+     +---------------+     +---------------+     +---------------+
```

### 后端实现类图

```
+--------------------+
|                    |
| MediaLibvaInterface|
| (VA-API接口实现)   |
|                    |
+--------------------+
          |
          |
          v
+--------------------+     +--------------------+     +--------------------+
|                    |     |                    |     |                    |
| MediaLibvaCaps     |---->| MediaLibvaContext  |---->| MediaLibvaCodec    |
| (能力查询)         |     | (上下文管理)       |     | (编解码实现)       |
|                    |     |                    |     |                    |
+--------------------+     +--------------------+     +--------------------+
          |                         |                          |
          |                         |                          |
          v                         v                          v
+--------------------+     +--------------------+     +--------------------+
|                    |     |                    |     |                    |
| MediaLibvaCapsFactory|   | MediaLibvaBuffer   |     | CodecHal          |
| (工厂模式实现)     |     | (缓冲区管理)       |     | (硬件抽象层)      |
|                    |     |                    |     |                    |
+--------------------+     +--------------------+     +--------------------+
```

这些UML图表展示了libva的整体架构和组件间的关系，帮助理解其设计理念和实现方式。
