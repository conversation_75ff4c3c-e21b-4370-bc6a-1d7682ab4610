# BIOS Configuration Management System

## What is GitOps?

GitOps is an operational framework that applies DevOps best practices used for application development to infrastructure automation. With GitOps, the entire system is described declaratively in a Git repository that serves as the single source of truth for the infrastructure and application configurations.

### Purpose of GitOps

GitOps provides the following key benefits:

- **Single Source of Truth**: All system configurations are stored in Git, providing version control, history, and audit trails
- **Declarative Infrastructure**: Infrastructure is defined as code, describing the desired state rather than imperative steps
- **Automated Reconciliation**: Automated systems continuously reconcile the actual state with the desired state
- **Improved Security**: Leverages Git's authentication and authorization mechanisms for infrastructure changes
- **Simplified Rollbacks**: Easy rollback to previous states using Git's version control capabilities
- **Enhanced Collaboration**: Enables team collaboration through Git workflows like pull requests and code reviews

### GitOps Workflow Diagram

```
+----------------+     +-------------------+     +----------------------+
|                |     |                   |     |                      |
| Git Repository |---->| GitOps Controller |---->| Kubernetes Cluster   |
| (Source of     |     | (Flux/ArgoCD)     |     | (Target Environment) |
|  Truth)        |     |                   |     |                      |
+----------------+     +-------------------+     +----------------------+
        ^                       |                        |
        |                       |                        |
        +------------------------                        |
        |                Monitors for Drift              |
        |                                                |
        +------------------------------------------------+
                   Reports Status & Events
```

1. **Developers commit changes** to the Git repository containing infrastructure configurations
2. **GitOps controller (like Flux or ArgoCD) detects changes** in the Git repository
3. **Controller applies changes** to the target environment (Kubernetes cluster)
4. **Controller continuously monitors** for drift between desired state (Git) and actual state (cluster)
5. **Status and events are reported back** to the Git repository or monitoring systems

## Purpose of the Infrastructure as Code (IaC) Project

This Infrastructure as Code (IaC) project automates the management of server BIOS configurations using Kubernetes and GitOps principles. The system addresses key challenges in data center operations:

- **Scalability**: Manage BIOS configurations across thousands of servers efficiently
- **Consistency**: Ensure uniform BIOS settings across server fleets
- **Automation**: Eliminate manual configuration processes prone to errors
- **Verification**: Automatically verify that BIOS settings are applied correctly
- **Vendor Independence**: Support multiple server vendors through a unified interface
- **Dependency Management**: Handle complex BIOS parameter dependencies automatically
- **Security**: Securely manage sensitive server configurations

## Key Features

- **Multi-vendor Support**: Works with Dell, HP, Lenovo, and other server vendors that support Redfish API
- **BIOS Configuration Management**: Apply and verify BIOS settings across your server fleet
- **Dependency Handling**: Automatically manages BIOS parameter dependencies
- **Read-only Parameter Detection**: Identifies and handles read-only BIOS parameters
- **Kubernetes Native**: Runs as a Kubernetes operator with custom resources
- **Helm Chart Deployment**: Easy deployment using Helm charts
- **Two-Phase Updates**: Handles complex parameter dependencies through intelligent update sequencing

## Quick Start

The system uses a custom Kubernetes resource called `BiosPlugin` to manage server BIOS configurations:
When applied to a Kubernetes cluster, the system automatically:
1. Connects to the server BMC via Redfish API
2. Compares current vs desired BIOS configuration
3. Applies necessary changes and reboots if needed
4. Verifies the configuration was applied correctly

## Documentation

For complete information on deployment, usage, and system design:

- **[User Guide](docs/user_guide.md)** - Installation and usage instructions
- **[Design Document](docs/design_detail.md)** - Comprehensive technical details and architecture
