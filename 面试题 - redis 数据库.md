# Redis 面试题全集

## ⚠️ **重要声明**

本文档包含两类内容：
1. **✅ 验证过的真实面试题**: 基于公开的面试经验分享、技术博客和官方文档整理
2. **🔧 技术方向推理题**: 基于各公司技术栈和可能的面试重点进行的合理推理

**请注意**: 标注具体公司的题目不能保证 100% 来自该公司的真实面试，更多是基于该公司的技术特点和行业经验进行的推理。建议将其作为技术学习参考，而非面试题库。

## 目录

- [第一部分：基础概念面试题](#第一部分基础概念面试题)
- [第二部分：数据结构面试题](#第二部分数据结构面试题)
- [第三部分：持久化机制面试题](#第三部分持久化机制面试题)
- [第四部分：缓存问题面试题](#第四部分缓存问题面试题)
- [第五部分：集群与高可用面试题](#第五部分集群与高可用面试题)
- [第六部分：性能优化面试题](#第六部分性能优化面试题)
- [第七部分：分布式锁面试题](#第七部分分布式锁面试题)
- [第八部分：实际应用场景面试题](#第八部分实际应用场景面试题)

---

## 第一部分：基础概念面试题

### 1.1 阿里巴巴面试题

#### Q1: Redis 为什么这么快？
**来源**: 阿里巴巴 P6/P7 技术面试
**难度**: ⭐⭐⭐

**答案**:
1. **纯内存操作**: 数据存储在内存中，避免磁盘I/O
2. **单线程模型**: 避免线程切换和锁竞争开销
3. **高效数据结构**: 针对不同场景优化的数据结构
4. **I/O多路复用**: 使用 epoll/kqueue 处理网络请求
5. **简单协议**: RESP 协议简单高效

**详细解析**:
```c
// Redis 事件循环核心代码 (ae.c)
int aeProcessEvents(aeEventLoop *eventLoop, int flags) {
    int processed = 0, numevents;
    
    // 获取就绪的文件事件
    numevents = aeApiPoll(eventLoop, tvp);
    
    // 处理文件事件
    for (j = 0; j < numevents; j++) {
        aeFileEvent *fe = &eventLoop->events[eventLoop->fired[j].fd];
        
        if (fe->mask & mask & AE_READABLE) {
            fe->rfileProc(eventLoop, fd, fe->clientData, mask);
        }
        if (fe->mask & mask & AE_WRITABLE) {
            fe->wfileProc(eventLoop, fd, fe->clientData, mask);
        }
    }
    
    return processed;
}
```

#### Q2: Redis 和 Memcached 的区别？
**来源**: 阿里巴巴淘宝技术部
**难度**: ⭐⭐

**答案**:
| **特性** | **Redis** | **Memcached** |
|---------|-----------|---------------|
| **数据类型** | 8种数据类型 | 只支持字符串 |
| **持久化** | RDB + AOF | 不支持 |
| **分布式** | 原生集群支持 | 客户端分片 |
| **线程模型** | 单线程 | 多线程 |
| **内存使用** | 相对较高 | 更高效 |
| **功能** | 丰富 (发布订阅、事务等) | 简单 |

### 1.2 腾讯面试题

#### Q3: Redis 的单线程模型是怎样的？
**来源**: 腾讯微信后台开发
**难度**: ⭐⭐⭐

**答案**:
Redis 6.0 之前是单线程，6.0 之后引入了多线程：

**单线程模型 (Redis < 6.0)**:
- 所有命令在一个线程中串行执行
- 使用 I/O 多路复用处理网络请求
- 避免了线程切换和锁的开销

**多线程模型 (Redis >= 6.0)**:
- 网络 I/O 使用多线程
- 命令执行仍然是单线程
- 提高了网络处理能力

```c
// Redis 6.0 多线程网络处理
void *IOThreadMain(void *myid) {
    long id = (long)myid;
    
    while(1) {
        // 等待主线程分配任务
        while(io_threads_pending[id] == 0) {
            usleep(1);
        }
        
        // 处理网络 I/O
        if (io_threads_op == IO_THREADS_OP_WRITE) {
            writeToClient(c, 0);
        } else if (io_threads_op == IO_THREADS_OP_READ) {
            readQueryFromClient(c);
        }
    }
}
```

#### Q4: Redis 的过期策略有哪些？
**来源**: 腾讯游戏后台开发
**难度**: ⭐⭐⭐

**答案**:
Redis 使用两种过期策略：

**1. 惰性删除 (Lazy Expiration)**:
- 访问 key 时检查是否过期
- 过期则删除并返回 nil
- 节省 CPU，但可能占用内存

**2. 定期删除 (Active Expiration)**:
- 定时随机检查过期 key
- 每次检查有时间限制
- 平衡 CPU 和内存使用

```c
// Redis 过期检查核心代码 (expire.c)
int expireIfNeeded(redisDb *db, robj *key) {
    if (!keyIsExpired(db, key)) return 0;
    
    // 如果是从节点，不主动删除
    if (server.masterhost != NULL) return 1;
    
    // 删除过期 key
    deleteKey(db, key);
    server.stat_expiredkeys++;
    
    return 1;
}
```

### 1.3 字节跳动面试题

#### Q5: Redis 的内存淘汰策略有哪些？
**来源**: 字节跳动抖音后端
**难度**: ⭐⭐⭐⭐

**答案**:
Redis 提供 8 种内存淘汰策略：

**1. noeviction (默认)**:
- 不淘汰任何 key
- 内存满时返回错误

**2. allkeys-lru**:
- 从所有 key 中淘汰最近最少使用的

**3. allkeys-lfu**:
- 从所有 key 中淘汰最少使用频率的

**4. allkeys-random**:
- 从所有 key 中随机淘汰

**5. volatile-lru**:
- 从设置过期时间的 key 中淘汰 LRU

**6. volatile-lfu**:
- 从设置过期时间的 key 中淘汰 LFU

**7. volatile-random**:
- 从设置过期时间的 key 中随机淘汰

**8. volatile-ttl**:
- 淘汰即将过期的 key

**LRU 实现原理**:
```c
// Redis LRU 近似算法
typedef struct redisObject {
    unsigned type:4;
    unsigned encoding:4;
    unsigned lru:LRU_BITS;  // LRU 时间戳
    int refcount;
    void *ptr;
} robj;

// 更新 LRU 时间
void updateLRU(robj *o) {
    o->lru = LRU_CLOCK();
}
```

#### Q6: Redis 的 LRU 算法是如何实现的？
**来源**: 字节跳动基础架构
**难度**: ⭐⭐⭐⭐⭐

**答案**:
Redis 使用近似 LRU 算法，而不是标准 LRU：

**标准 LRU 问题**:
- 需要额外的双向链表
- 每次访问都要移动节点
- 内存和时间开销大

**Redis 近似 LRU**:
- 每个对象记录最后访问时间
- 淘汰时随机采样比较
- 默认采样 5 个 key

**配置参数**:
```conf
# 设置采样数量
maxmemory-samples 5

# 设置淘汰策略
maxmemory-policy allkeys-lru
```

**实现代码**:
```c
// 近似 LRU 淘汰算法
robj *evictPoolPopBest(struct evictionPoolEntry *pool) {
    int j, k, count;
    struct evictionPoolEntry *selected = NULL;
    
    // 从采样池中选择最佳淘汰候选
    for (k = EVPOOL_SIZE-1; k >= 0; k--) {
        if (pool[k].key == NULL) continue;
        selected = pool + k;
        break;
    }
    
    return selected ? selected->obj : NULL;
}
```

---

## 第二部分：数据结构面试题

### 2.1 百度面试题

#### Q7: Redis 的 ZSet 是如何实现的？
**来源**: 百度搜索后端
**难度**: ⭐⭐⭐⭐

**答案**:
ZSet (有序集合) 使用两种数据结构：

**1. 跳跃表 (Skip List)**:
- 支持范围查询
- 时间复杂度 O(log N)
- 实现相对简单

**2. 哈希表 (Hash Table)**:
- 支持 O(1) 查找成员
- 存储 member -> score 映射

**跳跃表结构**:
```c
// 跳跃表节点
typedef struct zskiplistNode {
    sds ele;                    // 成员对象
    double score;               // 分值
    struct zskiplistNode *backward;  // 后退指针
    struct zskiplistLevel {
        struct zskiplistNode *forward;  // 前进指针
        unsigned long span;             // 跨度
    } level[];
} zskiplistNode;

// 跳跃表
typedef struct zskiplist {
    struct zskiplistNode *header, *tail;
    unsigned long length;       // 节点数量
    int level;                 // 最大层数
} zskiplist;
```

**为什么选择跳跃表而不是红黑树？**
1. 实现简单，代码可读性好
2. 支持范围查询
3. 内存局部性更好
4. 并发友好

#### Q8: Redis 的 Hash 表是如何解决哈希冲突的？
**来源**: 百度云计算
**难度**: ⭐⭐⭐

**答案**:
Redis 使用链地址法解决哈希冲突：

**哈希表结构**:
```c
// 哈希表节点
typedef struct dictEntry {
    void *key;                  // 键
    union {
        void *val;
        uint64_t u64;
        int64_t s64;
        double d;
    } v;                       // 值
    struct dictEntry *next;    // 指向下一个节点
} dictEntry;

// 哈希表
typedef struct dictht {
    dictEntry **table;         // 哈希表数组
    unsigned long size;        // 哈希表大小
    unsigned long sizemask;    // 哈希表大小掩码
    unsigned long used;        // 已有节点数量
} dictht;
```

**渐进式 Rehash**:
- 使用两个哈希表 ht[0] 和 ht[1]
- 逐步将 ht[0] 的数据迁移到 ht[1]
- 避免一次性 rehash 造成的阻塞

```c
// 渐进式 rehash
int dictRehash(dict *d, int n) {
    int empty_visits = n * 10;
    
    if (!dictIsRehashing(d)) return 0;
    
    while(n-- && d->ht[0].used != 0) {
        dictEntry *de, *nextde;
        
        // 跳过空桶
        while(d->ht[0].table[d->rehashidx] == NULL) {
            d->rehashidx++;
            if (--empty_visits == 0) return 1;
        }
        
        // 迁移桶中的所有节点
        de = d->ht[0].table[d->rehashidx];
        while(de) {
            nextde = de->next;
            // 计算在新表中的位置
            unsigned int h = dictHashKey(d, de->key) & d->ht[1].sizemask;
            de->next = d->ht[1].table[h];
            d->ht[1].table[h] = de;
            d->ht[0].used--;
            d->ht[1].used++;
            de = nextde;
        }
        d->ht[0].table[d->rehashidx] = NULL;
        d->rehashidx++;
    }
    
    return 0;
}
```

---

## 第三部分：持久化机制面试题

### 3.1 美团面试题

#### Q9: RDB 和 AOF 的区别是什么？各有什么优缺点？
**来源**: 美团外卖后端开发
**难度**: ⭐⭐⭐

**答案**:

**RDB (Redis Database)**:
- **原理**: 将内存中的数据快照保存到磁盘
- **触发**: 手动 SAVE/BGSAVE 或自动触发
- **文件**: 二进制格式，文件小

**AOF (Append Only File)**:
- **原理**: 记录每个写操作命令
- **触发**: 每次写操作或定期同步
- **文件**: 文本格式，文件大

| **特性** | **RDB** | **AOF** |
|---------|---------|---------|
| **文件大小** | 小 | 大 |
| **恢复速度** | 快 | 慢 |
| **数据安全性** | 可能丢失数据 | 更安全 |
| **性能影响** | fork 时有影响 | 持续写入影响 |
| **压缩** | 自动压缩 | 需要重写 |

**RDB 配置示例**:
```conf
# 900秒内至少1个key发生变化则触发
save 900 1
save 300 10
save 60 10000

# RDB文件名
dbfilename dump.rdb

# 是否压缩
rdbcompression yes
```

**AOF 配置示例**:
```conf
# 启用AOF
appendonly yes

# AOF文件名
appendfilename "appendonly.aof"

# 同步策略
appendfsync everysec  # always/everysec/no
```

#### Q10: Redis 4.0 的混合持久化是什么？
**来源**: 美团点评基础架构
**难度**: ⭐⭐⭐⭐

**答案**:
混合持久化结合了 RDB 和 AOF 的优点：

**工作原理**:
1. AOF 重写时，将当前内存数据以 RDB 格式写入 AOF 文件开头
2. 后续的写操作以 AOF 格式追加到文件末尾
3. 恢复时先加载 RDB 部分，再重放 AOF 部分

**配置**:
```conf
# 启用混合持久化
aof-use-rdb-preamble yes
```

**优势**:
- 文件更小 (RDB 部分压缩)
- 恢复更快 (RDB 部分快速加载)
- 数据更安全 (AOF 部分保证实时性)

### 3.2 滴滴面试题

#### Q11: Redis 持久化过程中会阻塞主线程吗？
**来源**: 滴滴出行后端
**难度**: ⭐⭐⭐⭐

**答案**:

**RDB 持久化**:
- **SAVE 命令**: 会阻塞主线程
- **BGSAVE 命令**: 不会阻塞，使用 fork 子进程
- **fork 过程**: 会有短暂阻塞 (毫秒级)

**AOF 持久化**:
- **写入**: 不阻塞，写入缓冲区
- **同步**: 根据策略可能阻塞
- **重写**: 使用子进程，不阻塞

**fork 阻塞分析**:
```c
// Redis fork 过程
int rdbSaveBackground(char *filename, rdbSaveInfo *rsi) {
    pid_t childpid;

    if (hasActiveChildProcess()) return C_ERR;

    server.dirty_before_bgsave = server.dirty;
    server.lastbgsave_try = time(NULL);

    // fork 子进程 - 这里会短暂阻塞
    if ((childpid = redisFork()) == 0) {
        // 子进程执行 RDB 保存
        int retval = rdbSave(filename, rsi);
        exitFromChild((retval == C_OK) ? 0 : 1);
    } else {
        // 父进程继续处理请求
        server.rdb_save_time_start = time(NULL);
        server.rdb_child_pid = childpid;
        return C_OK;
    }
}
```

**优化建议**:
1. 使用 SSD 减少 fork 时间
2. 控制内存使用量
3. 避免在高峰期手动触发
4. 监控 fork 耗时

#### Q12: 大 key 对持久化有什么影响？
**来源**: 滴滴基础平台
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**对 RDB 的影响**:
1. **fork 时间增长**: 大 key 增加内存使用，fork 耗时更长
2. **写时复制开销**: 修改大 key 时复制整个内存页
3. **文件大小**: RDB 文件变大，传输和加载耗时

**对 AOF 的影响**:
1. **命令记录**: 大 key 的操作命令很长
2. **重写耗时**: AOF 重写时处理大 key 耗时更长
3. **磁盘 I/O**: 频繁操作大 key 增加磁盘压力

**检测大 key**:
```bash
# 使用 redis-cli 扫描大 key
redis-cli --bigkeys

# 使用 MEMORY USAGE 命令
redis-cli MEMORY USAGE mykey

# 自定义脚本检测
redis-cli --eval scan_bigkeys.lua
```

**解决方案**:
1. **拆分大 key**: 将大 key 拆分成多个小 key
2. **压缩数据**: 使用压缩算法减少内存占用
3. **异步删除**: 使用 UNLINK 而不是 DEL
4. **监控告警**: 设置大 key 监控和告警

---

## 第四部分：缓存问题面试题

### 4.1 京东面试题

#### Q13: 什么是缓存穿透？如何解决？
**来源**: 京东商城后端
**难度**: ⭐⭐⭐

**答案**:

**缓存穿透定义**:
查询一个不存在的数据，缓存和数据库都没有，导致每次请求都打到数据库。

**危害**:
- 数据库压力增大
- 可能被恶意攻击
- 系统性能下降

**解决方案**:

**1. 布隆过滤器**:
```python
import redis
from pybloom_live import BloomFilter

class BloomFilterCache:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.bloom_filter = BloomFilter(capacity=1000000, error_rate=0.001)

    def add_key(self, key):
        """添加 key 到布隆过滤器"""
        self.bloom_filter.add(key)

    def might_exist(self, key):
        """检查 key 是否可能存在"""
        return key in self.bloom_filter

    def get_data(self, key):
        # 先检查布隆过滤器
        if not self.might_exist(key):
            return None

        # 检查缓存
        cached_data = self.redis_client.get(key)
        if cached_data:
            return cached_data

        # 查询数据库
        data = self.query_database(key)
        if data:
            self.redis_client.setex(key, 3600, data)

        return data
```

**2. 缓存空值**:
```python
def get_data_with_null_cache(key):
    # 检查缓存
    cached_data = redis_client.get(key)
    if cached_data is not None:
        if cached_data == "NULL":
            return None
        return cached_data

    # 查询数据库
    data = query_database(key)
    if data:
        redis_client.setex(key, 3600, data)
    else:
        # 缓存空值，设置较短过期时间
        redis_client.setex(key, 300, "NULL")

    return data
```

#### Q14: 什么是缓存击穿？如何解决？
**来源**: 京东物流技术部
**难度**: ⭐⭐⭐⭐

**答案**:

**缓存击穿定义**:
热点 key 在某个时间点过期，此时有大量并发请求，都去查询数据库。

**解决方案**:

**1. 互斥锁**:
```python
import redis
import threading
import time

class MutexCache:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.local_lock = threading.Lock()

    def get_data_with_mutex(self, key):
        # 检查缓存
        data = self.redis_client.get(key)
        if data:
            return data

        # 尝试获取分布式锁
        lock_key = f"lock:{key}"
        lock_acquired = self.redis_client.set(
            lock_key, "1", nx=True, ex=10
        )

        if lock_acquired:
            try:
                # 再次检查缓存（双重检查）
                data = self.redis_client.get(key)
                if data:
                    return data

                # 查询数据库
                data = self.query_database(key)
                if data:
                    self.redis_client.setex(key, 3600, data)

                return data
            finally:
                # 释放锁
                self.redis_client.delete(lock_key)
        else:
            # 未获得锁，等待后重试
            time.sleep(0.1)
            return self.get_data_with_mutex(key)
```

**2. 热点数据永不过期**:
```python
def get_data_never_expire(key):
    data = redis_client.get(key)
    if data:
        data_obj = json.loads(data)

        # 检查逻辑过期时间
        if data_obj['expire_time'] > time.time():
            return data_obj['data']
        else:
            # 异步更新数据
            threading.Thread(
                target=async_update_data,
                args=(key,)
            ).start()

            # 返回过期数据
            return data_obj['data']

    # 缓存未命中，同步加载
    return load_data_sync(key)
```

#### Q15: 什么是缓存雪崩？如何解决？
**来源**: 京东云计算
**难度**: ⭐⭐⭐⭐

**答案**:

**缓存雪崩定义**:
大量缓存在同一时间过期，导致请求都打到数据库，造成数据库压力过大。

**解决方案**:

**1. 过期时间随机化**:
```python
import random

def set_cache_with_random_ttl(key, data, base_ttl=3600):
    # 在基础TTL上增加随机时间
    random_ttl = base_ttl + random.randint(0, 300)
    redis_client.setex(key, random_ttl, data)
```

**2. 多级缓存**:
```python
class MultiLevelCache:
    def __init__(self):
        self.l1_cache = {}  # 本地缓存
        self.l2_cache = redis.Redis()  # Redis 缓存

    def get_data(self, key):
        # L1 缓存
        if key in self.l1_cache:
            return self.l1_cache[key]

        # L2 缓存
        data = self.l2_cache.get(key)
        if data:
            self.l1_cache[key] = data
            return data

        # 数据库
        data = self.query_database(key)
        if data:
            self.set_cache(key, data)

        return data

    def set_cache(self, key, data):
        # 设置多级缓存
        self.l1_cache[key] = data
        self.l2_cache.setex(key, 3600, data)
```

**3. 熔断降级**:
```python
class CircuitBreaker:
    def __init__(self, failure_threshold=5, timeout=60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

    def call(self, func, *args, **kwargs):
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'HALF_OPEN'
            else:
                raise Exception("Circuit breaker is OPEN")

        try:
            result = func(*args, **kwargs)
            self.on_success()
            return result
        except Exception as e:
            self.on_failure()
            raise e

    def on_success(self):
        self.failure_count = 0
        self.state = 'CLOSED'

    def on_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'
```

---

## 第五部分：集群与高可用面试题

### 5.1 网易面试题

#### Q16: Redis 主从复制的原理是什么？
**来源**: 网易云音乐后端
**难度**: ⭐⭐⭐⭐

**答案**:

**主从复制过程**:
1. **建立连接**: 从节点向主节点发送 PSYNC 命令
2. **全量同步**: 主节点执行 BGSAVE，发送 RDB 文件
3. **增量同步**: 主节点将写命令发送给从节点
4. **命令传播**: 持续同步写操作

**复制流程图**:
```
主节点                     从节点
  |                        |
  |<------ PSYNC ----------|  1. 从节点请求同步
  |                        |
  |------- +FULLRESYNC --->|  2. 主节点响应全量同步
  |                        |
  |------- RDB 文件 ------>|  3. 发送数据快照
  |                        |
  |------- 写命令 -------->|  4. 增量同步
```

**核心代码分析**:
```c
// 主从复制核心函数 (replication.c)
void replicationFeedSlaves(list *slaves, int dictid, robj **argv, int argc) {
    listNode *ln;
    listIter li;
    int j;

    // 遍历所有从节点
    listRewind(slaves, &li);
    while((ln = listNext(&li))) {
        client *slave = ln->value;

        // 发送 SELECT 命令切换数据库
        if (slave->slaveseldb != dictid) {
            robj *selectcmd;
            selectcmd = createObject(OBJ_STRING,
                sdscatprintf(sdsempty(),"*2\r\n$6\r\nSELECT\r\n$%lu\r\n%s\r\n",
                (unsigned long)sdslen(dictid_str), dictid_str));
            addReply(slave, selectcmd);
            decrRefCount(selectcmd);
            slave->slaveseldb = dictid;
        }

        // 发送写命令
        addReplyArrayLen(slave, argc);
        for (j = 0; j < argc; j++)
            addReplyBulk(slave, argv[j]);
    }
}
```

**PSYNC 命令详解**:
```bash
# 全量同步
PSYNC ? -1

# 增量同步 (带 runid 和 offset)
PSYNC <runid> <offset>
```

#### Q17: Redis 哨兵模式是如何工作的？
**来源**: 网易有道技术部
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**哨兵模式架构**:
- **监控**: 检查主从节点是否正常工作
- **通知**: 通过 API 通知管理员故障信息
- **故障转移**: 自动进行主从切换
- **配置提供**: 为客户端提供主节点信息

**故障检测机制**:
```c
// 哨兵检测节点状态
void sentinelCheckSubjectivelyDown(sentinelRedisInstance *ri) {
    mstime_t elapsed = 0;

    if (ri->last_ping_time)
        elapsed = mstime() - ri->last_ping_time;

    // 超过 down-after-milliseconds 认为主观下线
    if (elapsed > ri->down_after_period) {
        if (!(ri->flags & SRI_S_DOWN)) {
            sentinelEvent(LL_WARNING, "+sdown", ri, "%@");
            ri->s_down_since_time = mstime();
            ri->flags |= SRI_S_DOWN;
        }
    }
}
```

**选举新主节点算法**:
1. **过滤**: 排除下线、断线、优先级为0的从节点
2. **优先级**: 选择 slave-priority 最小的
3. **复制偏移量**: 选择复制偏移量最大的
4. **运行ID**: 选择运行ID最小的

**配置示例**:
```conf
# sentinel.conf
port 26379
sentinel monitor mymaster 127.0.0.1 6379 2
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 10000
sentinel parallel-syncs mymaster 1
```

### 5.2 小米面试题

#### Q18: Redis Cluster 的分片原理是什么？
**来源**: 小米云平台
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**哈希槽 (Hash Slot)**:
- Redis Cluster 使用 16384 个哈希槽
- 每个 key 通过 CRC16 算法映射到槽
- 每个节点负责一部分槽

**槽分配算法**:
```c
// 计算 key 的槽位
unsigned int keyHashSlot(char *key, int keylen) {
    int s, e;

    // 查找 {} 标签
    for (s = 0; s < keylen; s++)
        if (key[s] == '{') break;

    if (s == keylen) return crc16(key, keylen) & 0x3FFF;

    for (e = s+1; e < keylen; e++)
        if (key[e] == '}') break;

    if (e == keylen || e == s+1) return crc16(key, keylen) & 0x3FFF;

    // 使用 {} 内的内容计算槽位
    return crc16(key+s+1, e-s-1) & 0x3FFF;
}
```

**节点通信 (Gossip 协议)**:
```c
// 集群节点信息
typedef struct clusterNode {
    mstime_t ctime;             // 创建时间
    char name[CLUSTER_NAMELEN]; // 节点名称
    int flags;                  // 节点标志
    uint64_t configEpoch;       // 配置纪元
    unsigned char slots[CLUSTER_SLOTS/8]; // 负责的槽位图
    int numslots;               // 槽位数量
    int numslaves;              // 从节点数量
    struct clusterNode **slaves; // 从节点数组
    struct clusterNode *slaveof; // 主节点
    mstime_t ping_sent;         // 最后发送 PING 时间
    mstime_t pong_received;     // 最后收到 PONG 时间
    char ip[NET_IP_STR_LEN];    // IP 地址
    int port;                   // 端口
    clusterLink *link;          // 连接
} clusterNode;
```

**故障转移流程**:
1. **故障检测**: 节点互相监控，发现故障
2. **故障确认**: 大多数主节点确认故障
3. **从节点选举**: 故障主节点的从节点发起选举
4. **接管槽位**: 新主节点接管原主节点的槽位

#### Q19: Redis Cluster 如何处理数据迁移？
**来源**: 小米广告平台
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**在线重分片 (Resharding)**:
Redis Cluster 支持在不停机的情况下重新分配槽位。

**迁移过程**:
1. **设置迁移状态**: 源节点标记槽为 MIGRATING
2. **设置导入状态**: 目标节点标记槽为 IMPORTING
3. **迁移数据**: 逐个迁移槽中的 key
4. **更新配置**: 通知所有节点槽位变更

**迁移命令**:
```bash
# 1. 设置槽位迁移状态
CLUSTER SETSLOT 1234 MIGRATING target-node-id

# 2. 设置槽位导入状态
CLUSTER SETSLOT 1234 IMPORTING source-node-id

# 3. 获取槽中的 key
CLUSTER GETKEYSINSLOT 1234 100

# 4. 迁移具体的 key
MIGRATE target-host target-port key 0 timeout

# 5. 完成迁移
CLUSTER SETSLOT 1234 NODE target-node-id
```

**ASK 重定向**:
```c
// 处理 ASK 重定向
if (server.cluster_enabled &&
    (error_code == CLUSTER_REDIR_ASK ||
     error_code == CLUSTER_REDIR_MOVED)) {

    if (error_code == CLUSTER_REDIR_ASK) {
        // ASK 重定向：槽正在迁移
        addReplySds(c, sdscatprintf(sdsempty(),
            "-ASK %d %s:%d\r\n", slot, node->ip, node->port));
    } else {
        // MOVED 重定向：槽已经迁移完成
        addReplySds(c, sdscatprintf(sdsempty(),
            "-MOVED %d %s:%d\r\n", slot, node->ip, node->port));
    }
}
```

**客户端处理**:
```python
class RedisClusterClient:
    def handle_redirect(self, key, command):
        try:
            return self.execute_command(key, command)
        except MovedError as e:
            # 更新槽位映射
            self.update_slot_mapping(e.slot, e.node)
            return self.execute_command(key, command)
        except AskError as e:
            # 临时重定向到目标节点
            return self.execute_on_node(e.node, "ASKING", command)
```

---

## 第六部分：性能优化面试题

### 6.1 快手面试题

#### Q20: Redis 的 Pipeline 是什么？有什么优势？
**来源**: 快手短视频后端
**难度**: ⭐⭐⭐

**答案**:

**Pipeline 原理**:
将多个命令打包发送，减少网络往返时间 (RTT)。

**性能对比**:
```python
import redis
import time

def without_pipeline():
    r = redis.Redis()
    start = time.time()

    for i in range(1000):
        r.set(f"key:{i}", f"value:{i}")

    end = time.time()
    print(f"Without pipeline: {end - start:.2f}s")

def with_pipeline():
    r = redis.Redis()
    start = time.time()

    pipe = r.pipeline()
    for i in range(1000):
        pipe.set(f"key:{i}", f"value:{i}")
    pipe.execute()

    end = time.time()
    print(f"With pipeline: {end - start:.2f}s")

# 结果对比
# Without pipeline: 2.34s
# With pipeline: 0.12s
```

**Pipeline 实现原理**:
```c
// Redis 客户端缓冲区
typedef struct client {
    int fd;                 // 套接字描述符
    sds querybuf;          // 查询缓冲区
    int argc;              // 参数数量
    robj **argv;           // 参数数组
    list *reply;           // 回复列表
    unsigned long long reply_bytes; // 回复字节数
} client;

// 处理管道命令
void processInputBuffer(client *c) {
    while(sdslen(c->querybuf)) {
        // 解析命令
        if (processInlineBuffer(c) != C_OK) break;

        // 执行命令
        if (processCommand(c) == C_OK) {
            // 重置客户端状态，准备处理下一个命令
            resetClient(c);
        }
    }
}
```

**使用注意事项**:
1. **内存使用**: Pipeline 会占用更多内存
2. **原子性**: Pipeline 不保证原子性
3. **错误处理**: 需要检查每个命令的执行结果
4. **批次大小**: 建议每批 100-1000 个命令

#### Q21: Redis 的 Lua 脚本有什么优势？
**来源**: 快手电商技术部
**难度**: ⭐⭐⭐⭐

**答案**:

**Lua 脚本优势**:
1. **原子性**: 脚本执行过程中不会被其他命令打断
2. **减少网络开销**: 多个操作在服务端执行
3. **复杂逻辑**: 支持条件判断、循环等
4. **一致性**: 相同输入保证相同输出

**分布式锁实现**:
```lua
-- 获取锁的 Lua 脚本
local key = KEYS[1]
local value = ARGV[1]
local ttl = ARGV[2]

-- 检查锁是否存在
if redis.call('EXISTS', key) == 0 then
    -- 设置锁
    redis.call('SET', key, value, 'EX', ttl)
    return 1
else
    -- 检查是否是同一个客户端
    if redis.call('GET', key) == value then
        -- 续期
        redis.call('EXPIRE', key, ttl)
        return 1
    else
        return 0
    end
end
```

**释放锁脚本**:
```lua
-- 释放锁的 Lua 脚本
local key = KEYS[1]
local value = ARGV[1]

-- 检查锁的持有者
if redis.call('GET', key) == value then
    return redis.call('DEL', key)
else
    return 0
end
```

**限流器实现**:
```lua
-- 滑动窗口限流
local key = KEYS[1]
local window = tonumber(ARGV[1])  -- 时间窗口
local limit = tonumber(ARGV[2])   -- 限制次数
local current_time = tonumber(ARGV[3])

-- 清理过期数据
redis.call('ZREMRANGEBYSCORE', key, 0, current_time - window)

-- 获取当前计数
local current_count = redis.call('ZCARD', key)

if current_count < limit then
    -- 添加当前请求
    redis.call('ZADD', key, current_time, current_time)
    redis.call('EXPIRE', key, window)
    return 1
else
    return 0
end
```

**Python 调用示例**:
```python
import redis

class RedisLuaScripts:
    def __init__(self):
        self.redis_client = redis.Redis()

        # 预加载脚本
        self.lock_script = self.redis_client.register_script("""
            local key = KEYS[1]
            local value = ARGV[1]
            local ttl = ARGV[2]

            if redis.call('EXISTS', key) == 0 then
                redis.call('SET', key, value, 'EX', ttl)
                return 1
            else
                if redis.call('GET', key) == value then
                    redis.call('EXPIRE', key, ttl)
                    return 1
                else
                    return 0
                end
            end
        """)

    def acquire_lock(self, lock_key, client_id, ttl=30):
        return self.lock_script(keys=[lock_key], args=[client_id, ttl])
```

---

## 第七部分：分布式锁面试题

### 7.1 京东面试题

#### Q22: Redis 分布式锁的实现原理？
**来源**: 京东商城后端开发
**难度**: ⭐⭐⭐⭐

**答案**:

**基础实现**:
```python
import redis
import uuid
import time

class RedisDistributedLock:
    def __init__(self, redis_client, lock_key, timeout=30):
        self.redis = redis_client
        self.lock_key = lock_key
        self.timeout = timeout
        self.identifier = str(uuid.uuid4())

    def acquire(self):
        """获取锁"""
        # 使用 SET NX EX 原子操作
        result = self.redis.set(
            self.lock_key,
            self.identifier,
            nx=True,  # 只在键不存在时设置
            ex=self.timeout  # 设置过期时间
        )
        return result is not None

    def release(self):
        """释放锁 - 使用 Lua 脚本保证原子性"""
        lua_script = """
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('DEL', KEYS[1])
        else
            return 0
        end
        """
        return self.redis.eval(lua_script, 1, self.lock_key, self.identifier)
```

**可重入锁实现**:
```python
class ReentrantRedisLock:
    def __init__(self, redis_client, lock_key, timeout=30):
        self.redis = redis_client
        self.lock_key = lock_key
        self.timeout = timeout
        self.identifier = f"{threading.current_thread().ident}:{uuid.uuid4()}"

    def acquire(self):
        """可重入锁获取"""
        lua_script = """
        local key = KEYS[1]
        local identifier = ARGV[1]
        local timeout = ARGV[2]

        local current = redis.call('HGET', key, 'owner')
        if current == false then
            -- 锁不存在，直接获取
            redis.call('HSET', key, 'owner', identifier)
            redis.call('HSET', key, 'count', 1)
            redis.call('EXPIRE', key, timeout)
            return 1
        elseif current == identifier then
            -- 同一线程重入
            local count = redis.call('HGET', key, 'count')
            redis.call('HSET', key, 'count', count + 1)
            redis.call('EXPIRE', key, timeout)
            return 1
        else
            -- 其他线程持有锁
            return 0
        end
        """
        return self.redis.eval(lua_script, 1, self.lock_key, self.identifier, self.timeout)

    def release(self):
        """可重入锁释放"""
        lua_script = """
        local key = KEYS[1]
        local identifier = ARGV[1]

        local current = redis.call('HGET', key, 'owner')
        if current == identifier then
            local count = redis.call('HGET', key, 'count')
            if tonumber(count) > 1 then
                redis.call('HSET', key, 'count', count - 1)
                return 1
            else
                redis.call('DEL', key)
                return 1
            end
        else
            return 0
        end
        """
        return self.redis.eval(lua_script, 1, self.lock_key, self.identifier)
```

#### Q23: RedLock 红锁算法是什么？
**来源**: 京东物流技术部
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**RedLock 算法原理**:
RedLock 是 Redis 作者提出的分布式锁算法，解决单点故障问题。

**算法步骤**:
1. 获取当前时间戳
2. 依次向 N 个 Redis 实例请求锁
3. 计算获取锁的总耗时
4. 如果获得超过半数锁且耗时小于锁有效期，则成功
5. 否则释放所有已获得的锁

**RedLock 实现**:
```python
import time
import threading
from typing import List

class RedLock:
    def __init__(self, redis_instances: List, lock_key: str, ttl: int = 30000):
        self.redis_instances = redis_instances
        self.lock_key = lock_key
        self.ttl = ttl  # 毫秒
        self.identifier = f"{threading.current_thread().ident}:{uuid.uuid4()}"
        self.drift_factor = 0.01  # 时钟漂移因子
        self.retry_delay = 200  # 重试延迟(毫秒)
        self.retry_count = 3

    def acquire(self):
        """获取 RedLock"""
        for _ in range(self.retry_count):
            start_time = int(time.time() * 1000)
            locked_instances = 0

            # 尝试从所有实例获取锁
            for redis_instance in self.redis_instances:
                if self._acquire_single_lock(redis_instance):
                    locked_instances += 1

            # 计算获取锁的耗时
            elapsed_time = int(time.time() * 1000) - start_time

            # 计算有效时间（考虑时钟漂移）
            validity_time = self.ttl - elapsed_time - int(self.ttl * self.drift_factor)

            # 检查是否获得大多数锁且在有效时间内
            if locked_instances >= (len(self.redis_instances) // 2 + 1) and validity_time > 0:
                return True
            else:
                # 释放已获得的锁
                self._release_all_locks()

            # 等待后重试
            time.sleep(self.retry_delay / 1000.0)

        return False

    def _acquire_single_lock(self, redis_instance):
        """从单个 Redis 实例获取锁"""
        try:
            result = redis_instance.set(
                self.lock_key,
                self.identifier,
                px=self.ttl,  # 毫秒级过期时间
                nx=True
            )
            return result is not None
        except Exception:
            return False

    def release(self):
        """释放 RedLock"""
        self._release_all_locks()

    def _release_all_locks(self):
        """释放所有实例上的锁"""
        lua_script = """
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('DEL', KEYS[1])
        else
            return 0
        end
        """

        for redis_instance in self.redis_instances:
            try:
                redis_instance.eval(lua_script, 1, self.lock_key, self.identifier)
            except Exception:
                pass  # 忽略释放失败
```

**RedLock 争议**:
- **支持者**: 解决了单点故障问题
- **反对者**: 复杂度高，时钟同步要求严格
- **替代方案**: 使用 ZooKeeper 或 etcd

### 7.2 美团面试题

#### Q24: Redis 事务是如何实现的？
**来源**: 美团外卖后端
**难度**: ⭐⭐⭐

**答案**:

**Redis 事务特点**:
- **原子性**: 事务中的命令要么全部执行，要么全部不执行
- **隔离性**: 事务执行期间不会被其他命令打断
- **无持久性**: Redis 事务不保证持久性
- **无一致性**: 不支持回滚

**事务命令**:
```bash
# 开始事务
MULTI

# 添加命令到事务队列
SET key1 value1
SET key2 value2
INCR counter

# 执行事务
EXEC

# 取消事务
DISCARD
```

**WATCH 实现乐观锁**:
```python
def transfer_money(from_account, to_account, amount):
    """使用 WATCH 实现转账的乐观锁"""
    with redis_client.pipeline() as pipe:
        while True:
            try:
                # 监控账户余额
                pipe.watch(from_account, to_account)

                # 获取当前余额
                from_balance = float(pipe.get(from_account) or 0)
                to_balance = float(pipe.get(to_account) or 0)

                # 检查余额是否足够
                if from_balance < amount:
                    pipe.unwatch()
                    raise ValueError("Insufficient balance")

                # 开始事务
                pipe.multi()
                pipe.set(from_account, from_balance - amount)
                pipe.set(to_account, to_balance + amount)

                # 执行事务
                pipe.execute()
                break

            except redis.WatchError:
                # 数据被修改，重试
                continue
```

**事务实现原理**:
```c
// Redis 事务状态
typedef struct multiState {
    multiCmd *commands;     // 事务命令数组
    int count;             // 命令数量
    int cmd_flags;         // 命令标志
    int minreplicas;       // 最小副本数
    time_t minreplicas_timeout; // 副本超时时间
} multiState;

// 执行事务
void execCommand(client *c) {
    int j;
    robj **orig_argv;
    int orig_argc;
    struct redisCommand *orig_cmd;

    // 检查事务状态
    if (!(c->flags & CLIENT_MULTI)) {
        addReplyError(c, "EXEC without MULTI");
        return;
    }

    // 检查是否有被 WATCH 的键被修改
    if (c->flags & (CLIENT_DIRTY_CAS|CLIENT_DIRTY_EXEC)) {
        addReply(c, shared.nullarray);
        discardTransaction(c);
        return;
    }

    // 执行事务中的所有命令
    unwatchAllKeys(c);
    orig_argv = c->argv;
    orig_argc = c->argc;
    orig_cmd = c->cmd;
    addReplyArrayLen(c, c->mstate.count);

    for (j = 0; j < c->mstate.count; j++) {
        c->argc = c->mstate.commands[j].argc;
        c->argv = c->mstate.commands[j].argv;
        c->cmd = c->mstate.commands[j].cmd;

        // 执行命令
        call(c, CMD_CALL_FULL);
    }

    // 恢复客户端状态
    c->argv = orig_argv;
    c->argc = orig_argc;
    c->cmd = orig_cmd;
    discardTransaction(c);
}
```

---

## 第八部分：实际应用场景面试题

### 8.1 字节跳动面试题

#### Q25: 如何使用 Redis 实现消息队列？
**来源**: 字节跳动抖音后端
**难度**: ⭐⭐⭐⭐

**答案**:

**方案1: List 实现**
```python
class RedisListQueue:
    def __init__(self, redis_client, queue_name):
        self.redis = redis_client
        self.queue_name = queue_name

    def enqueue(self, message):
        """入队"""
        return self.redis.lpush(self.queue_name, message)

    def dequeue(self, timeout=0):
        """出队 - 阻塞式"""
        result = self.redis.brpop(self.queue_name, timeout)
        return result[1] if result else None

    def size(self):
        """队列长度"""
        return self.redis.llen(self.queue_name)
```

**方案2: Pub/Sub 实现**
```python
class RedisPubSubQueue:
    def __init__(self, redis_client, channel_name):
        self.redis = redis_client
        self.channel_name = channel_name
        self.pubsub = self.redis.pubsub()

    def publish(self, message):
        """发布消息"""
        return self.redis.publish(self.channel_name, message)

    def subscribe(self, callback):
        """订阅消息"""
        self.pubsub.subscribe(self.channel_name)

        for message in self.pubsub.listen():
            if message['type'] == 'message':
                callback(message['data'])
```

**方案3: Stream 实现 (推荐)**
```python
class RedisStreamQueue:
    def __init__(self, redis_client, stream_name):
        self.redis = redis_client
        self.stream_name = stream_name

    def add_message(self, fields):
        """添加消息"""
        return self.redis.xadd(self.stream_name, fields)

    def read_messages(self, consumer_group, consumer_name, count=1):
        """读取消息"""
        try:
            # 尝试创建消费者组
            self.redis.xgroup_create(self.stream_name, consumer_group, id='0', mkstream=True)
        except redis.ResponseError:
            pass  # 组已存在

        # 读取消息
        messages = self.redis.xreadgroup(
            consumer_group,
            consumer_name,
            {self.stream_name: '>'},
            count=count,
            block=1000
        )

        return messages

    def ack_message(self, consumer_group, message_id):
        """确认消息"""
        return self.redis.xack(self.stream_name, consumer_group, message_id)
```

**三种方案对比**:
| **特性** | **List** | **Pub/Sub** | **Stream** |
|---------|----------|-------------|------------|
| **持久化** | ✅ | ❌ | ✅ |
| **消费确认** | ❌ | ❌ | ✅ |
| **消费者组** | ❌ | ❌ | ✅ |
| **消息回溯** | ❌ | ❌ | ✅ |
| **广播** | ❌ | ✅ | ✅ |
| **复杂度** | 低 | 低 | 中等 |

#### Q26: 如何解决 Redis 热 Key 问题？
**来源**: 字节跳动基础架构
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**热 Key 定义**:
某个 key 的访问频率远高于其他 key，导致请求集中在单个 Redis 节点。

**热 Key 危害**:
1. **性能瓶颈**: 单节点 CPU 使用率过高
2. **网络拥塞**: 网络带宽被占满
3. **服务不稳定**: 可能导致节点宕机

**检测热 Key**:
```python
class HotKeyDetector:
    def __init__(self, redis_client, threshold=1000):
        self.redis = redis_client
        self.threshold = threshold
        self.key_counter = {}
        self.time_window = 60  # 60秒时间窗口

    def record_access(self, key):
        """记录 key 访问"""
        current_time = int(time.time())
        time_slot = current_time // self.time_window

        counter_key = f"{key}:{time_slot}"
        count = self.key_counter.get(counter_key, 0) + 1
        self.key_counter[counter_key] = count

        # 检查是否为热 key
        if count > self.threshold:
            self.handle_hot_key(key)

    def handle_hot_key(self, key):
        """处理热 key"""
        print(f"Hot key detected: {key}")
        # 触发热 key 处理逻辑
```

**解决方案**:

**1. 客户端缓存**:
```python
class ClientSideCache:
    def __init__(self, redis_client, local_cache_size=1000):
        self.redis = redis_client
        self.local_cache = {}
        self.cache_size = local_cache_size
        self.access_count = {}

    def get(self, key):
        # 检查本地缓存
        if key in self.local_cache:
            self.access_count[key] = self.access_count.get(key, 0) + 1
            return self.local_cache[key]

        # 从 Redis 获取
        value = self.redis.get(key)

        # 缓存热点数据
        if self._is_hot_key(key):
            self._add_to_local_cache(key, value)

        return value

    def _is_hot_key(self, key):
        """判断是否为热 key"""
        return self.access_count.get(key, 0) > 100

    def _add_to_local_cache(self, key, value):
        """添加到本地缓存"""
        if len(self.local_cache) >= self.cache_size:
            # LRU 淘汰
            oldest_key = min(self.local_cache.keys(),
                           key=lambda k: self.access_count.get(k, 0))
            del self.local_cache[oldest_key]

        self.local_cache[key] = value
```

**2. 热 Key 分散**:
```python
def scatter_hot_key(redis_client, hot_key, scatter_count=10):
    """将热 key 分散到多个副本"""
    import random

    # 读取时随机选择副本
    def get_scattered_key():
        replica_index = random.randint(0, scatter_count - 1)
        replica_key = f"{hot_key}:replica:{replica_index}"

        value = redis_client.get(replica_key)
        if value is None:
            # 副本不存在，从主 key 复制
            main_value = redis_client.get(hot_key)
            if main_value:
                redis_client.setex(replica_key, 300, main_value)
                return main_value
        return value

    # 写入时更新所有副本
    def set_scattered_key(value):
        # 更新主 key
        redis_client.set(hot_key, value)

        # 更新所有副本
        for i in range(scatter_count):
            replica_key = f"{hot_key}:replica:{i}"
            redis_client.setex(replica_key, 300, value)

    return get_scattered_key, set_scattered_key
```

**3. 多级缓存**:
```python
class MultiLevelCache:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.l1_cache = {}  # 进程内缓存
        self.l2_cache = redis_client  # Redis 缓存

    def get(self, key):
        # L1 缓存
        if key in self.l1_cache:
            return self.l1_cache[key]

        # L2 缓存
        value = self.l2_cache.get(key)
        if value:
            # 热点数据放入 L1
            if self._is_hot_key(key):
                self.l1_cache[key] = value
            return value

        return None

    def set(self, key, value):
        # 同时更新两级缓存
        self.l1_cache[key] = value
        self.l2_cache.set(key, value)
```

### 8.2 阿里巴巴面试题

#### Q27: Redis 大 Key 问题如何解决？
**来源**: 阿里巴巴淘宝技术部
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**大 Key 定义**:
- String 类型：值大于 10KB
- Hash、List、Set、ZSet：元素个数超过 5000

**大 Key 危害**:
1. **内存不均**: 导致内存分布不均衡
2. **超时阻塞**: 操作大 Key 耗时长，阻塞其他操作
3. **网络拥塞**: 大 Key 传输占用大量带宽
4. **主从同步**: 影响主从同步效率

**检测大 Key**:
```bash
# 使用 redis-cli 扫描
redis-cli --bigkeys

# 使用 MEMORY USAGE 命令
redis-cli MEMORY USAGE mykey

# 自定义扫描脚本
redis-cli --eval scan_bigkeys.lua 0 , 1024000
```

**解决方案**:

**1. 大 String 拆分**:
```python
def split_large_string(redis_client, key, value, chunk_size=1024):
    """将大字符串拆分成多个小块"""
    chunks = [value[i:i+chunk_size] for i in range(0, len(value), chunk_size)]

    # 存储块数量
    redis_client.set(f"{key}:count", len(chunks))

    # 存储每个块
    for i, chunk in enumerate(chunks):
        redis_client.set(f"{key}:chunk:{i}", chunk)

    # 删除原始大 key
    redis_client.delete(key)

def get_large_string(redis_client, key):
    """重组大字符串"""
    count = int(redis_client.get(f"{key}:count") or 0)
    if count == 0:
        return None

    chunks = []
    for i in range(count):
        chunk = redis_client.get(f"{key}:chunk:{i}")
        if chunk:
            chunks.append(chunk.decode())

    return ''.join(chunks)
```

**2. 异步删除大 Key**:
```python
def safe_delete_large_key(redis_client, key):
    """安全删除大 Key"""
    key_type = redis_client.type(key).decode()

    if key_type == 'string':
        # String 类型直接删除
        return redis_client.delete(key)

    elif key_type in ['hash', 'set', 'zset']:
        # 分批删除元素
        while True:
            if key_type == 'hash':
                fields = redis_client.hscan(key, count=100)[1]
                if not fields:
                    break
                redis_client.hdel(key, *fields.keys())

            elif key_type == 'set':
                members = redis_client.sscan(key, count=100)[1]
                if not members:
                    break
                redis_client.srem(key, *members)

            elif key_type == 'zset':
                members = redis_client.zscan(key, count=100)[1]
                if not members:
                    break
                redis_client.zrem(key, *members.keys())

        # 删除空 key
        return redis_client.delete(key)

    elif key_type == 'list':
        # List 使用 LTRIM 逐步删除
        while redis_client.llen(key) > 0:
            redis_client.ltrim(key, 100, -1)

        return redis_client.delete(key)
```

#### Q28: Redis 慢查询如何分析和优化？
**来源**: 阿里巴巴云计算
**难度**: ⭐⭐⭐⭐

**答案**:

**慢查询配置**:
```conf
# 慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 慢查询日志长度
slowlog-max-len 128
```

**慢查询分析**:
```bash
# 查看慢查询日志
SLOWLOG GET 10

# 查看慢查询统计
SLOWLOG LEN

# 清空慢查询日志
SLOWLOG RESET
```

**常见慢查询优化**:

**1. KEYS 命令优化**:
```python
# 错误用法
def bad_scan_keys(redis_client, pattern):
    return redis_client.keys(pattern)  # 阻塞式，性能差

# 正确用法
def good_scan_keys(redis_client, pattern):
    keys = []
    cursor = 0
    while True:
        cursor, partial_keys = redis_client.scan(
            cursor=cursor,
            match=pattern,
            count=100
        )
        keys.extend(partial_keys)
        if cursor == 0:
            break
    return keys
```

**2. 大集合操作优化**:
```python
# 分页获取 Hash 所有字段
def get_hash_all_paginated(redis_client, key, page_size=100):
    result = {}
    cursor = 0

    while True:
        cursor, fields = redis_client.hscan(key, cursor, count=page_size)
        result.update(fields)
        if cursor == 0:
            break

    return result
```

---

#### Q29: Redis 监控需要关注哪些指标？
**来源**: 阿里巴巴基础架构
**难度**: ⭐⭐⭐⭐

**答案**:

**核心监控指标**:

**1. 性能指标**:
```bash
# QPS (每秒查询数)
INFO stats | grep instantaneous_ops_per_sec

# 延迟指标
redis-cli --latency-history

# 慢查询
SLOWLOG GET 10
```

**2. 内存指标**:
```bash
# 内存使用情况
INFO memory

# 关键指标
used_memory_human          # 已使用内存
used_memory_peak_human     # 内存使用峰值
mem_fragmentation_ratio    # 内存碎片率
```

**3. 连接指标**:
```bash
# 连接数
INFO clients

# 关键指标
connected_clients          # 当前连接数
client_recent_max_input_buffer  # 客户端输入缓冲区
client_recent_max_output_buffer # 客户端输出缓冲区
```

**监控脚本示例**:
```python
import redis
import time
import json

class RedisMonitor:
    def __init__(self, redis_client):
        self.redis = redis_client

    def get_performance_metrics(self):
        """获取性能指标"""
        info = self.redis.info()

        metrics = {
            'qps': info.get('instantaneous_ops_per_sec', 0),
            'connected_clients': info.get('connected_clients', 0),
            'used_memory': info.get('used_memory', 0),
            'used_memory_peak': info.get('used_memory_peak', 0),
            'mem_fragmentation_ratio': info.get('mem_fragmentation_ratio', 0),
            'keyspace_hits': info.get('keyspace_hits', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
            'expired_keys': info.get('expired_keys', 0),
            'evicted_keys': info.get('evicted_keys', 0)
        }

        # 计算命中率
        total_requests = metrics['keyspace_hits'] + metrics['keyspace_misses']
        if total_requests > 0:
            metrics['hit_rate'] = metrics['keyspace_hits'] / total_requests
        else:
            metrics['hit_rate'] = 0

        return metrics

    def check_alerts(self, metrics):
        """检查告警条件"""
        alerts = []

        # 内存使用率告警
        if metrics['mem_fragmentation_ratio'] > 1.5:
            alerts.append(f"内存碎片率过高: {metrics['mem_fragmentation_ratio']}")

        # 命中率告警
        if metrics['hit_rate'] < 0.8:
            alerts.append(f"缓存命中率过低: {metrics['hit_rate']:.2%}")

        # 连接数告警
        if metrics['connected_clients'] > 1000:
            alerts.append(f"连接数过多: {metrics['connected_clients']}")

        return alerts
```

#### Q30: Redis 内存优化有哪些方法？
**来源**: 阿里巴巴云计算
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**内存优化策略**:

**1. 数据结构优化**:
```python
# 使用合适的数据结构
def optimize_data_structure():
    # 小 Hash 使用 ziplist 编码
    redis.config_set('hash-max-ziplist-entries', 512)
    redis.config_set('hash-max-ziplist-value', 64)

    # 小 List 使用 ziplist 编码
    redis.config_set('list-max-ziplist-size', -2)

    # 小 Set 使用 intset 编码
    redis.config_set('set-max-intset-entries', 512)

    # 小 ZSet 使用 ziplist 编码
    redis.config_set('zset-max-ziplist-entries', 128)
    redis.config_set('zset-max-ziplist-value', 64)
```

**2. 键名优化**:
```python
# 错误的键名设计
bad_keys = [
    'user:profile:12345:name',
    'user:profile:12345:email',
    'user:profile:12345:phone'
]

# 优化后的设计
def optimize_key_design():
    # 使用 Hash 存储用户信息
    user_data = {
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '************'
    }
    redis.hmset('u:12345', user_data)
```

**3. 过期时间优化**:
```python
def set_smart_expiration():
    # 避免同时过期造成缓存雪崩
    import random

    base_ttl = 3600  # 1小时
    random_ttl = base_ttl + random.randint(0, 300)  # 增加随机时间

    redis.setex('key', random_ttl, 'value')
```

**4. 内存碎片优化**:
```bash
# 检查内存碎片
INFO memory | grep mem_fragmentation_ratio

# 主动整理内存碎片 (Redis 4.0+)
MEMORY PURGE

# 配置自动整理
config set activedefrag yes
config set active-defrag-ignore-bytes 100mb
config set active-defrag-threshold-lower 10
```

---

## 第九部分：美国大厂面试题

### 9.1 Google 面试题

#### Q31: 设计一个分布式缓存系统 (Google L4/L5)
**来源**: Google 系统设计面试
**难度**: ⭐⭐⭐⭐⭐

**问题**: 设计一个类似 Google 内部使用的分布式缓存系统，需要支持数百万 QPS 和 PB 级数据。

**答案**:

**系统架构设计**:
```python
class DistributedCacheSystem:
    """Google 风格的分布式缓存系统"""

    def __init__(self):
        self.consistent_hash = ConsistentHashRing()
        self.cache_nodes = []
        self.replication_factor = 3
        self.monitoring = MetricsCollector()

    def put(self, key: str, value: bytes, ttl: int = 3600):
        """写入数据到分布式缓存"""
        # 1. 计算哈希值
        hash_value = self._hash(key)

        # 2. 找到负责的节点
        primary_nodes = self.consistent_hash.get_nodes(hash_value, self.replication_factor)

        # 3. 并行写入多个副本
        write_futures = []
        for node in primary_nodes:
            future = self._async_write(node, key, value, ttl)
            write_futures.append(future)

        # 4. 等待大多数写入成功
        success_count = 0
        for future in write_futures:
            try:
                if future.result(timeout=0.1):  # 100ms 超时
                    success_count += 1
            except TimeoutError:
                self.monitoring.record_timeout('write', node.id)

        # 5. 判断写入是否成功
        if success_count >= (self.replication_factor // 2 + 1):
            self.monitoring.record_success('write')
            return True
        else:
            self.monitoring.record_failure('write')
            return False

    def get(self, key: str) -> bytes:
        """从分布式缓存读取数据"""
        hash_value = self._hash(key)
        nodes = self.consistent_hash.get_nodes(hash_value, self.replication_factor)

        # 并行读取，返回第一个成功的结果
        read_futures = []
        for node in nodes:
            future = self._async_read(node, key)
            read_futures.append(future)

        for future in read_futures:
            try:
                result = future.result(timeout=0.05)  # 50ms 超时
                if result is not None:
                    self.monitoring.record_hit()
                    return result
            except TimeoutError:
                continue

        self.monitoring.record_miss()
        return None

    def _hash(self, key: str) -> int:
        """一致性哈希函数"""
        import hashlib
        return int(hashlib.md5(key.encode()).hexdigest(), 16)

    def handle_node_failure(self, failed_node):
        """处理节点故障"""
        # 1. 从哈希环中移除故障节点
        self.consistent_hash.remove_node(failed_node)

        # 2. 重新分配数据
        self._redistribute_data(failed_node)

        # 3. 更新路由表
        self._update_routing_table()

    def add_node(self, new_node):
        """添加新节点"""
        # 1. 添加到哈希环
        self.consistent_hash.add_node(new_node)

        # 2. 数据迁移
        self._migrate_data_to_new_node(new_node)
```

**一致性哈希实现**:
```python
class ConsistentHashRing:
    def __init__(self, virtual_nodes=150):
        self.virtual_nodes = virtual_nodes
        self.ring = {}
        self.sorted_keys = []

    def add_node(self, node):
        """添加节点到哈希环"""
        for i in range(self.virtual_nodes):
            virtual_key = self._hash(f"{node.id}:{i}")
            self.ring[virtual_key] = node

        self.sorted_keys = sorted(self.ring.keys())

    def remove_node(self, node):
        """从哈希环移除节点"""
        for i in range(self.virtual_nodes):
            virtual_key = self._hash(f"{node.id}:{i}")
            if virtual_key in self.ring:
                del self.ring[virtual_key]

        self.sorted_keys = sorted(self.ring.keys())

    def get_nodes(self, key_hash, count=1):
        """获取负责该key的节点"""
        if not self.ring:
            return []

        nodes = []
        start_idx = self._binary_search(key_hash)

        seen_nodes = set()
        for i in range(len(self.sorted_keys)):
            idx = (start_idx + i) % len(self.sorted_keys)
            node = self.ring[self.sorted_keys[idx]]

            if node.id not in seen_nodes:
                nodes.append(node)
                seen_nodes.add(node.id)

                if len(nodes) >= count:
                    break

        return nodes
```

#### Q32: 如何设计 Redis 的监控和告警系统？
**来源**: Google SRE 面试
**难度**: ⭐⭐⭐⭐

**答案**:

**监控指标体系**:
```python
class RedisMonitoringSystem:
    """Google SRE 风格的 Redis 监控系统"""

    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.alerting_rules = AlertingRules()
        self.time_series_db = TimeSeriesDB()

    def collect_golden_signals(self, redis_instance):
        """收集四个黄金信号"""
        # 1. Latency (延迟)
        latency_metrics = self._collect_latency_metrics(redis_instance)

        # 2. Traffic (流量)
        traffic_metrics = self._collect_traffic_metrics(redis_instance)

        # 3. Errors (错误)
        error_metrics = self._collect_error_metrics(redis_instance)

        # 4. Saturation (饱和度)
        saturation_metrics = self._collect_saturation_metrics(redis_instance)

        return {
            'latency': latency_metrics,
            'traffic': traffic_metrics,
            'errors': error_metrics,
            'saturation': saturation_metrics
        }

    def _collect_latency_metrics(self, redis_instance):
        """收集延迟指标"""
        info = redis_instance.info()

        return {
            'avg_ttl_p50': self._get_percentile_latency(redis_instance, 50),
            'avg_ttl_p95': self._get_percentile_latency(redis_instance, 95),
            'avg_ttl_p99': self._get_percentile_latency(redis_instance, 99),
            'slow_queries_count': len(redis_instance.slowlog_get(10))
        }

    def _collect_traffic_metrics(self, redis_instance):
        """收集流量指标"""
        info = redis_instance.info()

        return {
            'ops_per_sec': info.get('instantaneous_ops_per_sec', 0),
            'network_input_bytes_per_sec': info.get('instantaneous_input_kbps', 0) * 1024,
            'network_output_bytes_per_sec': info.get('instantaneous_output_kbps', 0) * 1024,
            'connected_clients': info.get('connected_clients', 0)
        }

    def _collect_error_metrics(self, redis_instance):
        """收集错误指标"""
        info = redis_instance.info()

        return {
            'rejected_connections': info.get('rejected_connections', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
            'expired_keys': info.get('expired_keys', 0),
            'evicted_keys': info.get('evicted_keys', 0)
        }

    def _collect_saturation_metrics(self, redis_instance):
        """收集饱和度指标"""
        info = redis_instance.info()

        used_memory = info.get('used_memory', 0)
        max_memory = info.get('maxmemory', 0)

        return {
            'memory_utilization': used_memory / max_memory if max_memory > 0 else 0,
            'cpu_utilization': self._get_cpu_usage(redis_instance),
            'connection_utilization': info.get('connected_clients', 0) / info.get('maxclients', 10000),
            'memory_fragmentation_ratio': info.get('mem_fragmentation_ratio', 1.0)
        }

    def setup_sli_slo(self):
        """设置 SLI/SLO"""
        slos = {
            'availability': {
                'target': 99.9,  # 99.9% 可用性
                'measurement_window': '30d'
            },
            'latency': {
                'target': 95,  # 95% 请求延迟 < 10ms
                'threshold_ms': 10,
                'measurement_window': '24h'
            },
            'throughput': {
                'target': 100000,  # 最低 10万 QPS
                'measurement_window': '5m'
            }
        }

        return slos
```

### 9.2 Amazon AWS 面试题

#### Q33: 设计 ElastiCache 的自动故障转移机制
**来源**: Amazon AWS 系统设计面试
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**自动故障转移架构**:
```python
class ElastiCacheFailoverManager:
    """AWS ElastiCache 自动故障转移管理器"""

    def __init__(self):
        self.health_checker = HealthChecker()
        self.dns_manager = Route53Manager()
        self.notification_service = SNSNotificationService()
        self.backup_manager = BackupManager()

    def monitor_cluster_health(self, cluster_id):
        """监控集群健康状态"""
        cluster_info = self._get_cluster_info(cluster_id)

        for node in cluster_info['nodes']:
            health_status = self.health_checker.check_node_health(node)

            if health_status['status'] == 'unhealthy':
                self._handle_node_failure(cluster_id, node, health_status)

    def _handle_node_failure(self, cluster_id, failed_node, health_status):
        """处理节点故障"""
        if failed_node['role'] == 'primary':
            self._handle_primary_failure(cluster_id, failed_node)
        else:
            self._handle_replica_failure(cluster_id, failed_node)

    def _handle_primary_failure(self, cluster_id, failed_primary):
        """处理主节点故障"""
        # 1. 选择最佳的副本节点
        best_replica = self._select_best_replica(cluster_id, failed_primary)

        if not best_replica:
            # 没有可用副本，创建新实例
            self._create_new_primary(cluster_id, failed_primary)
            return

        # 2. 提升副本为主节点
        self._promote_replica_to_primary(best_replica)

        # 3. 更新 DNS 记录
        self.dns_manager.update_primary_endpoint(
            cluster_id,
            best_replica['endpoint']
        )

        # 4. 通知相关人员
        self.notification_service.send_failover_notification(
            cluster_id,
            failed_primary,
            best_replica
        )

        # 5. 启动新的副本节点
        self._launch_replacement_replica(cluster_id, failed_primary)

    def _select_best_replica(self, cluster_id, failed_primary):
        """选择最佳副本节点"""
        replicas = self._get_healthy_replicas(cluster_id)

        if not replicas:
            return None

        # 评估标准：
        # 1. 复制延迟最小
        # 2. 可用区分布
        # 3. 实例类型
        best_replica = None
        best_score = -1

        for replica in replicas:
            score = self._calculate_replica_score(replica, failed_primary)
            if score > best_score:
                best_score = score
                best_replica = replica

        return best_replica

    def _calculate_replica_score(self, replica, failed_primary):
        """计算副本评分"""
        score = 0

        # 复制延迟评分 (权重: 40%)
        replication_lag = replica.get('replication_lag_seconds', 0)
        if replication_lag < 1:
            score += 40
        elif replication_lag < 5:
            score += 30
        elif replication_lag < 10:
            score += 20

        # 可用区评分 (权重: 30%)
        if replica['availability_zone'] != failed_primary['availability_zone']:
            score += 30

        # 实例类型评分 (权重: 20%)
        if replica['instance_type'] == failed_primary['instance_type']:
            score += 20

        # 网络性能评分 (权重: 10%)
        if replica.get('enhanced_networking', False):
            score += 10

        return score

    def _promote_replica_to_primary(self, replica):
        """提升副本为主节点"""
        # 1. 停止复制
        replica.execute_command('REPLICAOF NO ONE')

        # 2. 更新节点角色
        replica.update_role('primary')

        # 3. 启用写入
        replica.config_set('replica-read-only', 'no')

        # 4. 更新监控标签
        replica.update_tags({'Role': 'Primary'})
```

#### Q34: 如何实现 Redis 的多区域复制？
**来源**: Amazon AWS 架构师面试
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**多区域复制架构**:
```python
class MultiRegionRedisReplication:
    """AWS 多区域 Redis 复制系统"""

    def __init__(self):
        self.regions = ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1']
        self.primary_region = 'us-east-1'
        self.replication_topology = self._setup_topology()

    def _setup_topology(self):
        """设置复制拓扑"""
        topology = {
            'primary': {
                'region': self.primary_region,
                'cluster_id': 'redis-primary-cluster',
                'role': 'master'
            },
            'replicas': []
        }

        for region in self.regions:
            if region != self.primary_region:
                topology['replicas'].append({
                    'region': region,
                    'cluster_id': f'redis-replica-{region}',
                    'role': 'replica',
                    'replication_mode': 'async'
                })

        return topology

    def setup_cross_region_replication(self):
        """设置跨区域复制"""
        primary_cluster = self._get_cluster(
            self.topology['primary']['region'],
            self.topology['primary']['cluster_id']
        )

        for replica_config in self.topology['replicas']:
            replica_cluster = self._get_cluster(
                replica_config['region'],
                replica_config['cluster_id']
            )

            # 配置跨区域复制
            self._configure_cross_region_replication(
                primary_cluster,
                replica_cluster,
                replica_config
            )

    def _configure_cross_region_replication(self, primary, replica, config):
        """配置跨区域复制"""
        # 1. 设置 VPC 对等连接
        self._setup_vpc_peering(primary['region'], replica['region'])

        # 2. 配置安全组
        self._configure_security_groups(primary, replica)

        # 3. 启用复制
        replica_endpoint = replica['primary_endpoint']
        primary_endpoint = primary['primary_endpoint']

        # 在副本集群执行复制命令
        replica.execute_command(f'REPLICAOF {primary_endpoint} 6379')

        # 4. 配置复制参数
        replica.config_set('repl-backlog-size', '256mb')
        replica.config_set('repl-backlog-ttl', '3600')
        replica.config_set('replica-priority', '100')

        # 5. 启用压缩传输
        replica.config_set('repl-diskless-sync', 'yes')
        replica.config_set('repl-diskless-sync-delay', '5')

    def handle_region_failover(self, failed_region):
        """处理区域故障转移"""
        if failed_region == self.primary_region:
            # 主区域故障，需要提升副本
            self._promote_replica_region()
        else:
            # 副本区域故障，重建副本
            self._rebuild_replica_region(failed_region)

    def _promote_replica_region(self):
        """提升副本区域为主区域"""
        # 1. 选择最佳副本区域
        best_replica = self._select_best_replica_region()

        # 2. 提升为主区域
        best_replica_cluster = self._get_cluster(
            best_replica['region'],
            best_replica['cluster_id']
        )

        # 停止复制，成为独立主节点
        best_replica_cluster.execute_command('REPLICAOF NO ONE')

        # 3. 更新 DNS 记录
        self._update_global_dns(best_replica['region'])

        # 4. 重新配置其他副本
        self._reconfigure_remaining_replicas(best_replica['region'])

        # 5. 更新拓扑
        self.primary_region = best_replica['region']
        self.replication_topology = self._setup_topology()

    def monitor_replication_lag(self):
        """监控复制延迟"""
        primary_cluster = self._get_cluster(
            self.topology['primary']['region'],
            self.topology['primary']['cluster_id']
        )

        lag_metrics = {}

        for replica_config in self.topology['replicas']:
            replica_cluster = self._get_cluster(
                replica_config['region'],
                replica_config['cluster_id']
            )

            # 获取复制延迟
            replica_info = replica_cluster.info('replication')
            lag_seconds = replica_info.get('master_last_io_seconds_ago', 0)

            lag_metrics[replica_config['region']] = {
                'lag_seconds': lag_seconds,
                'status': 'healthy' if lag_seconds < 10 else 'lagging'
            }

            # 发送告警
            if lag_seconds > 30:
                self._send_replication_lag_alert(
                    replica_config['region'],
                    lag_seconds
                )

        return lag_metrics
```

### 9.3 Meta (Facebook) 面试题

#### Q35: 设计 Facebook 新闻流的缓存系统
**来源**: Meta 系统设计面试
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**新闻流缓存架构**:
```python
class FacebookNewsFeedCache:
    """Facebook 新闻流缓存系统"""

    def __init__(self):
        self.user_cache = RedisCluster('user-cache')
        self.feed_cache = RedisCluster('feed-cache')
        self.content_cache = RedisCluster('content-cache')
        self.social_graph_cache = RedisCluster('social-graph-cache')

    def generate_news_feed(self, user_id: int, limit: int = 20):
        """生成用户新闻流"""
        # 1. 检查用户新闻流缓存
        cached_feed = self.feed_cache.get(f"feed:{user_id}")
        if cached_feed:
            return self._hydrate_feed_content(cached_feed[:limit])

        # 2. 获取用户社交图谱
        friends = self._get_user_friends(user_id)

        # 3. 获取朋友的最新动态
        friend_posts = self._get_friends_recent_posts(friends)

        # 4. 应用排序算法
        ranked_posts = self._rank_posts(user_id, friend_posts)

        # 5. 缓存结果
        self.feed_cache.setex(
            f"feed:{user_id}",
            300,  # 5分钟过期
            ranked_posts[:100]  # 缓存前100条
        )

        return self._hydrate_feed_content(ranked_posts[:limit])

    def _get_user_friends(self, user_id: int):
        """获取用户好友列表"""
        cache_key = f"friends:{user_id}"
        friends = self.social_graph_cache.get(cache_key)

        if not friends:
            # 从数据库加载
            friends = self._load_friends_from_db(user_id)
            self.social_graph_cache.setex(cache_key, 3600, friends)

        return friends

    def _get_friends_recent_posts(self, friends: list):
        """获取朋友的最新动态"""
        all_posts = []

        # 并行获取每个朋友的最新动态
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []

            for friend_id in friends:
                future = executor.submit(self._get_user_recent_posts, friend_id)
                futures.append(future)

            for future in futures:
                try:
                    posts = future.result(timeout=0.1)  # 100ms 超时
                    all_posts.extend(posts)
                except TimeoutError:
                    continue

        return all_posts

    def _get_user_recent_posts(self, user_id: int):
        """获取用户最新动态"""
        cache_key = f"user_posts:{user_id}"
        posts = self.content_cache.get(cache_key)

        if not posts:
            posts = self._load_user_posts_from_db(user_id, limit=50)
            self.content_cache.setex(cache_key, 600, posts)  # 10分钟

        return posts

    def _rank_posts(self, user_id: int, posts: list):
        """对动态进行排序"""
        # Facebook 的 EdgeRank 算法简化版
        scored_posts = []

        for post in posts:
            score = self._calculate_edge_rank_score(user_id, post)
            scored_posts.append((score, post))

        # 按分数排序
        scored_posts.sort(key=lambda x: x[0], reverse=True)

        return [post for score, post in scored_posts]

    def _calculate_edge_rank_score(self, user_id: int, post: dict):
        """计算 EdgeRank 分数"""
        # EdgeRank = Affinity × Weight × Time Decay

        # 1. 亲密度 (Affinity)
        affinity = self._get_user_affinity(user_id, post['author_id'])

        # 2. 权重 (Weight)
        weight = self._get_post_weight(post)

        # 3. 时间衰减 (Time Decay)
        time_decay = self._calculate_time_decay(post['created_at'])

        return affinity * weight * time_decay

    def _get_user_affinity(self, user_id: int, author_id: int):
        """获取用户亲密度"""
        cache_key = f"affinity:{user_id}:{author_id}"
        affinity = self.user_cache.get(cache_key)

        if affinity is None:
            # 计算亲密度：互动频率、共同好友等
            affinity = self._calculate_affinity(user_id, author_id)
            self.user_cache.setex(cache_key, 1800, affinity)  # 30分钟

        return float(affinity)

    def _get_post_weight(self, post: dict):
        """获取动态权重"""
        weights = {
            'photo': 1.0,
            'video': 1.2,
            'link': 0.8,
            'status': 0.6
        }

        base_weight = weights.get(post['type'], 0.5)

        # 根据互动数调整权重
        engagement_boost = min(post.get('likes', 0) * 0.01 +
                             post.get('comments', 0) * 0.02 +
                             post.get('shares', 0) * 0.03, 2.0)

        return base_weight * (1 + engagement_boost)

    def handle_new_post(self, post: dict):
        """处理新发布的动态"""
        author_id = post['author_id']

        # 1. 缓存动态内容
        self.content_cache.setex(
            f"post:{post['id']}",
            86400,  # 24小时
            post
        )

        # 2. 更新作者的动态列表
        self._update_user_posts_cache(author_id, post)

        # 3. 推送给在线好友
        self._push_to_online_friends(author_id, post)

        # 4. 异步更新相关用户的新闻流
        self._async_update_friends_feeds(author_id, post)

    def _push_to_online_friends(self, author_id: int, post: dict):
        """推送给在线好友"""
        online_friends = self._get_online_friends(author_id)

        for friend_id in online_friends:
            # 实时推送到客户端
            self._push_to_client(friend_id, post)

            # 更新好友的新闻流缓存
            feed_key = f"feed:{friend_id}"
            self.feed_cache.lpush(feed_key, post['id'])
            self.feed_cache.ltrim(feed_key, 0, 99)  # 保持100条
```

### 9.4 Microsoft 面试题

#### Q36: 设计 Azure Cache for Redis 的高可用架构
**来源**: Microsoft Azure 架构师面试
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**Azure Redis 高可用架构**:
```python
class AzureRedisHighAvailability:
    """Azure Cache for Redis 高可用架构"""

    def __init__(self):
        self.primary_region = 'East US'
        self.secondary_region = 'West US'
        self.geo_replication = GeoReplicationManager()
        self.traffic_manager = AzureTrafficManager()
        self.monitoring = AzureMonitor()

    def setup_premium_cluster(self):
        """设置 Premium 集群"""
        cluster_config = {
            'tier': 'Premium',
            'size': 'P4',  # 26GB
            'shard_count': 3,
            'replica_count': 1,
            'zones': ['1', '2', '3'],  # 可用区分布
            'persistence': {
                'rdb_enabled': True,
                'rdb_frequency': '15min',
                'aof_enabled': True,
                'aof_frequency': '1sec'
            }
        }

        # 主区域集群
        primary_cluster = self._create_redis_cluster(
            self.primary_region,
            cluster_config
        )

        # 次区域集群
        secondary_cluster = self._create_redis_cluster(
            self.secondary_region,
            cluster_config
        )

        # 配置地理复制
        self.geo_replication.setup_replication(
            primary_cluster,
            secondary_cluster
        )

        return primary_cluster, secondary_cluster

    def configure_zone_redundancy(self, cluster):
        """配置可用区冗余"""
        # 确保节点分布在不同可用区
        shard_placement = {
            'shard_0': {'primary': 'zone_1', 'replica': 'zone_2'},
            'shard_1': {'primary': 'zone_2', 'replica': 'zone_3'},
            'shard_2': {'primary': 'zone_3', 'replica': 'zone_1'}
        }

        for shard_id, placement in shard_placement.items():
            self._configure_shard_placement(cluster, shard_id, placement)

    def implement_circuit_breaker(self):
        """实现熔断器模式"""
        class RedisCircuitBreaker:
            def __init__(self, failure_threshold=5, timeout=60):
                self.failure_threshold = failure_threshold
                self.timeout = timeout
                self.failure_count = 0
                self.last_failure_time = None
                self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

            def call_redis(self, redis_operation):
                if self.state == 'OPEN':
                    if time.time() - self.last_failure_time > self.timeout:
                        self.state = 'HALF_OPEN'
                    else:
                        raise CircuitBreakerOpenException()

                try:
                    result = redis_operation()
                    self._on_success()
                    return result
                except Exception as e:
                    self._on_failure()
                    raise e

            def _on_success(self):
                self.failure_count = 0
                self.state = 'CLOSED'

            def _on_failure(self):
                self.failure_count += 1
                self.last_failure_time = time.time()

                if self.failure_count >= self.failure_threshold:
                    self.state = 'OPEN'

        return RedisCircuitBreaker()

    def setup_traffic_manager_failover(self):
        """设置流量管理器故障转移"""
        traffic_manager_config = {
            'routing_method': 'Priority',
            'endpoints': [
                {
                    'name': 'primary-redis',
                    'target': f'{self.primary_region}-redis.redis.cache.windows.net',
                    'priority': 1,
                    'weight': 100
                },
                {
                    'name': 'secondary-redis',
                    'target': f'{self.secondary_region}-redis.redis.cache.windows.net',
                    'priority': 2,
                    'weight': 100
                }
            ],
            'health_check': {
                'protocol': 'TCP',
                'port': 6380,
                'interval': 30,
                'timeout': 10,
                'tolerated_failures': 3
            }
        }

        return self.traffic_manager.create_profile(traffic_manager_config)
```

#### Q37: 如何实现 Redis 的自动扩缩容？
**来源**: Microsoft Azure 云服务面试
**难度**: ⭐⭐⭐⭐

**答案**:

**自动扩缩容实现**:
```python
class AzureRedisAutoScaling:
    """Azure Redis 自动扩缩容"""

    def __init__(self):
        self.metrics_client = AzureMetricsClient()
        self.redis_client = AzureRedisClient()
        self.autoscale_rules = []

    def setup_autoscale_rules(self):
        """设置自动扩缩容规则"""
        # CPU 使用率规则
        cpu_scale_out_rule = {
            'metric': 'cpu_percent',
            'threshold': 80,
            'direction': 'scale_out',
            'action': 'increase_tier',
            'cooldown': 300  # 5分钟冷却期
        }

        cpu_scale_in_rule = {
            'metric': 'cpu_percent',
            'threshold': 30,
            'direction': 'scale_in',
            'action': 'decrease_tier',
            'cooldown': 600  # 10分钟冷却期
        }

        # 内存使用率规则
        memory_scale_out_rule = {
            'metric': 'memory_percent',
            'threshold': 85,
            'direction': 'scale_out',
            'action': 'increase_tier',
            'cooldown': 300
        }

        # 连接数规则
        connection_scale_out_rule = {
            'metric': 'connected_clients_percent',
            'threshold': 90,
            'direction': 'scale_out',
            'action': 'increase_tier',
            'cooldown': 300
        }

        self.autoscale_rules = [
            cpu_scale_out_rule,
            cpu_scale_in_rule,
            memory_scale_out_rule,
            connection_scale_out_rule
        ]

    def monitor_and_scale(self, redis_instance_id):
        """监控并执行扩缩容"""
        current_metrics = self.metrics_client.get_metrics(
            redis_instance_id,
            time_range='PT5M'  # 过去5分钟
        )

        for rule in self.autoscale_rules:
            if self._should_trigger_rule(current_metrics, rule):
                self._execute_scaling_action(redis_instance_id, rule)

    def _should_trigger_rule(self, metrics, rule):
        """判断是否应该触发规则"""
        metric_value = metrics.get(rule['metric'], 0)

        if rule['direction'] == 'scale_out':
            return metric_value > rule['threshold']
        else:
            return metric_value < rule['threshold']

    def _execute_scaling_action(self, instance_id, rule):
        """执行扩缩容操作"""
        current_tier = self.redis_client.get_instance_tier(instance_id)

        if rule['action'] == 'increase_tier':
            new_tier = self._get_next_tier(current_tier, 'up')
        else:
            new_tier = self._get_next_tier(current_tier, 'down')

        if new_tier and new_tier != current_tier:
            # 检查冷却期
            if self._is_in_cooldown(instance_id, rule['cooldown']):
                return

            # 执行扩缩容
            self.redis_client.scale_instance(instance_id, new_tier)

            # 记录扩缩容事件
            self._log_scaling_event(instance_id, current_tier, new_tier, rule)

    def _get_next_tier(self, current_tier, direction):
        """获取下一个层级"""
        tiers = ['C0', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6',
                'P1', 'P2', 'P3', 'P4', 'P5']

        try:
            current_index = tiers.index(current_tier)

            if direction == 'up' and current_index < len(tiers) - 1:
                return tiers[current_index + 1]
            elif direction == 'down' and current_index > 0:
                return tiers[current_index - 1]
        except ValueError:
            pass

        return None

    def implement_predictive_scaling(self, instance_id):
        """实现预测性扩缩容"""
        # 获取历史数据
        historical_data = self.metrics_client.get_historical_metrics(
            instance_id,
            time_range='P7D'  # 过去7天
        )

        # 使用机器学习预测未来负载
        predicted_load = self._predict_future_load(historical_data)

        # 根据预测结果提前扩缩容
        if predicted_load['peak_cpu'] > 80:
            self._preemptive_scale_out(instance_id)
        elif predicted_load['peak_cpu'] < 30:
            self._preemptive_scale_in(instance_id)

    def _predict_future_load(self, historical_data):
        """预测未来负载"""
        # 简化的预测算法，实际应该使用更复杂的ML模型
        import numpy as np

        cpu_data = [point['cpu_percent'] for point in historical_data]
        memory_data = [point['memory_percent'] for point in historical_data]

        # 计算趋势
        cpu_trend = np.polyfit(range(len(cpu_data)), cpu_data, 1)[0]
        memory_trend = np.polyfit(range(len(memory_data)), memory_data, 1)[0]

        # 预测下一小时的峰值
        current_cpu = cpu_data[-1] if cpu_data else 0
        current_memory = memory_data[-1] if memory_data else 0

        predicted_cpu = current_cpu + cpu_trend * 12  # 12个5分钟间隔
        predicted_memory = current_memory + memory_trend * 12

        return {
            'peak_cpu': max(predicted_cpu, max(cpu_data[-12:]) if len(cpu_data) >= 12 else 0),
            'peak_memory': max(predicted_memory, max(memory_data[-12:]) if len(memory_data) >= 12 else 0)
        }
```

### 9.5 Apple 面试题

#### Q38: 设计 iCloud 的分布式缓存系统
**来源**: Apple iCloud 基础架构面试
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**iCloud 分布式缓存架构**:
```python
class iCloudDistributedCache:
    """Apple iCloud 分布式缓存系统"""

    def __init__(self):
        self.cache_tiers = {
            'L1': LocalCache(),      # 设备本地缓存
            'L2': EdgeCache(),       # 边缘节点缓存
            'L3': RegionalCache(),   # 区域数据中心缓存
            'L4': GlobalCache()      # 全球数据中心缓存
        }
        self.privacy_manager = PrivacyManager()
        self.encryption = EndToEndEncryption()

    def get_user_data(self, user_id: str, data_type: str, device_id: str):
        """获取用户数据"""
        # 1. 检查设备本地缓存
        cache_key = self._generate_cache_key(user_id, data_type, device_id)

        data = self.cache_tiers['L1'].get(cache_key)
        if data:
            return self._decrypt_data(data, user_id)

        # 2. 检查边缘缓存
        edge_location = self._get_nearest_edge_location(device_id)
        data = self.cache_tiers['L2'].get(cache_key, edge_location)
        if data:
            # 回填到本地缓存
            self.cache_tiers['L1'].set(cache_key, data, ttl=300)
            return self._decrypt_data(data, user_id)

        # 3. 检查区域缓存
        region = self._get_user_region(user_id)
        data = self.cache_tiers['L3'].get(cache_key, region)
        if data:
            # 回填到边缘和本地缓存
            self._backfill_cache(cache_key, data, ['L2', 'L1'])
            return self._decrypt_data(data, user_id)

        # 4. 从全球缓存或数据库加载
        data = self._load_from_storage(user_id, data_type)
        if data:
            encrypted_data = self._encrypt_data(data, user_id)
            # 回填到所有缓存层
            self._backfill_cache(cache_key, encrypted_data, ['L4', 'L3', 'L2', 'L1'])
            return data

        return None

    def _generate_cache_key(self, user_id: str, data_type: str, device_id: str):
        """生成缓存键"""
        # 考虑隐私保护，对用户ID进行哈希
        hashed_user_id = self.privacy_manager.hash_user_id(user_id)
        return f"{hashed_user_id}:{data_type}:{device_id}"

    def _encrypt_data(self, data: bytes, user_id: str):
        """端到端加密数据"""
        user_key = self.encryption.get_user_key(user_id)
        return self.encryption.encrypt(data, user_key)

    def _decrypt_data(self, encrypted_data: bytes, user_id: str):
        """解密数据"""
        user_key = self.encryption.get_user_key(user_id)
        return self.encryption.decrypt(encrypted_data, user_key)

    def handle_device_sync(self, user_id: str, device_id: str, sync_data: dict):
        """处理设备同步"""
        # 1. 验证设备权限
        if not self.privacy_manager.verify_device_access(user_id, device_id):
            raise UnauthorizedDeviceError()

        # 2. 加密同步数据
        encrypted_data = self._encrypt_data(
            json.dumps(sync_data).encode(),
            user_id
        )

        # 3. 更新所有缓存层
        cache_key = self._generate_cache_key(user_id, 'sync', device_id)

        for tier_name, cache_tier in self.cache_tiers.items():
            try:
                cache_tier.set(cache_key, encrypted_data, ttl=3600)
            except Exception as e:
                # 记录但不阻塞同步
                self._log_cache_error(tier_name, e)

        # 4. 通知其他设备
        self._notify_other_devices(user_id, device_id, sync_data)

    def implement_privacy_compliance(self):
        """实现隐私合规"""
        class PrivacyCompliantCache:
            def __init__(self, base_cache):
                self.base_cache = base_cache
                self.data_retention_policy = DataRetentionPolicy()
                self.gdpr_compliance = GDPRCompliance()

            def set(self, key, value, ttl=None):
                # 检查数据分类
                data_classification = self._classify_data(value)

                # 应用保留策略
                adjusted_ttl = self.data_retention_policy.get_ttl(
                    data_classification, ttl
                )

                # 添加隐私标记
                privacy_metadata = {
                    'classification': data_classification,
                    'retention_period': adjusted_ttl,
                    'created_at': time.time()
                }

                wrapped_value = {
                    'data': value,
                    'privacy_metadata': privacy_metadata
                }

                return self.base_cache.set(key, wrapped_value, adjusted_ttl)

            def get(self, key):
                wrapped_value = self.base_cache.get(key)
                if not wrapped_value:
                    return None

                # 检查数据是否过期（隐私角度）
                if self._is_privacy_expired(wrapped_value['privacy_metadata']):
                    self.base_cache.delete(key)
                    return None

                return wrapped_value['data']

            def _classify_data(self, data):
                """数据分类"""
                # 简化的数据分类逻辑
                if 'personal' in str(data).lower():
                    return 'PII'  # 个人身份信息
                elif 'health' in str(data).lower():
                    return 'PHI'  # 健康信息
                else:
                    return 'GENERAL'

            def _is_privacy_expired(self, metadata):
                """检查隐私过期"""
                created_at = metadata['created_at']
                retention_period = metadata['retention_period']

                return time.time() - created_at > retention_period

        # 包装所有缓存层
        for tier_name, cache_tier in self.cache_tiers.items():
            self.cache_tiers[tier_name] = PrivacyCompliantCache(cache_tier)

    def optimize_for_mobile_devices(self):
        """针对移动设备优化"""
        mobile_optimizations = {
            'compression': True,
            'delta_sync': True,  # 增量同步
            'background_prefetch': True,
            'battery_aware_caching': True
        }

        return mobile_optimizations
```

---

## 第十部分：硬件大厂面试题

### 10.1 Intel 面试题

#### Q39: 设计支持 Intel SGX 的安全 Redis 缓存系统
**来源**: Intel 安全计算部门
**难度**: ⭐⭐⭐⭐⭐

**问题**: 设计一个基于 Intel SGX (Software Guard Extensions) 的安全 Redis 缓存系统，保护敏感数据在内存中的安全。

**答案**:

**SGX 安全缓存架构**:
```cpp
// Intel SGX 安全 Redis 实现
#include <sgx.h>
#include <sgx_tcrypto.h>
#include <sgx_trts.h>

class SGXSecureRedisCache {
private:
    sgx_aes_gcm_128bit_key_t encryption_key;
    sgx_enclave_id_t enclave_id;

public:
    SGXSecureRedisCache() {
        // 初始化 SGX enclave
        sgx_status_t ret = sgx_create_enclave(
            "secure_redis.signed.so",
            SGX_DEBUG_FLAG,
            nullptr,
            nullptr,
            &enclave_id,
            nullptr
        );

        if (ret != SGX_SUCCESS) {
            throw std::runtime_error("Failed to create SGX enclave");
        }

        // 生成加密密钥
        generate_encryption_key();
    }

    // 安全存储数据
    bool secure_set(const std::string& key, const std::string& value) {
        // 1. 在 enclave 内部加密数据
        std::vector<uint8_t> encrypted_data;
        if (!encrypt_in_enclave(value, encrypted_data)) {
            return false;
        }

        // 2. 存储加密后的数据
        return store_encrypted_data(key, encrypted_data);
    }

    // 安全获取数据
    std::string secure_get(const std::string& key) {
        // 1. 获取加密数据
        std::vector<uint8_t> encrypted_data = get_encrypted_data(key);
        if (encrypted_data.empty()) {
            return "";
        }

        // 2. 在 enclave 内部解密
        std::string decrypted_value;
        if (!decrypt_in_enclave(encrypted_data, decrypted_value)) {
            return "";
        }

        return decrypted_value;
    }

private:
    void generate_encryption_key() {
        sgx_status_t ret = sgx_read_rand(
            reinterpret_cast<unsigned char*>(&encryption_key),
            sizeof(encryption_key)
        );

        if (ret != SGX_SUCCESS) {
            throw std::runtime_error("Failed to generate encryption key");
        }
    }

    bool encrypt_in_enclave(const std::string& plaintext,
                           std::vector<uint8_t>& ciphertext) {
        // SGX enclave 调用进行加密
        sgx_status_t ret;
        size_t ciphertext_len = plaintext.length() + SGX_AESGCM_MAC_SIZE + SGX_AESGCM_IV_SIZE;
        ciphertext.resize(ciphertext_len);

        ret = ecall_encrypt_data(
            enclave_id,
            plaintext.c_str(),
            plaintext.length(),
            ciphertext.data(),
            ciphertext_len
        );

        return ret == SGX_SUCCESS;
    }

    bool decrypt_in_enclave(const std::vector<uint8_t>& ciphertext,
                           std::string& plaintext) {
        // SGX enclave 调用进行解密
        sgx_status_t ret;
        size_t plaintext_len = ciphertext.size();
        std::vector<char> plaintext_buffer(plaintext_len);

        ret = ecall_decrypt_data(
            enclave_id,
            ciphertext.data(),
            ciphertext.size(),
            plaintext_buffer.data(),
            plaintext_len
        );

        if (ret == SGX_SUCCESS) {
            plaintext = std::string(plaintext_buffer.data(), plaintext_len);
            return true;
        }

        return false;
    }
};

// SGX Enclave 内部实现
extern "C" {
    sgx_status_t ecall_encrypt_data(const char* plaintext,
                                   size_t plaintext_len,
                                   uint8_t* ciphertext,
                                   size_t ciphertext_len) {
        // 在 enclave 内部进行 AES-GCM 加密
        sgx_aes_gcm_128bit_key_t key;
        uint8_t iv[SGX_AESGCM_IV_SIZE];
        uint8_t mac[SGX_AESGCM_MAC_SIZE];

        // 生成随机 IV
        sgx_status_t ret = sgx_read_rand(iv, SGX_AESGCM_IV_SIZE);
        if (ret != SGX_SUCCESS) return ret;

        // 执行加密
        ret = sgx_rijndael128GCM_encrypt(
            &key,
            reinterpret_cast<const uint8_t*>(plaintext),
            plaintext_len,
            ciphertext + SGX_AESGCM_IV_SIZE,
            iv,
            SGX_AESGCM_IV_SIZE,
            nullptr,
            0,
            mac
        );

        if (ret == SGX_SUCCESS) {
            // 将 IV 和 MAC 添加到密文前面
            memcpy(ciphertext, iv, SGX_AESGCM_IV_SIZE);
            memcpy(ciphertext + SGX_AESGCM_IV_SIZE + plaintext_len,
                   mac, SGX_AESGCM_MAC_SIZE);
        }

        return ret;
    }

    sgx_status_t ecall_decrypt_data(const uint8_t* ciphertext,
                                   size_t ciphertext_len,
                                   char* plaintext,
                                   size_t plaintext_len) {
        // 在 enclave 内部进行 AES-GCM 解密
        sgx_aes_gcm_128bit_key_t key;
        const uint8_t* iv = ciphertext;
        const uint8_t* encrypted_data = ciphertext + SGX_AESGCM_IV_SIZE;
        const uint8_t* mac = ciphertext + ciphertext_len - SGX_AESGCM_MAC_SIZE;
        size_t encrypted_len = ciphertext_len - SGX_AESGCM_IV_SIZE - SGX_AESGCM_MAC_SIZE;

        return sgx_rijndael128GCM_decrypt(
            &key,
            encrypted_data,
            encrypted_len,
            reinterpret_cast<uint8_t*>(plaintext),
            iv,
            SGX_AESGCM_IV_SIZE,
            nullptr,
            0,
            reinterpret_cast<const sgx_aes_gcm_128bit_tag_t*>(mac)
        );
    }
}
```

#### Q40: 如何优化 Redis 在 Intel 处理器上的性能？
**来源**: Intel 性能工程部门
**难度**: ⭐⭐⭐⭐

**答案**:

**Intel 处理器优化策略**:
```cpp
class IntelOptimizedRedis {
private:
    // 使用 Intel 特定的优化
    bool use_avx512;
    bool use_intel_mkl;
    bool use_dpdk;

public:
    IntelOptimizedRedis() {
        // 检测 CPU 特性
        detect_cpu_features();

        // 启用 Intel 特定优化
        enable_intel_optimizations();
    }

    void detect_cpu_features() {
        // 检测 AVX-512 支持
        use_avx512 = check_avx512_support();

        // 检测其他 Intel 特性
        use_intel_mkl = check_mkl_support();
        use_dpdk = check_dpdk_support();
    }

    // 使用 AVX-512 优化字符串操作
    void optimized_string_copy(const char* src, char* dst, size_t len) {
        if (use_avx512 && len >= 64) {
            // 使用 AVX-512 进行批量内存操作
            avx512_memcpy(src, dst, len);
        } else {
            // 回退到标准实现
            memcpy(dst, src, len);
        }
    }

    // Intel MKL 优化的哈希计算
    uint64_t optimized_hash(const void* data, size_t len) {
        if (use_intel_mkl) {
            return intel_mkl_hash(data, len);
        } else {
            return standard_hash(data, len);
        }
    }

    // DPDK 优化的网络 I/O
    void setup_dpdk_networking() {
        if (!use_dpdk) return;

        // 初始化 DPDK
        int ret = rte_eal_init(argc, argv);
        if (ret < 0) {
            throw std::runtime_error("DPDK initialization failed");
        }

        // 配置网络端口
        configure_dpdk_ports();

        // 启动 DPDK 工作线程
        launch_dpdk_workers();
    }

private:
    void avx512_memcpy(const void* src, void* dst, size_t len) {
        // 使用 AVX-512 指令进行高速内存复制
        const __m512i* src_vec = static_cast<const __m512i*>(src);
        __m512i* dst_vec = static_cast<__m512i*>(dst);

        size_t vec_count = len / 64;
        for (size_t i = 0; i < vec_count; i++) {
            __m512i data = _mm512_load_si512(&src_vec[i]);
            _mm512_store_si512(&dst_vec[i], data);
        }

        // 处理剩余字节
        size_t remaining = len % 64;
        if (remaining > 0) {
            memcpy(dst_vec + vec_count, src_vec + vec_count, remaining);
        }
    }

    uint64_t intel_mkl_hash(const void* data, size_t len) {
        // 使用 Intel MKL 库进行优化的哈希计算
        // 这里是示例实现
        return mkl_optimized_crc64(data, len);
    }

    void configure_dpdk_ports() {
        // DPDK 端口配置
        struct rte_eth_conf port_conf = {};
        port_conf.rxmode.mq_mode = ETH_MQ_RX_RSS;
        port_conf.txmode.mq_mode = ETH_MQ_TX_NONE;

        // 配置接收队列
        int ret = rte_eth_dev_configure(0, 1, 1, &port_conf);
        if (ret < 0) {
            throw std::runtime_error("DPDK port configuration failed");
        }
    }
};

// Intel 性能监控和调优
class IntelPerformanceMonitor {
public:
    void setup_pmu_monitoring() {
        // 设置 Intel PMU (Performance Monitoring Unit) 监控
        setup_cache_monitoring();
        setup_memory_bandwidth_monitoring();
        setup_instruction_monitoring();
    }

private:
    void setup_cache_monitoring() {
        // 监控 L1/L2/L3 缓存命中率
        // 使用 Intel PCM (Performance Counter Monitor)
    }

    void setup_memory_bandwidth_monitoring() {
        // 监控内存带宽使用情况
        // 使用 Intel Memory Latency Checker
    }

    void setup_instruction_monitoring() {
        // 监控指令执行效率
        // 使用 Intel VTune Profiler API
    }
};
```

### 10.2 NVIDIA 面试题

#### Q41: 设计 GPU 加速的 Redis 缓存系统
**来源**: NVIDIA CUDA 软件工程部门
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**GPU 加速 Redis 架构**:
```cuda
// CUDA 加速的 Redis 实现
#include <cuda_runtime.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>
#include <thrust/sort.h>
#include <thrust/binary_search.h>

class CUDAAcceleratedRedis {
private:
    // GPU 内存管理
    void* gpu_memory_pool;
    size_t gpu_memory_size;

    // CUDA 流用于异步操作
    cudaStream_t compute_stream;
    cudaStream_t memory_stream;

public:
    CUDAAcceleratedRedis(size_t gpu_mem_size = 1024 * 1024 * 1024) // 1GB
        : gpu_memory_size(gpu_mem_size) {

        // 分配 GPU 内存
        cudaMalloc(&gpu_memory_pool, gpu_memory_size);

        // 创建 CUDA 流
        cudaStreamCreate(&compute_stream);
        cudaStreamCreate(&memory_stream);

        // 初始化 GPU 哈希表
        initialize_gpu_hashtable();
    }

    ~CUDAAcceleratedRedis() {
        cudaFree(gpu_memory_pool);
        cudaStreamDestroy(compute_stream);
        cudaStreamDestroy(memory_stream);
    }

    // GPU 加速的批量操作
    void batch_set(const std::vector<std::string>& keys,
                   const std::vector<std::string>& values) {

        // 1. 将数据传输到 GPU
        thrust::device_vector<char> gpu_keys = prepare_gpu_keys(keys);
        thrust::device_vector<char> gpu_values = prepare_gpu_values(values);

        // 2. 在 GPU 上并行计算哈希值
        thrust::device_vector<uint64_t> gpu_hashes(keys.size());
        compute_hashes_gpu<<<(keys.size() + 255) / 256, 256, 0, compute_stream>>>(
            thrust::raw_pointer_cast(gpu_keys.data()),
            thrust::raw_pointer_cast(gpu_hashes.data()),
            keys.size()
        );

        // 3. 并行插入到哈希表
        parallel_insert_gpu<<<(keys.size() + 255) / 256, 256, 0, compute_stream>>>(
            thrust::raw_pointer_cast(gpu_hashes.data()),
            thrust::raw_pointer_cast(gpu_keys.data()),
            thrust::raw_pointer_cast(gpu_values.data()),
            keys.size()
        );

        // 4. 同步等待完成
        cudaStreamSynchronize(compute_stream);
    }

    // GPU 加速的范围查询
    std::vector<std::string> range_query(const std::string& start_key,
                                        const std::string& end_key) {

        // 1. 在 GPU 上进行并行搜索
        thrust::device_vector<bool> match_flags = parallel_range_search_gpu(start_key, end_key);

        // 2. 收集匹配的结果
        return collect_matching_results(match_flags);
    }

private:
    void initialize_gpu_hashtable() {
        // 初始化 GPU 上的哈希表结构
        // 使用 CUDA 内存管理器分配空间
    }

    thrust::device_vector<char> prepare_gpu_keys(const std::vector<std::string>& keys) {
        // 将字符串键转换为 GPU 友好的格式
        size_t total_size = 0;
        for (const auto& key : keys) {
            total_size += key.length() + 1; // +1 for null terminator
        }

        thrust::host_vector<char> host_data(total_size);
        size_t offset = 0;

        for (const auto& key : keys) {
            memcpy(host_data.data() + offset, key.c_str(), key.length() + 1);
            offset += key.length() + 1;
        }

        return thrust::device_vector<char>(host_data);
    }
};

// CUDA 内核函数
__global__ void compute_hashes_gpu(const char* keys, uint64_t* hashes, int count) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= count) return;

    // 每个线程计算一个键的哈希值
    const char* key = keys + get_key_offset(idx);
    hashes[idx] = cuda_hash_function(key);
}

__global__ void parallel_insert_gpu(const uint64_t* hashes,
                                   const char* keys,
                                   const char* values,
                                   int count) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= count) return;

    // 每个线程插入一个键值对
    uint64_t hash = hashes[idx];
    const char* key = keys + get_key_offset(idx);
    const char* value = values + get_value_offset(idx);

    // 使用原子操作进行线程安全的插入
    insert_to_hashtable(hash, key, value);
}

__device__ uint64_t cuda_hash_function(const char* key) {
    // GPU 优化的哈希函数
    uint64_t hash = 14695981039346656037ULL; // FNV offset basis

    while (*key) {
        hash ^= (uint64_t)*key++;
        hash *= 1099511628211ULL; // FNV prime
    }

    return hash;
}

// GPU 内存池管理器
class CUDAMemoryPool {
private:
    struct MemoryBlock {
        void* ptr;
        size_t size;
        bool is_free;
    };

    std::vector<MemoryBlock> memory_blocks;
    void* base_ptr;
    size_t total_size;

public:
    CUDAMemoryPool(size_t size) : total_size(size) {
        cudaMalloc(&base_ptr, size);

        // 初始化为一个大的空闲块
        memory_blocks.push_back({base_ptr, size, true});
    }

    void* allocate(size_t size) {
        // 查找合适的空闲块
        for (auto& block : memory_blocks) {
            if (block.is_free && block.size >= size) {
                // 分割块
                if (block.size > size) {
                    MemoryBlock new_block = {
                        static_cast<char*>(block.ptr) + size,
                        block.size - size,
                        true
                    };
                    memory_blocks.push_back(new_block);
                }

                block.size = size;
                block.is_free = false;
                return block.ptr;
            }
        }

        return nullptr; // 分配失败
    }

    void deallocate(void* ptr) {
        // 标记块为空闲并尝试合并相邻块
        for (auto& block : memory_blocks) {
            if (block.ptr == ptr) {
                block.is_free = true;
                merge_adjacent_blocks();
                break;
            }
        }
    }

private:
    void merge_adjacent_blocks() {
        // 合并相邻的空闲块
        std::sort(memory_blocks.begin(), memory_blocks.end(),
                 [](const MemoryBlock& a, const MemoryBlock& b) {
                     return a.ptr < b.ptr;
                 });

        for (size_t i = 0; i < memory_blocks.size() - 1; ) {
            if (memory_blocks[i].is_free && memory_blocks[i + 1].is_free &&
                static_cast<char*>(memory_blocks[i].ptr) + memory_blocks[i].size ==
                memory_blocks[i + 1].ptr) {

                // 合并块
                memory_blocks[i].size += memory_blocks[i + 1].size;
                memory_blocks.erase(memory_blocks.begin() + i + 1);
            } else {
                i++;
            }
        }
    }
};
```

### 10.3 AMD 面试题

#### Q42: 设计基于 AMD ROCm 的 Redis GPU 计算加速
**来源**: AMD ROCm 软件工程部门
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**AMD ROCm Redis 加速实现**:
```cpp
// AMD ROCm 加速的 Redis 实现
#include <hip/hip_runtime.h>
#include <rocprim/rocprim.hpp>
#include <rccl/rccl.h>

class ROCmAcceleratedRedis {
private:
    hipStream_t compute_stream;
    hipStream_t memory_stream;
    void* gpu_memory_pool;
    size_t gpu_memory_size;

    // ROCm 特定的优化
    bool use_infinity_fabric;
    bool use_hbm_memory;

public:
    ROCmAcceleratedRedis() {
        // 初始化 ROCm 运行时
        hipError_t err = hipInit(0);
        if (err != hipSuccess) {
            throw std::runtime_error("Failed to initialize ROCm");
        }

        // 检测 AMD GPU 特性
        detect_amd_gpu_features();

        // 创建 HIP 流
        hipStreamCreate(&compute_stream);
        hipStreamCreate(&memory_stream);

        // 分配 GPU 内存
        allocate_gpu_memory();
    }

    // AMD GPU 加速的并行哈希计算
    void parallel_hash_computation(const std::vector<std::string>& keys,
                                  std::vector<uint64_t>& hashes) {

        // 1. 准备 GPU 数据
        size_t total_key_size = calculate_total_key_size(keys);
        char* gpu_keys;
        hipMalloc(&gpu_keys, total_key_size);

        uint64_t* gpu_hashes;
        hipMalloc(&gpu_hashes, keys.size() * sizeof(uint64_t));

        // 2. 异步传输数据到 GPU
        copy_keys_to_gpu(keys, gpu_keys, total_key_size);

        // 3. 启动 AMD GPU 内核
        int block_size = 256;
        int grid_size = (keys.size() + block_size - 1) / block_size;

        hipLaunchKernelGGL(amd_parallel_hash_kernel,
                          dim3(grid_size), dim3(block_size),
                          0, compute_stream,
                          gpu_keys, gpu_hashes, keys.size());

        // 4. 传输结果回 CPU
        hashes.resize(keys.size());
        hipMemcpyAsync(hashes.data(), gpu_hashes,
                      keys.size() * sizeof(uint64_t),
                      hipMemcpyDeviceToHost, memory_stream);

        // 5. 同步等待完成
        hipStreamSynchronize(compute_stream);
        hipStreamSynchronize(memory_stream);

        // 清理 GPU 内存
        hipFree(gpu_keys);
        hipFree(gpu_hashes);
    }

    // 使用 AMD Infinity Fabric 的多 GPU 分布式缓存
    void setup_multi_gpu_cache() {
        if (!use_infinity_fabric) return;

        int device_count;
        hipGetDeviceCount(&device_count);

        // 为每个 GPU 设置缓存分片
        for (int i = 0; i < device_count; i++) {
            hipSetDevice(i);
            setup_gpu_cache_shard(i, device_count);
        }

        // 配置 GPU 间通信
        setup_infinity_fabric_communication();
    }

private:
    void detect_amd_gpu_features() {
        hipDeviceProp_t prop;
        hipGetDeviceProperties(&prop, 0);

        // 检测是否支持 Infinity Fabric
        use_infinity_fabric = (prop.major >= 9); // RDNA2+ 架构

        // 检测 HBM 内存
        use_hbm_memory = (prop.totalGlobalMem > 16ULL * 1024 * 1024 * 1024); // >16GB

        printf("AMD GPU: %s\n", prop.name);
        printf("Infinity Fabric: %s\n", use_infinity_fabric ? "Yes" : "No");
        printf("HBM Memory: %s\n", use_hbm_memory ? "Yes" : "No");
    }

    void setup_gpu_cache_shard(int gpu_id, int total_gpus) {
        // 为每个 GPU 设置缓存分片
        size_t shard_size = gpu_memory_size / total_gpus;

        void* shard_memory;
        hipMalloc(&shard_memory, shard_size);

        // 初始化分片哈希表
        initialize_shard_hashtable(gpu_id, shard_memory, shard_size);
    }

    void setup_infinity_fabric_communication() {
        // 配置 AMD Infinity Fabric 进行 GPU 间通信
        // 使用 RCCL (ROCm Communication Collectives Library)

        ncclUniqueId id;
        ncclGetUniqueId(&id);

        int device_count;
        hipGetDeviceCount(&device_count);

        ncclComm_t* comms = new ncclComm_t[device_count];

        // 初始化 NCCL 通信器
        ncclCommInitAll(comms, device_count, nullptr);

        // 设置 GPU 间数据同步
        setup_cross_gpu_synchronization(comms, device_count);
    }
};

// AMD GPU 内核函数
__global__ void amd_parallel_hash_kernel(const char* keys,
                                        uint64_t* hashes,
                                        int count) {
    int idx = hipBlockIdx_x * hipBlockDim_x + hipThreadIdx_x;
    if (idx >= count) return;

    // 使用 AMD GPU 优化的哈希算法
    const char* key = get_key_at_index(keys, idx);
    hashes[idx] = amd_optimized_hash(key);
}

__device__ uint64_t amd_optimized_hash(const char* key) {
    // 针对 AMD RDNA 架构优化的哈希函数
    uint64_t hash = 0xcbf29ce484222325ULL; // FNV-1a offset basis

    // 使用 AMD GPU 的向量指令
    while (*key) {
        hash ^= (uint64_t)*key++;
        hash *= 0x100000001b3ULL; // FNV-1a prime
    }

    return hash;
}

// AMD 特定的内存优化
class AMDMemoryOptimizer {
public:
    // 优化 HBM 内存访问模式
    void optimize_hbm_access() {
        // 配置内存访问模式以最大化 HBM 带宽
        configure_memory_coalescing();
        setup_memory_prefetching();
    }

    // 利用 AMD Infinity Cache
    void setup_infinity_cache() {
        // 配置 L3 缓存 (Infinity Cache) 使用策略
        set_cache_policy(CACHE_POLICY_WRITE_BACK);
        enable_cache_prefetch();
    }

private:
    void configure_memory_coalescing() {
        // 确保内存访问是合并的，以最大化带宽利用率
        // AMD GPU 偏好 64 字节对齐的访问
    }

    void setup_memory_prefetching() {
        // 配置硬件预取器
        // AMD RDNA 架构的预取优化
    }
};

// AMD ROCm 性能分析集成
class ROCmProfiler {
public:
    void start_profiling() {
        // 使用 ROCProfiler 进行性能分析
        rocprofiler_start();
    }

    void collect_metrics() {
        // 收集 GPU 性能指标
        collect_compute_unit_utilization();
        collect_memory_bandwidth_usage();
        collect_cache_hit_rates();
    }

private:
    void collect_compute_unit_utilization() {
        // 收集计算单元利用率
        // AMD GPU 特有的 CU (Compute Unit) 指标
    }

    void collect_memory_bandwidth_usage() {
        // 收集 HBM 内存带宽使用情况
        // 监控 VRAM 访问模式
    }

    void collect_cache_hit_rates() {
        // 收集 Infinity Cache 命中率
        // L1/L2/L3 缓存性能指标
    }
};
```

#### Q43: 如何在异构计算环境中优化 Redis 性能？
**来源**: AMD 异构计算部门
**难度**: ⭐⭐⭐⭐

**答案**:

**异构计算 Redis 优化**:
```cpp
class HeterogeneousRedisOptimizer {
private:
    // 计算资源管理
    std::vector<ComputeDevice> available_devices;
    TaskScheduler scheduler;

public:
    HeterogeneousRedisOptimizer() {
        // 检测所有可用的计算设备
        detect_compute_devices();

        // 初始化任务调度器
        scheduler.initialize(available_devices);
    }

    void detect_compute_devices() {
        // 检测 CPU
        detect_cpu_capabilities();

        // 检测 AMD GPU
        detect_amd_gpus();

        // 检测 FPGA (如果可用)
        detect_fpgas();

        // 检测其他加速器
        detect_other_accelerators();
    }

    // 智能任务分配
    void process_redis_operations(const std::vector<RedisOperation>& operations) {
        // 1. 分析操作类型和复杂度
        auto operation_analysis = analyze_operations(operations);

        // 2. 根据设备特性分配任务
        for (const auto& op : operations) {
            ComputeDevice* best_device = scheduler.select_best_device(op);

            switch (best_device->type) {
                case DeviceType::CPU:
                    execute_on_cpu(op);
                    break;
                case DeviceType::AMD_GPU:
                    execute_on_amd_gpu(op);
                    break;
                case DeviceType::FPGA:
                    execute_on_fpga(op);
                    break;
            }
        }
    }

private:
    void detect_cpu_capabilities() {
        ComputeDevice cpu_device;
        cpu_device.type = DeviceType::CPU;
        cpu_device.name = get_cpu_name();

        // 检测 CPU 特性
        cpu_device.features.avx512 = check_avx512_support();
        cpu_device.features.avx2 = check_avx2_support();
        cpu_device.features.sse4 = check_sse4_support();

        // CPU 适合的操作类型
        cpu_device.optimal_operations = {
            OperationType::COMPLEX_LOGIC,
            OperationType::SEQUENTIAL_PROCESSING,
            OperationType::SMALL_DATA_OPERATIONS
        };

        available_devices.push_back(cpu_device);
    }

    void detect_amd_gpus() {
        int gpu_count;
        hipGetDeviceCount(&gpu_count);

        for (int i = 0; i < gpu_count; i++) {
            hipDeviceProp_t prop;
            hipGetDeviceProperties(&prop, i);

            ComputeDevice gpu_device;
            gpu_device.type = DeviceType::AMD_GPU;
            gpu_device.name = prop.name;
            gpu_device.device_id = i;

            // GPU 特性
            gpu_device.features.compute_units = prop.multiProcessorCount;
            gpu_device.features.memory_size = prop.totalGlobalMem;
            gpu_device.features.memory_bandwidth = calculate_memory_bandwidth(prop);

            // GPU 适合的操作类型
            gpu_device.optimal_operations = {
                OperationType::PARALLEL_HASH_COMPUTATION,
                OperationType::BULK_DATA_PROCESSING,
                OperationType::PATTERN_MATCHING,
                OperationType::SORTING_OPERATIONS
            };

            available_devices.push_back(gpu_device);
        }
    }

    void execute_on_cpu(const RedisOperation& op) {
        // CPU 执行路径 - 适合复杂逻辑和小数据量操作
        switch (op.type) {
            case OperationType::COMPLEX_LOGIC:
                cpu_complex_logic_handler(op);
                break;
            case OperationType::SEQUENTIAL_PROCESSING:
                cpu_sequential_handler(op);
                break;
            default:
                cpu_default_handler(op);
        }
    }

    void execute_on_amd_gpu(const RedisOperation& op) {
        // AMD GPU 执行路径 - 适合并行计算
        switch (op.type) {
            case OperationType::PARALLEL_HASH_COMPUTATION:
                gpu_parallel_hash_handler(op);
                break;
            case OperationType::BULK_DATA_PROCESSING:
                gpu_bulk_processing_handler(op);
                break;
            case OperationType::SORTING_OPERATIONS:
                gpu_sorting_handler(op);
                break;
            default:
                gpu_default_handler(op);
        }
    }

    void execute_on_fpga(const RedisOperation& op) {
        // FPGA 执行路径 - 适合特定算法加速
        fpga_accelerated_handler(op);
    }
};

// 任务调度器
class TaskScheduler {
private:
    std::vector<ComputeDevice> devices;
    PerformanceModel performance_model;

public:
    ComputeDevice* select_best_device(const RedisOperation& op) {
        ComputeDevice* best_device = nullptr;
        double best_score = -1.0;

        for (auto& device : devices) {
            double score = calculate_device_score(device, op);
            if (score > best_score) {
                best_score = score;
                best_device = &device;
            }
        }

        return best_device;
    }

private:
    double calculate_device_score(const ComputeDevice& device,
                                 const RedisOperation& op) {
        double score = 0.0;

        // 基于操作类型的适配性评分
        if (std::find(device.optimal_operations.begin(),
                     device.optimal_operations.end(),
                     op.type) != device.optimal_operations.end()) {
            score += 50.0;
        }

        // 基于当前负载的评分
        double load_factor = 1.0 - device.current_utilization;
        score += load_factor * 30.0;

        // 基于数据传输成本的评分
        double transfer_cost = calculate_transfer_cost(device, op);
        score -= transfer_cost * 20.0;

        return score;
    }

    double calculate_transfer_cost(const ComputeDevice& device,
                                  const RedisOperation& op) {
        // 计算数据传输成本
        if (device.type == DeviceType::CPU) {
            return 0.0; // CPU 无传输成本
        }

        // GPU/FPGA 需要考虑 PCIe 传输成本
        size_t data_size = op.data_size;
        double transfer_time = data_size / device.features.pcie_bandwidth;

        return transfer_time;
    }
};
```

---

## 第十一部分：半导体芯片公司面试题

### 11.1 高通 (Qualcomm) 面试题

#### Q44: 设计移动设备上的轻量级 Redis 缓存系统
**来源**: 高通移动平台软件部门
**难度**: ⭐⭐⭐⭐⭐

**问题**: 为 Snapdragon 移动平台设计一个轻量级的 Redis 缓存系统，需要考虑功耗、内存限制和网络连接不稳定等移动设备特有的约束。

**答案**:

**移动优化 Redis 架构**:
```cpp
// 高通移动平台优化的 Redis 实现
#include <android/log.h>
#include <sys/mman.h>
#include <unistd.h>

class QualcommMobileRedis {
private:
    // 移动设备特有的优化参数
    PowerManager power_manager;
    NetworkManager network_manager;
    MemoryManager memory_manager;

    // 自适应配置
    bool is_low_power_mode;
    bool is_background_mode;
    NetworkType current_network_type;

public:
    QualcommMobileRedis() {
        // 初始化移动平台特有的管理器
        initialize_mobile_optimizations();

        // 注册系统事件监听
        register_system_callbacks();
    }

    // 功耗感知的缓存策略
    void power_aware_caching() {
        PowerState current_state = power_manager.get_current_state();

        switch (current_state) {
            case PowerState::HIGH_PERFORMANCE:
                configure_aggressive_caching();
                break;

            case PowerState::BALANCED:
                configure_balanced_caching();
                break;

            case PowerState::POWER_SAVER:
                configure_minimal_caching();
                break;

            case PowerState::ULTRA_POWER_SAVER:
                disable_background_operations();
                break;
        }
    }

    // 网络自适应数据同步
    void adaptive_network_sync() {
        NetworkType network = network_manager.get_current_network();
        NetworkQuality quality = network_manager.get_network_quality();

        SyncStrategy strategy = determine_sync_strategy(network, quality);

        switch (strategy) {
            case SyncStrategy::IMMEDIATE:
                perform_immediate_sync();
                break;

            case SyncStrategy::BATCHED:
                schedule_batched_sync();
                break;

            case SyncStrategy::DEFERRED:
                defer_sync_until_wifi();
                break;

            case SyncStrategy::OFFLINE:
                enable_offline_mode();
                break;
        }
    }

    // 内存压力感知的数据管理
    void memory_pressure_handling() {
        MemoryPressure pressure = memory_manager.get_memory_pressure();

        if (pressure >= MemoryPressure::HIGH) {
            // 激进的内存回收
            aggressive_memory_cleanup();

            // 降低缓存大小
            reduce_cache_size(0.5); // 减少50%

            // 启用压缩
            enable_data_compression();
        }
        else if (pressure >= MemoryPressure::MEDIUM) {
            // 温和的内存回收
            moderate_memory_cleanup();

            // 减少预取
            reduce_prefetch_size();
        }
    }

private:
    void configure_aggressive_caching() {
        // 高性能模式配置
        set_cache_size(128 * 1024 * 1024); // 128MB
        set_prefetch_enabled(true);
        set_background_sync_enabled(true);
        set_compression_enabled(false); // 优先性能
    }

    void configure_minimal_caching() {
        // 省电模式配置
        set_cache_size(16 * 1024 * 1024); // 16MB
        set_prefetch_enabled(false);
        set_background_sync_enabled(false);
        set_compression_enabled(true); // 节省内存

        // 启用更激进的过期策略
        set_ttl_multiplier(0.5);
    }

    SyncStrategy determine_sync_strategy(NetworkType network, NetworkQuality quality) {
        // 网络类型和质量决定同步策略
        if (network == NetworkType::WIFI && quality >= NetworkQuality::GOOD) {
            return SyncStrategy::IMMEDIATE;
        }
        else if (network == NetworkType::LTE && quality >= NetworkQuality::FAIR) {
            return SyncStrategy::BATCHED;
        }
        else if (network == NetworkType::CELLULAR_3G) {
            return SyncStrategy::DEFERRED;
        }
        else {
            return SyncStrategy::OFFLINE;
        }
    }

    void register_system_callbacks() {
        // 注册 Android 系统回调
        register_power_state_callback([this](PowerState state) {
            this->on_power_state_changed(state);
        });

        register_network_state_callback([this](NetworkType type) {
            this->on_network_changed(type);
        });

        register_memory_pressure_callback([this](MemoryPressure pressure) {
            this->on_memory_pressure(pressure);
        });
    }
};

// 高通 DSP 加速的数据处理
class QualcommDSPAcceleration {
private:
    // Hexagon DSP 接口
    HexagonDSP dsp_interface;

public:
    // 使用 Hexagon DSP 进行数据压缩
    std::vector<uint8_t> dsp_compress_data(const std::vector<uint8_t>& input) {
        // 将数据传输到 DSP
        DSPBuffer dsp_input = dsp_interface.allocate_buffer(input.size());
        dsp_interface.copy_to_dsp(input.data(), dsp_input, input.size());

        // 在 DSP 上执行压缩算法
        DSPBuffer dsp_output = dsp_interface.execute_compression(dsp_input);

        // 获取压缩结果
        std::vector<uint8_t> compressed_data(dsp_output.size);
        dsp_interface.copy_from_dsp(dsp_output, compressed_data.data());

        // 清理 DSP 资源
        dsp_interface.free_buffer(dsp_input);
        dsp_interface.free_buffer(dsp_output);

        return compressed_data;
    }

    // DSP 加速的哈希计算
    uint64_t dsp_hash_computation(const void* data, size_t length) {
        // 利用 Hexagon DSP 的向量处理能力
        return dsp_interface.compute_hash_vectorized(data, length);
    }
};

// 移动设备电池优化
class MobileBatteryOptimizer {
public:
    // 基于电池状态调整 Redis 行为
    void optimize_for_battery_level(int battery_percentage) {
        if (battery_percentage < 15) {
            // 极低电量模式
            enable_ultra_power_saving();
        }
        else if (battery_percentage < 30) {
            // 低电量模式
            enable_power_saving();
        }
        else if (battery_percentage > 80) {
            // 高电量模式
            enable_performance_mode();
        }
    }

private:
    void enable_ultra_power_saving() {
        // 禁用所有后台操作
        disable_background_sync();
        disable_prefetching();

        // 最小化内存使用
        set_minimal_cache_size();

        // 降低 CPU 频率请求
        request_low_cpu_frequency();
    }

    void enable_performance_mode() {
        // 启用所有优化功能
        enable_background_sync();
        enable_prefetching();

        // 增大缓存大小
        set_optimal_cache_size();

        // 请求高 CPU 频率
        request_high_cpu_frequency();
    }
};
```

#### Q45: 如何在 5G 网络环境下优化 Redis 性能？
**来源**: 高通 5G 技术部门
**难度**: ⭐⭐⭐⭐

**答案**:

**5G 网络优化 Redis**:
```cpp
class FiveGOptimizedRedis {
private:
    FiveGNetworkManager network_manager;
    EdgeComputingManager edge_manager;
    LatencyOptimizer latency_optimizer;

public:
    // 5G 网络切片感知的缓存策略
    void network_slice_aware_caching() {
        NetworkSlice current_slice = network_manager.get_current_slice();

        switch (current_slice.type) {
            case SliceType::ENHANCED_MOBILE_BROADBAND:
                configure_high_throughput_caching();
                break;

            case SliceType::ULTRA_RELIABLE_LOW_LATENCY:
                configure_low_latency_caching();
                break;

            case SliceType::MASSIVE_IOT:
                configure_iot_optimized_caching();
                break;
        }
    }

    // 边缘计算集成
    void integrate_with_edge_computing() {
        std::vector<EdgeNode> available_edges = edge_manager.discover_edge_nodes();

        for (const auto& edge : available_edges) {
            if (edge.latency < 5) { // 5ms 以内
                // 将热点数据迁移到边缘节点
                migrate_hot_data_to_edge(edge);

                // 配置边缘缓存策略
                configure_edge_cache_policy(edge);
            }
        }
    }

private:
    void configure_low_latency_caching() {
        // 超低延迟配置
        set_max_latency_target(1); // 1ms 目标
        enable_predictive_caching();
        use_memory_only_storage();
        disable_compression(); // 减少 CPU 开销
    }

    void configure_iot_optimized_caching() {
        // 物联网优化配置
        enable_data_aggregation();
        set_batch_processing_mode();
        optimize_for_small_payloads();
        enable_edge_preprocessing();
    }
};
```

### 11.2 ARM Holdings 面试题

#### Q46: 设计适用于 ARM 架构的高效 Redis 实现
**来源**: ARM 软件工程部门
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**ARM 架构优化 Redis**:
```cpp
// ARM 架构特定优化的 Redis 实现
#include <arm_neon.h>
#include <arm_acle.h>

class ARMOptimizedRedis {
private:
    // ARM 特定的优化标志
    bool has_neon_support;
    bool has_crypto_extensions;
    bool has_crc32_support;
    bool is_big_little_architecture;

public:
    ARMOptimizedRedis() {
        // 检测 ARM CPU 特性
        detect_arm_features();

        // 根据架构配置优化策略
        configure_arm_optimizations();
    }

    // 使用 ARM NEON 优化的内存操作
    void neon_optimized_memcpy(const void* src, void* dst, size_t size) {
        if (!has_neon_support || size < 64) {
            // 回退到标准实现
            memcpy(dst, src, size);
            return;
        }

        const uint8_t* src_ptr = static_cast<const uint8_t*>(src);
        uint8_t* dst_ptr = static_cast<uint8_t*>(dst);

        // 使用 NEON 向量指令进行批量复制
        size_t vector_count = size / 64;
        for (size_t i = 0; i < vector_count; i++) {
            // 加载 64 字节 (4 个 128 位向量)
            uint8x16x4_t data = vld1q_u8_x4(src_ptr + i * 64);

            // 存储 64 字节
            vst1q_u8_x4(dst_ptr + i * 64, data);
        }

        // 处理剩余字节
        size_t remaining = size % 64;
        if (remaining > 0) {
            memcpy(dst_ptr + vector_count * 64,
                   src_ptr + vector_count * 64,
                   remaining);
        }
    }

    // ARM Crypto Extensions 加速的哈希计算
    uint32_t arm_crc32_hash(const void* data, size_t length) {
        if (!has_crc32_support) {
            return standard_hash(data, length);
        }

        const uint8_t* ptr = static_cast<const uint8_t*>(data);
        uint32_t crc = 0;

        // 使用 ARM CRC32 指令
        size_t word_count = length / 4;
        for (size_t i = 0; i < word_count; i++) {
            uint32_t word = *reinterpret_cast<const uint32_t*>(ptr + i * 4);
            crc = __crc32w(crc, word);
        }

        // 处理剩余字节
        size_t remaining = length % 4;
        for (size_t i = 0; i < remaining; i++) {
            crc = __crc32b(crc, ptr[word_count * 4 + i]);
        }

        return crc;
    }

    // big.LITTLE 架构感知的任务调度
    void big_little_task_scheduling() {
        if (!is_big_little_architecture) return;

        CPUCluster big_cluster = get_big_cpu_cluster();
        CPUCluster little_cluster = get_little_cpu_cluster();

        // 将计算密集型任务分配给大核
        schedule_compute_intensive_tasks(big_cluster);

        // 将 I/O 密集型任务分配给小核
        schedule_io_intensive_tasks(little_cluster);
    }

private:
    void detect_arm_features() {
        // 检测 NEON 支持
        has_neon_support = check_neon_support();

        // 检测加密扩展
        has_crypto_extensions = check_crypto_extensions();

        // 检测 CRC32 支持
        has_crc32_support = check_crc32_support();

        // 检测 big.LITTLE 架构
        is_big_little_architecture = check_big_little_architecture();
    }

    void configure_arm_optimizations() {
        if (has_neon_support) {
            enable_neon_optimizations();
        }

        if (has_crypto_extensions) {
            enable_crypto_acceleration();
        }

        if (is_big_little_architecture) {
            setup_heterogeneous_scheduling();
        }
    }

    void schedule_compute_intensive_tasks(const CPUCluster& cluster) {
        // 哈希计算、数据压缩等任务
        TaskType compute_tasks[] = {
            TaskType::HASH_COMPUTATION,
            TaskType::DATA_COMPRESSION,
            TaskType::ENCRYPTION_DECRYPTION
        };

        for (auto task_type : compute_tasks) {
            assign_task_to_cluster(task_type, cluster);
        }
    }

    void schedule_io_intensive_tasks(const CPUCluster& cluster) {
        // 网络 I/O、磁盘 I/O 等任务
        TaskType io_tasks[] = {
            TaskType::NETWORK_IO,
            TaskType::DISK_IO,
            TaskType::MEMORY_MANAGEMENT
        };

        for (auto task_type : io_tasks) {
            assign_task_to_cluster(task_type, cluster);
        }
    }
};

// ARM TrustZone 安全扩展集成
class ARMTrustZoneRedis {
private:
    TrustZoneInterface tz_interface;

public:
    // 在安全世界中存储敏感数据
    bool secure_set(const std::string& key, const std::string& value) {
        // 切换到安全世界
        if (!tz_interface.switch_to_secure_world()) {
            return false;
        }

        // 在安全内存中存储数据
        bool result = tz_interface.secure_store(key, value);

        // 切换回非安全世界
        tz_interface.switch_to_normal_world();

        return result;
    }

    // 从安全世界获取敏感数据
    std::string secure_get(const std::string& key) {
        if (!tz_interface.switch_to_secure_world()) {
            return "";
        }

        std::string value = tz_interface.secure_retrieve(key);

        tz_interface.switch_to_normal_world();

        return value;
    }
};
```

### 11.3 博通 (Broadcom) 面试题

#### Q47: 设计网络设备中的 Redis 缓存加速系统
**来源**: 博通网络芯片部门
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**网络设备 Redis 加速**:
```cpp
// 博通网络芯片加速的 Redis 实现
#include <bcm/bcm.h>
#include <bcm/switch.h>

class BroadcomNetworkRedis {
private:
    // 博通网络芯片接口
    BCMSwitchInterface switch_interface;
    PacketProcessorUnit ppu;
    NetworkProcessorUnit npu;

    // 硬件加速表
    HardwareHashTable hw_hash_table;
    ContentAddressableMemory cam;

public:
    BroadcomNetworkRedis() {
        // 初始化博通芯片
        initialize_broadcom_hardware();

        // 配置硬件加速
        setup_hardware_acceleration();
    }

    // 硬件加速的包处理
    void hardware_accelerated_packet_processing() {
        // 配置包处理流水线
        PacketPipeline pipeline;

        // 阶段1: 包解析
        pipeline.add_stage(PacketStage::PARSING, [this](Packet& pkt) {
            return this->parse_packet_headers(pkt);
        });

        // 阶段2: 缓存查找
        pipeline.add_stage(PacketStage::CACHE_LOOKUP, [this](Packet& pkt) {
            return this->hardware_cache_lookup(pkt);
        });

        // 阶段3: 数据处理
        pipeline.add_stage(PacketStage::DATA_PROCESSING, [this](Packet& pkt) {
            return this->process_redis_operation(pkt);
        });

        // 阶段4: 响应生成
        pipeline.add_stage(PacketStage::RESPONSE_GENERATION, [this](Packet& pkt) {
            return this->generate_response_packet(pkt);
        });

        // 启动硬件流水线
        ppu.configure_pipeline(pipeline);
        ppu.start_processing();
    }

    // 使用 CAM 进行快速键查找
    CacheEntry* cam_key_lookup(const std::string& key) {
        // 将键转换为 CAM 查找格式
        CAMEntry cam_entry = format_key_for_cam(key);

        // 硬件 CAM 查找 (单周期完成)
        CAMResult result = cam.lookup(cam_entry);

        if (result.hit) {
            // 从关联的内存获取完整数据
            return retrieve_cache_entry(result.index);
        }

        return nullptr;
    }

    // 网络流量感知的缓存策略
    void traffic_aware_caching() {
        NetworkTrafficStats stats = switch_interface.get_traffic_stats();

        // 根据网络负载调整缓存策略
        if (stats.utilization > 0.8) {
            // 高负载: 激进缓存
            enable_aggressive_caching();
            increase_cache_size();
        }
        else if (stats.utilization < 0.3) {
            // 低负载: 节能模式
            enable_power_saving_mode();
            reduce_cache_size();
        }

        // 根据流量模式优化
        optimize_for_traffic_pattern(stats.pattern);
    }

private:
    void initialize_broadcom_hardware() {
        // 初始化博通交换芯片
        int rv = bcm_switch_init();
        if (rv != BCM_E_NONE) {
            throw std::runtime_error("Failed to initialize Broadcom switch");
        }

        // 配置包处理单元
        configure_packet_processor();

        // 配置网络处理单元
        configure_network_processor();
    }

    void setup_hardware_acceleration() {
        // 配置硬件哈希表
        hw_hash_table.configure(
            .size = 1024 * 1024,  // 1M 条目
            .hash_function = HashFunction::CRC32,
            .collision_resolution = CollisionResolution::CHAINING
        );

        // 配置 CAM
        cam.configure(
            .size = 64 * 1024,    // 64K 条目
            .key_width = 128,     // 128 位键
            .associativity = 4    // 4 路关联
        );
    }

    bool hardware_cache_lookup(Packet& packet) {
        // 从包中提取 Redis 键
        std::string key = extract_redis_key(packet);

        // 硬件加速查找
        CacheEntry* entry = cam_key_lookup(key);

        if (entry) {
            // 缓存命中: 直接从硬件返回数据
            populate_response_from_cache(packet, entry);
            return true;
        }

        // 缓存未命中: 需要进一步处理
        return false;
    }

    void optimize_for_traffic_pattern(TrafficPattern pattern) {
        switch (pattern) {
            case TrafficPattern::BURSTY:
                configure_burst_handling();
                break;

            case TrafficPattern::STEADY:
                configure_steady_state_optimization();
                break;

            case TrafficPattern::PERIODIC:
                configure_periodic_optimization();
                break;
        }
    }

    void configure_burst_handling() {
        // 突发流量处理
        increase_buffer_sizes();
        enable_traffic_shaping();
        configure_priority_queuing();
    }
};

// 博通 StrataXGS 芯片特定优化
class StrataXGSOptimization {
public:
    // 利用 StrataXGS 的表查找引擎
    void configure_table_lookup_engine() {
        // 配置 L2 表用于 MAC 地址查找
        configure_l2_table();

        // 配置 L3 表用于 IP 路由
        configure_l3_table();

        // 配置自定义表用于 Redis 键查找
        configure_custom_redis_table();
    }

private:
    void configure_custom_redis_table() {
        // 创建自定义表用于 Redis 键到值的映射
        TableConfig config = {
            .table_id = CUSTOM_REDIS_TABLE,
            .key_size = 256,      // 256 字节键
            .value_size = 1024,   // 1KB 值
            .table_size = 1024 * 1024, // 1M 条目
            .hash_mode = HASH_MODE_CRC32
        };

        create_custom_table(config);
    }
};
```

### 11.4 英飞凌 (Infineon) 面试题

#### Q48: 设计汽车电子系统中的 Redis 缓存方案
**来源**: 英飞凌汽车电子部门
**难度**: ⭐⭐⭐⭐⭐

**答案**:

**汽车级 Redis 实现**:
```cpp
// 英飞凌汽车电子优化的 Redis 实现
#include <automotive_safety.h>
#include <iso26262.h>

class InfineonAutomotiveRedis {
private:
    // 汽车安全等级
    SafetyIntegrityLevel sil_level;

    // 汽车电子特有的约束
    TemperatureRange operating_temp;
    VibrationTolerance vibration_spec;
    EMCCompliance emc_compliance;

    // 功能安全管理
    SafetyManager safety_manager;
    DiagnosticManager diagnostic_manager;

public:
    InfineonAutomotiveRedis(SafetyIntegrityLevel sil = SIL_B)
        : sil_level(sil) {

        // 初始化汽车级安全机制
        initialize_automotive_safety();

        // 配置温度和振动容忍度
        configure_environmental_tolerance();

        // 启动诊断和监控
        start_safety_monitoring();
    }

    // 功能安全的数据存储
    SafetyResult safety_critical_set(const std::string& key,
                                   const std::string& value,
                                   SafetyCriticality criticality) {

        // 1. 安全性检查
        if (!safety_manager.validate_operation(Operation::WRITE, criticality)) {
            return SafetyResult::SAFETY_VIOLATION;
        }

        // 2. 数据完整性保护
        std::string protected_value = add_safety_protection(value, criticality);

        // 3. 冗余存储 (根据安全等级)
        if (criticality >= SafetyCriticality::HIGH) {
            // 三重冗余存储
            store_with_triple_redundancy(key, protected_value);
        }
        else if (criticality >= SafetyCriticality::MEDIUM) {
            // 双重冗余存储
            store_with_dual_redundancy(key, protected_value);
        }
        else {
            // 单一存储 + 校验
            store_with_checksum(key, protected_value);
        }

        // 4. 记录安全日志
        safety_manager.log_safety_operation(key, Operation::WRITE, criticality);

        return SafetyResult::SUCCESS;
    }

    // 实时系统优化
    void real_time_optimization() {
        // 确定性内存分配
        setup_deterministic_memory_allocation();

        // 优先级调度
        configure_priority_scheduling();

        // 中断延迟优化
        minimize_interrupt_latency();

        // 缓存预热
        perform_cache_warming();
    }

    // 汽车网络集成 (CAN, LIN, FlexRay, Ethernet)
    void integrate_automotive_networks() {
        // CAN 总线集成
        setup_can_interface();

        // 车载以太网集成
        setup_automotive_ethernet();

        // FlexRay 高速网络
        setup_flexray_interface();

        // LIN 低速网络
        setup_lin_interface();
    }

private:
    void initialize_automotive_safety() {
        // ISO 26262 功能安全标准
        safety_manager.configure_iso26262_compliance(sil_level);

        // ASIL (Automotive Safety Integrity Level) 配置
        configure_asil_requirements();

        // 故障检测和处理
        setup_fault_detection();
    }

    std::string add_safety_protection(const std::string& value,
                                    SafetyCriticality criticality) {
        SafetyProtection protection;

        // 添加 CRC 校验
        uint32_t crc = calculate_automotive_crc(value);
        protection.crc = crc;

        // 添加时间戳
        protection.timestamp = get_automotive_timestamp();

        // 添加序列号
        protection.sequence_number = get_next_sequence_number();

        // 根据安全等级添加额外保护
        if (criticality >= SafetyCriticality::HIGH) {
            // 添加数字签名
            protection.digital_signature = sign_data(value);

            // 添加完整性标记
            protection.integrity_marker = generate_integrity_marker(value);
        }

        // 序列化保护信息
        return serialize_protected_data(value, protection);
    }

    void setup_can_interface() {
        CANInterface can_interface;

        // 配置 CAN 消息过滤
        can_interface.configure_message_filters({
            {0x100, 0x7FF, MessageType::REDIS_REQUEST},
            {0x200, 0x7FF, MessageType::REDIS_RESPONSE},
            {0x300, 0x7FF, MessageType::REDIS_SYNC}
        });

        // 设置消息处理回调
        can_interface.set_message_handler([this](const CANMessage& msg) {
            this->handle_can_redis_message(msg);
        });

        // 启动 CAN 接口
        can_interface.start();
    }

    void handle_can_redis_message(const CANMessage& msg) {
        switch (msg.type) {
            case MessageType::REDIS_REQUEST:
                process_can_redis_request(msg);
                break;

            case MessageType::REDIS_RESPONSE:
                process_can_redis_response(msg);
                break;

            case MessageType::REDIS_SYNC:
                process_can_redis_sync(msg);
                break;
        }
    }

    void setup_deterministic_memory_allocation() {
        // 预分配内存池
        MemoryPool cache_pool(64 * 1024 * 1024); // 64MB
        MemoryPool network_pool(16 * 1024 * 1024); // 16MB
        MemoryPool safety_pool(8 * 1024 * 1024);   // 8MB

        // 禁用动态内存分配
        disable_dynamic_allocation();

        // 配置内存保护
        configure_memory_protection();
    }
};

// 英飞凌安全芯片集成
class InfineonSecurityChip {
private:
    TPMInterface tpm_interface;
    HSMInterface hsm_interface;

public:
    // 使用 TPM 进行密钥管理
    bool generate_redis_encryption_key(const std::string& key_id) {
        // 在 TPM 中生成密钥
        TPMKeyHandle key_handle = tpm_interface.generate_key(
            TPMKeyType::AES_256,
            TPMKeyUsage::ENCRYPTION,
            key_id
        );

        if (key_handle.is_valid()) {
            // 密钥生成成功，存储句柄
            store_key_handle(key_id, key_handle);
            return true;
        }

        return false;
    }

    // 使用 HSM 进行安全计算
    std::vector<uint8_t> hsm_encrypt_data(const std::vector<uint8_t>& data,
                                         const std::string& key_id) {
        // 在 HSM 中执行加密操作
        return hsm_interface.encrypt(data, key_id);
    }

    // 安全启动验证
    bool verify_secure_boot() {
        // 验证 Redis 二进制文件的完整性
        return tpm_interface.verify_binary_integrity("redis-server");
    }
};
```

### 11.5 恩智浦 (NXP) 面试题

#### Q49: 设计物联网边缘设备的 Redis 轻量级实现
**来源**: 恩智浦物联网部门
**难度**: ⭐⭐⭐⭐

**答案**:

**物联网边缘 Redis**:
```cpp
// 恩智浦物联网优化的 Redis 实现
#include <nxp_iot_sdk.h>
#include <lwm2m.h>
#include <coap.h>

class NXPIoTRedis {
private:
    // 物联网设备约束
    MemoryConstraint memory_limit;
    PowerConstraint power_budget;
    NetworkConstraint network_limit;

    // NXP 特有的硬件加速
    CryptoAccelerator crypto_hw;
    RandomNumberGenerator rng_hw;

public:
    NXPIoTRedis(const IoTDeviceProfile& profile) {
        // 根据设备配置文件初始化
        configure_for_device_profile(profile);

        // 初始化硬件加速器
        initialize_nxp_hardware();

        // 配置低功耗模式
        setup_power_management();
    }

    // 超轻量级数据结构
    class UltraLightHashTable {
    private:
        struct CompactEntry {
            uint16_t key_hash;    // 16位哈希值
            uint8_t key_len;      // 键长度
            uint8_t value_len;    // 值长度
            uint32_t data_offset; // 数据偏移
        };

        std::vector<CompactEntry> entries;
        std::vector<uint8_t> data_storage;

    public:
        bool set(const std::string& key, const std::string& value) {
            // 计算紧凑哈希
            uint16_t hash = calculate_compact_hash(key);

            // 查找现有条目
            auto it = find_entry(hash, key);
            if (it != entries.end()) {
                // 更新现有条目
                return update_entry(*it, value);
            }

            // 添加新条目
            return add_new_entry(hash, key, value);
        }

        std::string get(const std::string& key) {
            uint16_t hash = calculate_compact_hash(key);
            auto it = find_entry(hash, key);

            if (it != entries.end()) {
                return extract_value(*it);
            }

            return "";
        }

    private:
        uint16_t calculate_compact_hash(const std::string& key) {
            // 使用 NXP 硬件 CRC 计算
            return crypto_hw.calculate_crc16(key.data(), key.length());
        }
    };

    // CoAP 协议集成
    void setup_coap_interface() {
        CoAPServer coap_server;

        // 注册 Redis 资源
        coap_server.register_resource("/redis/get", [this](const CoAPRequest& req) {
            return this->handle_coap_get(req);
        });

        coap_server.register_resource("/redis/set", [this](const CoAPRequest& req) {
            return this->handle_coap_set(req);
        });

        // 启动 CoAP 服务器
        coap_server.start(5683); // 标准 CoAP 端口
    }

    // LWM2M 设备管理集成
    void setup_lwm2m_management() {
        LWM2MClient lwm2m_client;

        // 注册 Redis 缓存对象
        LWM2MObject redis_object = {
            .object_id = 65001, // 自定义对象 ID
            .name = "Redis Cache",
            .resources = {
                {0, "Cache Size", ResourceType::INTEGER, true},
                {1, "Hit Rate", ResourceType::FLOAT, true},
                {2, "Memory Usage", ResourceType::INTEGER, true},
                {3, "Clear Cache", ResourceType::EXECUTE, false}
            }
        };

        lwm2m_client.register_object(redis_object);

        // 设置资源处理器
        lwm2m_client.set_resource_handler(65001, 3, [this]() {
            this->clear_cache();
        });

        // 连接到 LWM2M 服务器
        lwm2m_client.connect("coap://lwm2m.server.com:5683");
    }

    // 低功耗模式管理
    void power_management() {
        PowerState current_state = get_current_power_state();

        switch (current_state) {
            case PowerState::ACTIVE:
                configure_active_mode();
                break;

            case PowerState::IDLE:
                configure_idle_mode();
                break;

            case PowerState::SLEEP:
                configure_sleep_mode();
                break;

            case PowerState::DEEP_SLEEP:
                configure_deep_sleep_mode();
                break;
        }
    }

private:
    void configure_for_device_profile(const IoTDeviceProfile& profile) {
        // 根据设备类型配置
        switch (profile.device_type) {
            case IoTDeviceType::SENSOR_NODE:
                configure_sensor_node_mode();
                break;

            case IoTDeviceType::GATEWAY:
                configure_gateway_mode();
                break;

            case IoTDeviceType::ACTUATOR:
                configure_actuator_mode();
                break;
        }

        // 内存限制配置
        memory_limit = profile.memory_constraint;
        configure_memory_usage(memory_limit);

        // 功耗预算配置
        power_budget = profile.power_constraint;
        configure_power_usage(power_budget);
    }

    void configure_sensor_node_mode() {
        // 传感器节点优化
        enable_data_aggregation();
        set_minimal_memory_footprint();
        enable_periodic_sleep();
        configure_sensor_data_caching();
    }

    void configure_gateway_mode() {
        // 网关模式优化
        enable_protocol_translation();
        increase_cache_size();
        enable_data_forwarding();
        configure_multi_protocol_support();
    }

    void configure_deep_sleep_mode() {
        // 深度睡眠模式
        // 1. 保存关键状态到非易失性存储
        save_critical_state_to_nvram();

        // 2. 关闭非必要硬件
        disable_non_essential_peripherals();

        // 3. 配置唤醒源
        configure_wakeup_sources();

        // 4. 进入深度睡眠
        enter_deep_sleep_mode();
    }

    CoAPResponse handle_coap_get(const CoAPRequest& request) {
        // 解析 CoAP 请求中的键
        std::string key = extract_key_from_coap_request(request);

        // 从缓存获取值
        std::string value = ultra_light_cache.get(key);

        // 构造 CoAP 响应
        CoAPResponse response;
        if (!value.empty()) {
            response.code = CoAPCode::CONTENT;
            response.payload = value;
        } else {
            response.code = CoAPCode::NOT_FOUND;
        }

        return response;
    }

    void initialize_nxp_hardware() {
        // 初始化 NXP 加密加速器
        crypto_hw.initialize();

        // 初始化硬件随机数生成器
        rng_hw.initialize();

        // 配置 NXP 特有的外设
        configure_nxp_peripherals();
    }
};

// NXP EdgeLock 安全元件集成
class NXPEdgeLockSecurity {
private:
    EdgeLockInterface edgelock;

public:
    // 使用 EdgeLock 进行设备认证
    bool authenticate_device() {
        // 获取设备唯一标识
        DeviceUID uid = edgelock.get_device_uid();

        // 生成认证挑战
        AuthChallenge challenge = generate_auth_challenge();

        // 使用 EdgeLock 签名
        Signature signature = edgelock.sign_challenge(challenge);

        // 验证签名
        return verify_device_signature(uid, challenge, signature);
    }

    // 安全密钥派生
    std::vector<uint8_t> derive_encryption_key(const std::string& context) {
        // 使用 EdgeLock 的密钥派生功能
        return edgelock.derive_key(context, 32); // 256位密钥
    }
};
```

---

**文档状态**: ✅ 已完成 (包含验证过的真实题目 + 技术方向推理)
**最后更新**: 2025年1月
**内容说明**:
- **✅ 核心真实题目**: 基于公开面试经验和技术文档验证的常见 Redis 面试题
- **🔧 技术扩展内容**: 基于各公司技术栈特点进行的合理技术推理和学习参考
- **📚 学习价值**: 涵盖 Redis 技术的各个方面，适合系统性学习和面试准备
**题目数量**: 49道题目 (包含验证题目和推理扩展)
**难度覆盖**: ⭐-⭐⭐⭐⭐⭐ (初级到专家级)
**技术覆盖**: Redis 基础、数据结构、持久化、缓存问题、集群、性能优化、分布式锁、实际应用、监控运维、系统设计
**使用建议**:
- 重点关注标记为"验证过"的基础题目
- 将技术扩展内容作为学习参考，了解不同技术方向的 Redis 应用
- 面试准备时以官方文档和公开资料为准
