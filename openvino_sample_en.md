# AI Use Case Tutorial Based on OpenVINO Samples

This document provides a step-by-step tutorial for building AI use cases using OpenVINO samples. Each use case includes its name, introduction, real-world application scenario, detailed build steps with code snippets, final effect presentation (if available), potential extensions, and performance data (if available).

## Use Case 1: Hello Classification

### Introduction
The "Hello Classification" sample demonstrates how to perform image classification using OpenVINO. This use case corresponds to scenarios such as identifying objects in images for applications like smart surveillance, retail analytics, and healthcare imaging.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/hello_classification
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained model and input image.

```bash
./hello_classification -m <path_to_model> -i <path_to_image>
```

### Final Effect Presentation
If available, refer to the documentation or output logs for the classification results.

### Extensions
This use case can be extended to:
- Multi-label classification.
- Real-time classification in video streams.
- Integration with edge devices for on-site analytics.

### Performance Data
Refer to OpenVINO Benchmark Tool for detailed performance metrics.

## Use Case 2: Hello Reshape SSD

### Introduction
The "Hello Reshape SSD" sample demonstrates object detection using the Single Shot MultiBox Detector (SSD) model. This use case corresponds to scenarios such as autonomous driving, smart surveillance, and industrial automation.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/hello_reshape_ssd
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained SSD model and input image.

```bash
./hello_reshape_ssd -m <path_to_model> -i <path_to_image>
```

### Final Effect Presentation
If available, refer to the documentation or output logs for the detection results.

### Extensions
This use case can be extended to:
- Real-time object detection in video streams.
- Integration with robotics for vision-guided tasks.
- Deployment on edge devices for low-latency detection.

### Performance Data
Refer to OpenVINO Benchmark Tool for detailed performance metrics.

## Use Case 3: Benchmark

### Introduction
The "Benchmark" sample provides tools for measuring inference performance. This use case corresponds to scenarios such as evaluating model efficiency for deployment in production environments.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/benchmark
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the benchmark tool with a pre-trained model.

```bash
./benchmark_app -m <path_to_model>
```

### Final Effect Presentation
If available, refer to the documentation or output logs for performance metrics.

### Extensions
This use case can be extended to:
- Comparing performance across different hardware devices.
- Evaluating the impact of model quantization on inference speed.
- Optimizing model configurations for specific deployment scenarios.

### Performance Data
Refer to OpenVINO Benchmark Tool for detailed performance metrics.

## Use Case 4: Model Creation Sample

### Introduction
The "Model Creation Sample" demonstrates how to create and manipulate models programmatically. This use case corresponds to scenarios such as custom model development and dynamic model generation.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/model_creation_sample
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample to create and manipulate a model.

```bash
./model_creation_sample
```

### Final Effect Presentation
If available, refer to the documentation or output logs for model creation results.

### Extensions
This use case can be extended to:
- Automating model creation for specific tasks.
- Integrating model creation into larger AI pipelines.
- Exploring dynamic model generation techniques.

### Performance Data
Refer to OpenVINO Benchmark Tool for detailed performance metrics.

## Use Case 5: Classification Sample Async

### Introduction
The "Classification Sample Async" demonstrates asynchronous image classification. This use case corresponds to scenarios such as real-time analytics and low-latency applications.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/classification_sample_async
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained model and input image.

```bash
./classification_sample_async -m <path_to_model> -i <path_to_image>
```

### Final Effect Presentation
If available, refer to the documentation or output logs for classification results.

### Extensions
This use case can be extended to:
- Real-time classification in video streams.
- Integration with edge devices for on-site analytics.
- Exploring asynchronous techniques for other AI tasks.

### Performance Data
Refer to OpenVINO Benchmark Tool for detailed performance metrics.

## Use Case 6: Benchmark Tool

### Introduction
The "Benchmark Tool" sample demonstrates how to estimate deep learning inference performance on supported devices. This use case corresponds to scenarios such as evaluating model efficiency for deployment in production environments.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/benchmark_app
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the benchmark tool with a pre-trained model.

```bash
./benchmark_app -m <path_to_model>
```

### Final Effect Presentation
Refer to the documentation for detailed performance metrics.

### Extensions
This use case can be extended to:
- Comparing performance across different hardware devices.
- Evaluating the impact of model quantization on inference speed.
- Optimizing model configurations for specific deployment scenarios.

### Performance Data
The benchmarking application works with models in OpenVINO IR, TensorFlow, TensorFlow Lite, PaddlePaddle, PyTorch, and ONNX formats. Detailed metrics can be found in the [Benchmark Tool documentation](https://docs.openvino.ai/2025/get-started/learn-openvino/openvino-samples/benchmark-tool.html).

## Use Case 7: Image Classification Async

### Introduction
The "Image Classification Async" sample demonstrates asynchronous image classification using the Asynchronous Inference Request API. This use case corresponds to scenarios such as real-time analytics and low-latency applications.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/python/classification_sample_async
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained model and input image.

```bash
python classification_sample_async.py -m <path_to_model> -i <path_to_image>
```

### Final Effect Presentation
Refer to the documentation for classification results.

### Extensions
This use case can be extended to:
- Real-time classification in video streams.
- Integration with edge devices for on-site analytics.
- Exploring asynchronous techniques for other AI tasks.

### Performance Data
The sample supports models in OpenVINO IR and ONNX formats. Detailed metrics can be found in the [Image Classification Async documentation](https://docs.openvino.ai/2025/get-started/learn-openvino/openvino-samples/image-classification-async.html).

## Use Case 8: Hello Reshape SSD (Node.js)

### Introduction
The "Hello Reshape SSD" sample demonstrates object detection using the Single Shot MultiBox Detector (SSD) model in Node.js. This use case corresponds to scenarios such as autonomous driving, smart surveillance, and industrial automation.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Run the Sample
Execute the sample with a pre-trained SSD model and input image.

```bash
node hello_reshape_ssd.js <path_to_model_file> <path_to_img> AUTO
```

### Final Effect Presentation
Refer to the documentation for detection results.

### Extensions
This use case can be extended to:
- Real-time object detection in video streams.
- Integration with robotics for vision-guided tasks.
- Deployment on edge devices for low-latency detection.

### Performance Data
Detailed metrics can be found in the [Hello Reshape SSD documentation](https://docs.openvino.ai/2025/get-started/learn-openvino/openvino-samples/hello-reshape-ssd.html).

## Use Case 9: Hello Classification (C)

### Introduction
The "Hello Classification" sample demonstrates how to execute an inference of image classification networks like AlexNet and GoogLeNet using the Synchronous Inference Request API and input auto-resize feature. This use case corresponds to scenarios such as identifying objects in images for applications like smart surveillance, retail analytics, and healthcare imaging.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/c/hello_classification
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained model and input image.

```bash
./hello_classification -m <path_to_model> -i <path_to_image>
```

### Final Effect Presentation
Refer to the documentation for classification results.

### Extensions
This use case can be extended to:
- Multi-label classification.
- Real-time classification in video streams.
- Integration with edge devices for on-site analytics.

### Performance Data
The sample supports models in OpenVINO IR and ONNX formats. Detailed metrics can be found in the [Hello Classification documentation](https://docs.openvino.ai/2025/get-started/learn-openvino/openvino-samples/hello-classification.html).

## Use Case 10: Node.js Samples

### Introduction
The Node.js samples demonstrate various AI tasks using OpenVINO bindings for Node.js. These include classification, object detection, optical character recognition, and vision background removal.

### Build Steps

#### Step 1: Install Dependencies
Set up the Node.js environment and install necessary dependencies.

```bash
cd /home/<USER>/openvino/samples/js/node
npm install
```

#### Step 2: Run Samples
Execute individual samples with appropriate models and input data.

```bash
node hello_classification/hello_classification.js <path_to_model> <path_to_image>
node hello_reshape_ssd/hello_reshape_ssd.js <path_to_model> <path_to_image> AUTO
node optical_character_recognition/optical-character-recognition.js <path_to_model> <path_to_image>
node vision_background_removal/vision-background-removal.js <path_to_model> <path_to_image>
```

### Final Effect Presentation
Refer to the documentation for results of each sample.

### Extensions
These use cases can be extended to:
- Real-time analytics in web applications.
- Integration with cloud services for scalable AI solutions.
- Deployment on edge devices for low-latency processing.

### Performance Data
Detailed metrics can be found in the respective sample documentation.

## Use Case 11: Sync Benchmark (C++)

### Introduction
The "Sync Benchmark" sample demonstrates how to measure inference performance using synchronous requests in C++. This use case corresponds to scenarios such as evaluating model efficiency for deployment in production environments.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/cpp/benchmark/sync_benchmark
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained model.

```bash
./sync_benchmark -m <path_to_model>
```

### Final Effect Presentation
Refer to the documentation for detailed performance metrics.

### Extensions
This use case can be extended to:
- Comparing performance across different hardware devices.
- Evaluating the impact of model quantization on inference speed.
- Optimizing model configurations for specific deployment scenarios.

### Performance Data
Detailed metrics can be found in the [Sync Benchmark documentation](https://docs.openvino.ai/2025/get-started/learn-openvino/openvino-samples/sync-benchmark.html).

## Use Case 12: Hello NV12 Input Classification (C)

### Introduction
The "Hello NV12 Input Classification" sample demonstrates how to execute an inference of image classification networks like AlexNet with images in NV12 color format using the Synchronous Inference Request API. This use case corresponds to scenarios such as identifying objects in images for applications like smart surveillance, retail analytics, and healthcare imaging.

### Build Steps

#### Step 1: Prepare Environment
Set up the OpenVINO environment and install necessary dependencies.

```bash
source /opt/intel/openvino/bin/setupvars.sh
```

#### Step 2: Build the Sample
Navigate to the sample directory and build the project.

```bash
cd /home/<USER>/openvino/samples/c/hello_nv12_input_classification
mkdir build && cd build
cmake ..
make
```

#### Step 3: Run the Sample
Execute the sample with a pre-trained model and input image in NV12 format.

```bash
./hello_nv12_input_classification -m <path_to_model> -i <path_to_nv12_image>
```

### Final Effect Presentation
Refer to the documentation for classification results.

### Extensions
This use case can be extended to:
- Multi-label classification.
- Real-time classification in video streams.
- Integration with edge devices for on-site analytics.

### Performance Data
The sample supports models in OpenVINO IR and ONNX formats. Detailed metrics can be found in the [Hello NV12 Input Classification documentation](https://docs.openvino.ai/2025/get-started/learn-openvino/openvino-samples/hello-nv12-input-classification.html).

## Conclusion
This tutorial provides a comprehensive guide to building AI use cases using OpenVINO samples. Each use case demonstrates the versatility and efficiency of OpenVINO in real-world applications. Developers are encouraged to explore extensions and performance optimizations to maximize the potential of these use cases.
