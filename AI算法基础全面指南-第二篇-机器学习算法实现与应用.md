# AI算法基础全面指南
## 第二篇：机器学习算法实现与应用

> **文档说明**: 这是AI算法基础全面指南的第二篇，专注于机器学习算法的具体实现和实际应用。本篇将详细解释所有专业术语，提供完整的代码实现，确保读者能够深入理解并实际应用这些算法。

---

## 目录

- [第六章：监督学习算法深度实现](#第六章监督学习算法深度实现)
- [第七章：无监督学习算法全解析](#第七章无监督学习算法全解析)
- [第八章：深度学习架构详解](#第八章深度学习架构详解)
- [第九章：集成学习与模型融合](#第九章集成学习与模型融合)
- [第十章：模型评估与选择策略](#第十章模型评估与选择策略)

---

# 第六章：监督学习算法深度实现

> **核心理念**: 监督学习是机器学习的核心分支，通过标记数据学习输入到输出的映射关系，是解决分类和回归问题的主要方法。

## 6.1 专业术语详解

### 6.1.1 监督学习基础概念

**监督学习 (Supervised Learning)**
- **定义**: 使用标记数据（输入-输出对）训练模型，学习从输入到输出的映射函数
- **数学表示**: 给定训练集 D = {(x₁,y₁), (x₂,y₂), ..., (xₙ,yₙ)}，学习函数 f: X → Y
- **目标**: 最小化经验风险 R_emp(f) = (1/n)Σᵢ L(f(xᵢ), yᵢ)
- **类型**:
  - **分类**: 输出是离散的类别标签
  - **回归**: 输出是连续的数值
- **关键假设**: 训练数据和测试数据来自同一分布
- **应用场景**: 图像识别、语音识别、医疗诊断、金融预测

**训练集与测试集 (Training Set & Test Set)**
- **训练集**: 用于训练模型参数的数据集
- **验证集**: 用于模型选择和超参数调优的数据集
- **测试集**: 用于评估模型最终性能的数据集
- **数据分割比例**: 通常采用 70:15:15 或 80:10:10 的比例
- **重要原则**: 
  - 测试集不能用于模型训练
  - 避免数据泄露（Data Leakage）
  - 确保数据分布的一致性

**过拟合与欠拟合 (Overfitting & Underfitting)**
- **过拟合**: 模型在训练数据上表现很好，但在新数据上表现差
  - **原因**: 模型复杂度过高，学习了噪声和细节
  - **表现**: 训练误差很小，验证误差很大
  - **解决方法**: 正则化、早停、数据增强、集成方法
- **欠拟合**: 模型在训练数据和测试数据上都表现不好
  - **原因**: 模型复杂度过低，无法捕捉数据的真实模式
  - **表现**: 训练误差和验证误差都很大
  - **解决方法**: 增加模型复杂度、特征工程、减少正则化

**偏差-方差权衡 (Bias-Variance Tradeoff)**
- **偏差 (Bias)**: 模型预测值与真实值之间的系统性差异
  - **高偏差**: 模型过于简单，无法捕捉数据的复杂性（欠拟合）
  - **低偏差**: 模型能够很好地拟合真实的数据模式
- **方差 (Variance)**: 模型对训练数据变化的敏感程度
  - **高方差**: 模型对训练数据的小变化很敏感（过拟合）
  - **低方差**: 模型对训练数据的变化不敏感，预测稳定
- **权衡关系**: 总误差 = 偏差² + 方差 + 噪声
- **实际应用**: 需要在偏差和方差之间找到最佳平衡点

**正则化 (Regularization)**
- **定义**: 在损失函数中添加惩罚项，防止模型过拟合
- **L1正则化 (Lasso)**:
  - **惩罚项**: λΣᵢ|wᵢ|
  - **效果**: 产生稀疏解，自动进行特征选择
  - **几何解释**: L1球约束
- **L2正则化 (Ridge)**:
  - **惩罚项**: λΣᵢwᵢ²
  - **效果**: 参数收缩，防止参数过大
  - **几何解释**: L2球约束
- **弹性网络 (Elastic Net)**: 结合L1和L2正则化
- **正则化参数λ**: 控制正则化强度的超参数

### 6.1.2 线性模型专业术语

**线性回归 (Linear Regression)**
- **模型假设**: y = wᵀx + b + ε，其中ε是噪声项
- **最小二乘法 (Least Squares)**: 最小化平方误差和
- **正规方程**: w = (XᵀX)⁻¹Xᵀy
- **几何解释**: 寻找最佳拟合直线/超平面
- **假设条件**:
  - 线性关系
  - 独立性
  - 同方差性
  - 正态性（用于推断）

**逻辑回归 (Logistic Regression)**
- **Sigmoid函数**: σ(z) = 1/(1 + e⁻ᶻ)，将实数映射到(0,1)
- **对数几率 (Log-odds)**: ln(p/(1-p)) = wᵀx + b
- **最大似然估计**: 最大化似然函数L(w) = Πᵢ p(yᵢ|xᵢ)
- **交叉熵损失**: -Σᵢ[yᵢlog(pᵢ) + (1-yᵢ)log(1-pᵢ)]
- **多分类扩展**: 
  - **一对多 (One-vs-Rest)**: 训练多个二分类器
  - **Softmax回归**: 多分类的直接扩展

**支持向量机 (Support Vector Machine, SVM)**
- **最大间隔 (Maximum Margin)**: 寻找能够最大化类别间距的超平面
- **支持向量**: 距离分类超平面最近的训练样本
- **间隔 (Margin)**: 支持向量到分类超平面的距离
- **硬间隔**: 要求所有样本都被正确分类
- **软间隔**: 允许一些样本被错误分类，引入松弛变量ξᵢ
- **核技巧 (Kernel Trick)**: 通过核函数将数据映射到高维空间
  - **线性核**: K(x,y) = xᵀy
  - **多项式核**: K(x,y) = (xᵀy + c)ᵈ
  - **RBF核**: K(x,y) = exp(-γ||x-y||²)
  - **Sigmoid核**: K(x,y) = tanh(αxᵀy + c)

### 6.1.3 树模型专业术语

**决策树 (Decision Tree)**
- **节点类型**:
  - **根节点**: 树的顶部节点
  - **内部节点**: 表示特征测试的节点
  - **叶节点**: 表示类别或数值的终端节点
- **分割准则**:
  - **信息增益**: IG = H(S) - Σ(|Sᵥ|/|S|)H(Sᵥ)
  - **信息增益率**: IGR = IG / SplitInfo，避免偏向多值特征
  - **基尼不纯度**: Gini = 1 - Σpᵢ²，计算简单
  - **均方误差**: MSE，用于回归树
- **剪枝 (Pruning)**:
  - **预剪枝**: 在构建过程中提前停止
  - **后剪枝**: 构建完整树后再剪枝
- **优势**: 可解释性强，能处理非线性关系
- **缺点**: 容易过拟合，对噪声敏感

**随机森林 (Random Forest)**
- **Bagging**: Bootstrap Aggregating，自助采样聚合
- **特征随机选择**: 每次分割时随机选择特征子集
- **投票机制**:
  - **分类**: 多数投票
  - **回归**: 平均值
- **袋外误差 (Out-of-Bag Error)**: 使用未被采样的数据评估模型
- **特征重要性**: 基于特征在所有树中的平均重要性
- **优势**: 减少过拟合，提供特征重要性，处理缺失值

**梯度提升树 (Gradient Boosting)**
- **提升 (Boosting)**: 串行训练弱学习器，每个学习器纠正前面的错误
- **梯度提升**: 使用梯度信息指导新学习器的训练
- **残差拟合**: 新树拟合前面模型的残差
- **学习率**: 控制每个树的贡献程度
- **XGBoost特性**:
  - **二阶梯度**: 使用二阶导数信息
  - **正则化**: 内置L1和L2正则化
  - **并行化**: 特征级别的并行化
  - **缺失值处理**: 自动学习缺失值的最佳分割方向

## 6.2 监督学习算法完整实现

### 6.2.1 线性模型实现

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification, make_regression
from sklearn.model_selection import train_test_split
import pandas as pd

class LinearRegression:
    """线性回归的完整实现"""

    def __init__(self, fit_intercept=True, regularization=None, alpha=0.01):
        """
        参数:
        - fit_intercept: 是否拟合截距项
        - regularization: 正则化类型 ('l1', 'l2', 'elastic_net', None)
        - alpha: 正则化强度
        """
        self.fit_intercept = fit_intercept
        self.regularization = regularization
        self.alpha = alpha
        self.weights = None
        self.intercept = None

    def _add_intercept(self, X):
        """添加截距项"""
        intercept = np.ones((X.shape[0], 1))
        return np.concatenate((intercept, X), axis=1)

    def fit(self, X, y):
        """训练线性回归模型"""

        # 添加截距项
        if self.fit_intercept:
            X = self._add_intercept(X)

        # 不同的求解方法
        if self.regularization is None:
            # 普通最小二乘法：w = (X^T X)^(-1) X^T y
            self.weights = np.linalg.inv(X.T @ X) @ X.T @ y

        elif self.regularization == 'l2':
            # 岭回归：w = (X^T X + αI)^(-1) X^T y
            I = np.eye(X.shape[1])
            if self.fit_intercept:
                I[0, 0] = 0  # 不对截距项进行正则化
            self.weights = np.linalg.inv(X.T @ X + self.alpha * I) @ X.T @ y

        elif self.regularization == 'l1':
            # Lasso回归：使用坐标下降法
            self.weights = self._coordinate_descent(X, y)

        else:
            raise ValueError(f"不支持的正则化类型: {self.regularization}")

        # 分离截距和权重
        if self.fit_intercept:
            self.intercept = self.weights[0]
            self.weights = self.weights[1:]
        else:
            self.intercept = 0

    def _coordinate_descent(self, X, y, max_iter=1000, tol=1e-6):
        """坐标下降法求解Lasso回归"""
        n_samples, n_features = X.shape
        weights = np.zeros(n_features)

        for iteration in range(max_iter):
            weights_old = weights.copy()

            for j in range(n_features):
                # 计算残差（不包括第j个特征的贡献）
                residual = y - X @ weights + X[:, j] * weights[j]

                # 计算第j个特征的系数
                rho = X[:, j] @ residual

                # 软阈值操作
                if self.fit_intercept and j == 0:
                    # 截距项不进行正则化
                    weights[j] = rho / (X[:, j] @ X[:, j])
                else:
                    z = X[:, j] @ X[:, j]
                    if rho > self.alpha:
                        weights[j] = (rho - self.alpha) / z
                    elif rho < -self.alpha:
                        weights[j] = (rho + self.alpha) / z
                    else:
                        weights[j] = 0

            # 检查收敛
            if np.linalg.norm(weights - weights_old) < tol:
                print(f"坐标下降法在第 {iteration+1} 次迭代后收敛")
                break

        return weights

    def predict(self, X):
        """预测"""
        return X @ self.weights + self.intercept

    def score(self, X, y):
        """计算R²分数"""
        y_pred = self.predict(X)
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        return 1 - (ss_res / ss_tot)

class LogisticRegression:
    """逻辑回归的完整实现"""

    def __init__(self, learning_rate=0.01, max_iter=1000, fit_intercept=True,
                 regularization=None, alpha=0.01):
        """
        参数:
        - learning_rate: 学习率
        - max_iter: 最大迭代次数
        - fit_intercept: 是否拟合截距项
        - regularization: 正则化类型
        - alpha: 正则化强度
        """
        self.learning_rate = learning_rate
        self.max_iter = max_iter
        self.fit_intercept = fit_intercept
        self.regularization = regularization
        self.alpha = alpha
        self.weights = None
        self.intercept = None
        self.cost_history = []

    def _add_intercept(self, X):
        """添加截距项"""
        intercept = np.ones((X.shape[0], 1))
        return np.concatenate((intercept, X), axis=1)

    def _sigmoid(self, z):
        """Sigmoid激活函数"""
        # 防止数值溢出
        z = np.clip(z, -500, 500)
        return 1 / (1 + np.exp(-z))

    def _cost_function(self, h, y, weights):
        """计算代价函数"""
        # 交叉熵损失
        cost = (-y * np.log(h) - (1 - y) * np.log(1 - h)).mean()

        # 添加正则化项
        if self.regularization == 'l1':
            if self.fit_intercept:
                cost += self.alpha * np.sum(np.abs(weights[1:]))
            else:
                cost += self.alpha * np.sum(np.abs(weights))
        elif self.regularization == 'l2':
            if self.fit_intercept:
                cost += self.alpha * np.sum(weights[1:] ** 2)
            else:
                cost += self.alpha * np.sum(weights ** 2)

        return cost

    def fit(self, X, y):
        """训练逻辑回归模型"""

        # 添加截距项
        if self.fit_intercept:
            X = self._add_intercept(X)

        # 初始化权重
        self.weights = np.zeros(X.shape[1])

        # 梯度下降
        for i in range(self.max_iter):
            # 前向传播
            z = X @ self.weights
            h = self._sigmoid(z)

            # 计算代价
            cost = self._cost_function(h, y, self.weights)
            self.cost_history.append(cost)

            # 计算梯度
            gradient = X.T @ (h - y) / y.size

            # 添加正则化梯度
            if self.regularization == 'l1':
                l1_gradient = self.alpha * np.sign(self.weights)
                if self.fit_intercept:
                    l1_gradient[0] = 0  # 不对截距项正则化
                gradient += l1_gradient
            elif self.regularization == 'l2':
                l2_gradient = 2 * self.alpha * self.weights
                if self.fit_intercept:
                    l2_gradient[0] = 0  # 不对截距项正则化
                gradient += l2_gradient

            # 更新权重
            self.weights -= self.learning_rate * gradient

        # 分离截距和权重
        if self.fit_intercept:
            self.intercept = self.weights[0]
            self.weights = self.weights[1:]
        else:
            self.intercept = 0

    def predict_proba(self, X):
        """预测概率"""
        z = X @ self.weights + self.intercept
        return self._sigmoid(z)

    def predict(self, X):
        """预测类别"""
        return (self.predict_proba(X) >= 0.5).astype(int)

    def score(self, X, y):
        """计算准确率"""
        return (self.predict(X) == y).mean()

class SupportVectorMachine:
    """支持向量机的简化实现（SMO算法）"""

    def __init__(self, C=1.0, kernel='linear', gamma='scale', degree=3,
                 coef0=0.0, tol=1e-3, max_iter=1000):
        """
        参数:
        - C: 正则化参数
        - kernel: 核函数类型
        - gamma: RBF核的参数
        - degree: 多项式核的度数
        - coef0: 核函数的独立项
        - tol: 容忍度
        - max_iter: 最大迭代次数
        """
        self.C = C
        self.kernel = kernel
        self.gamma = gamma
        self.degree = degree
        self.coef0 = coef0
        self.tol = tol
        self.max_iter = max_iter

        self.alpha = None
        self.support_vectors = None
        self.support_vector_labels = None
        self.support_vector_alpha = None
        self.intercept = None

    def _kernel_function(self, X1, X2):
        """计算核函数"""
        if self.kernel == 'linear':
            return X1 @ X2.T
        elif self.kernel == 'poly':
            return (self.gamma * X1 @ X2.T + self.coef0) ** self.degree
        elif self.kernel == 'rbf':
            if self.gamma == 'scale':
                gamma = 1.0 / (X1.shape[1] * X1.var())
            else:
                gamma = self.gamma

            # 计算欧几里得距离的平方
            X1_norm = np.sum(X1 ** 2, axis=1, keepdims=True)
            X2_norm = np.sum(X2 ** 2, axis=1, keepdims=True)
            distances_sq = X1_norm + X2_norm.T - 2 * X1 @ X2.T
            return np.exp(-gamma * distances_sq)
        else:
            raise ValueError(f"不支持的核函数: {self.kernel}")

    def fit(self, X, y):
        """训练SVM模型（简化的SMO算法）"""
        n_samples, n_features = X.shape

        # 将标签转换为-1和1
        y = np.where(y <= 0, -1, 1)

        # 初始化拉格朗日乘子
        self.alpha = np.zeros(n_samples)
        self.intercept = 0

        # 计算核矩阵
        K = self._kernel_function(X, X)

        # 简化的SMO算法
        for iteration in range(self.max_iter):
            alpha_prev = self.alpha.copy()

            for i in range(n_samples):
                # 计算预测值
                prediction = np.sum(self.alpha * y * K[:, i]) + self.intercept

                # 计算误差
                E_i = prediction - y[i]

                # 检查KKT条件
                if (y[i] * E_i < -self.tol and self.alpha[i] < self.C) or \
                   (y[i] * E_i > self.tol and self.alpha[i] > 0):

                    # 随机选择第二个变量
                    j = np.random.choice([idx for idx in range(n_samples) if idx != i])

                    # 计算边界
                    if y[i] != y[j]:
                        L = max(0, self.alpha[j] - self.alpha[i])
                        H = min(self.C, self.C + self.alpha[j] - self.alpha[i])
                    else:
                        L = max(0, self.alpha[i] + self.alpha[j] - self.C)
                        H = min(self.C, self.alpha[i] + self.alpha[j])

                    if L == H:
                        continue

                    # 计算eta
                    eta = 2 * K[i, j] - K[i, i] - K[j, j]
                    if eta >= 0:
                        continue

                    # 计算新的alpha_j
                    E_j = np.sum(self.alpha * y * K[:, j]) + self.intercept - y[j]
                    alpha_j_new = self.alpha[j] - y[j] * (E_i - E_j) / eta

                    # 裁剪alpha_j
                    alpha_j_new = max(L, min(H, alpha_j_new))

                    if abs(alpha_j_new - self.alpha[j]) < 1e-5:
                        continue

                    # 计算新的alpha_i
                    alpha_i_new = self.alpha[i] + y[i] * y[j] * (self.alpha[j] - alpha_j_new)

                    # 更新截距
                    b1 = self.intercept - E_i - y[i] * (alpha_i_new - self.alpha[i]) * K[i, i] - \
                         y[j] * (alpha_j_new - self.alpha[j]) * K[i, j]
                    b2 = self.intercept - E_j - y[i] * (alpha_i_new - self.alpha[i]) * K[i, j] - \
                         y[j] * (alpha_j_new - self.alpha[j]) * K[j, j]

                    if 0 < alpha_i_new < self.C:
                        self.intercept = b1
                    elif 0 < alpha_j_new < self.C:
                        self.intercept = b2
                    else:
                        self.intercept = (b1 + b2) / 2

                    # 更新alpha
                    self.alpha[i] = alpha_i_new
                    self.alpha[j] = alpha_j_new

            # 检查收敛
            if np.linalg.norm(self.alpha - alpha_prev) < self.tol:
                print(f"SMO算法在第 {iteration+1} 次迭代后收敛")
                break

        # 提取支持向量
        support_vector_indices = self.alpha > 1e-5
        self.support_vectors = X[support_vector_indices]
        self.support_vector_labels = y[support_vector_indices]
        self.support_vector_alpha = self.alpha[support_vector_indices]

        print(f"支持向量数量: {len(self.support_vectors)}/{n_samples}")

    def predict(self, X):
        """预测"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        # 计算核函数值
        K = self._kernel_function(X, self.support_vectors)

        # 计算预测值
        predictions = np.sum(self.support_vector_alpha * self.support_vector_labels * K.T, axis=0) + self.intercept

        return np.sign(predictions)

    def score(self, X, y):
        """计算准确率"""
        y_binary = np.where(y <= 0, -1, 1)
        return (self.predict(X) == y_binary).mean()

# 演示线性模型
def demonstrate_linear_models():
    """演示线性模型的实现和应用"""

    print("=== 线性模型演示 ===")

    # 1. 线性回归演示
    print("\n1. 线性回归演示")

    # 生成回归数据
    X_reg, y_reg = make_regression(n_samples=100, n_features=1, noise=10, random_state=42)
    X_train_reg, X_test_reg, y_train_reg, y_test_reg = train_test_split(
        X_reg, y_reg, test_size=0.2, random_state=42
    )

    # 训练不同的线性回归模型
    models_reg = {
        '普通最小二乘': LinearRegression(),
        '岭回归 (α=1.0)': LinearRegression(regularization='l2', alpha=1.0),
        'Lasso回归 (α=1.0)': LinearRegression(regularization='l1', alpha=1.0)
    }

    plt.figure(figsize=(15, 5))

    for i, (name, model) in enumerate(models_reg.items()):
        model.fit(X_train_reg, y_train_reg)

        # 预测
        y_pred_train = model.predict(X_train_reg)
        y_pred_test = model.predict(X_test_reg)

        # 计算分数
        train_score = model.score(X_train_reg, y_train_reg)
        test_score = model.score(X_test_reg, y_test_reg)

        print(f"{name}:")
        print(f"  训练R²: {train_score:.4f}")
        print(f"  测试R²: {test_score:.4f}")
        print(f"  权重: {model.weights[0]:.4f}")
        print(f"  截距: {model.intercept:.4f}")

        # 可视化
        plt.subplot(1, 3, i+1)
        plt.scatter(X_test_reg, y_test_reg, alpha=0.7, label='真实值')

        # 绘制拟合线
        X_plot = np.linspace(X_reg.min(), X_reg.max(), 100).reshape(-1, 1)
        y_plot = model.predict(X_plot)
        plt.plot(X_plot, y_plot, 'r-', linewidth=2, label='拟合线')

        plt.title(f'{name}\nR² = {test_score:.3f}')
        plt.xlabel('X')
        plt.ylabel('y')
        plt.legend()
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

# 运行演示
demonstrate_linear_models()
```

### 6.2.2 决策树与集成方法实现

```python
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification

class DecisionTreeNode:
    """决策树节点"""

    def __init__(self, feature=None, threshold=None, left=None, right=None,
                 value=None, samples=None, impurity=None):
        self.feature = feature      # 分割特征的索引
        self.threshold = threshold  # 分割阈值
        self.left = left           # 左子树
        self.right = right         # 右子树
        self.value = value         # 叶节点的预测值
        self.samples = samples     # 节点包含的样本数
        self.impurity = impurity   # 节点的不纯度

class DecisionTree:
    """决策树的完整实现"""

    def __init__(self, max_depth=None, min_samples_split=2, min_samples_leaf=1,
                 criterion='gini', random_state=None):
        """
        参数:
        - max_depth: 树的最大深度
        - min_samples_split: 分割所需的最小样本数
        - min_samples_leaf: 叶节点的最小样本数
        - criterion: 分割准则 ('gini', 'entropy', 'mse')
        - random_state: 随机种子
        """
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.criterion = criterion
        self.random_state = random_state
        self.root = None
        self.n_features = None
        self.n_classes = None

        if random_state:
            np.random.seed(random_state)

    def _gini_impurity(self, y):
        """计算基尼不纯度"""
        if len(y) == 0:
            return 0

        proportions = np.bincount(y) / len(y)
        return 1 - np.sum(proportions ** 2)

    def _entropy(self, y):
        """计算熵"""
        if len(y) == 0:
            return 0

        proportions = np.bincount(y) / len(y)
        # 避免log(0)
        proportions = proportions[proportions > 0]
        return -np.sum(proportions * np.log2(proportions))

    def _mse(self, y):
        """计算均方误差（用于回归）"""
        if len(y) == 0:
            return 0

        mean = np.mean(y)
        return np.mean((y - mean) ** 2)

    def _calculate_impurity(self, y):
        """根据准则计算不纯度"""
        if self.criterion == 'gini':
            return self._gini_impurity(y)
        elif self.criterion == 'entropy':
            return self._entropy(y)
        elif self.criterion == 'mse':
            return self._mse(y)
        else:
            raise ValueError(f"不支持的准则: {self.criterion}")

    def _information_gain(self, y, y_left, y_right):
        """计算信息增益"""
        n = len(y)
        n_left, n_right = len(y_left), len(y_right)

        if n_left == 0 or n_right == 0:
            return 0

        # 加权平均的子节点不纯度
        weighted_impurity = (n_left / n) * self._calculate_impurity(y_left) + \
                           (n_right / n) * self._calculate_impurity(y_right)

        # 信息增益 = 父节点不纯度 - 加权子节点不纯度
        return self._calculate_impurity(y) - weighted_impurity

    def _best_split(self, X, y):
        """寻找最佳分割"""
        best_gain = -1
        best_feature = None
        best_threshold = None

        n_samples, n_features = X.shape

        # 遍历所有特征
        for feature in range(n_features):
            # 获取该特征的所有唯一值作为候选阈值
            thresholds = np.unique(X[:, feature])

            for threshold in thresholds:
                # 根据阈值分割数据
                left_mask = X[:, feature] <= threshold
                right_mask = ~left_mask

                if np.sum(left_mask) < self.min_samples_leaf or \
                   np.sum(right_mask) < self.min_samples_leaf:
                    continue

                # 计算信息增益
                y_left, y_right = y[left_mask], y[right_mask]
                gain = self._information_gain(y, y_left, y_right)

                # 更新最佳分割
                if gain > best_gain:
                    best_gain = gain
                    best_feature = feature
                    best_threshold = threshold

        return best_feature, best_threshold, best_gain

    def _build_tree(self, X, y, depth=0):
        """递归构建决策树"""
        n_samples, n_features = X.shape
        n_classes = len(np.unique(y))

        # 创建节点
        node = DecisionTreeNode(
            samples=n_samples,
            impurity=self._calculate_impurity(y)
        )

        # 停止条件
        if (self.max_depth is not None and depth >= self.max_depth) or \
           n_samples < self.min_samples_split or \
           n_classes == 1:
            # 创建叶节点
            if self.criterion == 'mse':
                node.value = np.mean(y)  # 回归：预测均值
            else:
                node.value = Counter(y).most_common(1)[0][0]  # 分类：预测众数
            return node

        # 寻找最佳分割
        best_feature, best_threshold, best_gain = self._best_split(X, y)

        if best_gain <= 0:
            # 无法找到有效分割，创建叶节点
            if self.criterion == 'mse':
                node.value = np.mean(y)
            else:
                node.value = Counter(y).most_common(1)[0][0]
            return node

        # 设置分割参数
        node.feature = best_feature
        node.threshold = best_threshold

        # 分割数据
        left_mask = X[:, best_feature] <= best_threshold
        right_mask = ~left_mask

        # 递归构建子树
        node.left = self._build_tree(X[left_mask], y[left_mask], depth + 1)
        node.right = self._build_tree(X[right_mask], y[right_mask], depth + 1)

        return node

    def fit(self, X, y):
        """训练决策树"""
        self.n_features = X.shape[1]
        if self.criterion != 'mse':
            self.n_classes = len(np.unique(y))

        self.root = self._build_tree(X, y)

    def _predict_sample(self, x, node):
        """预测单个样本"""
        if node.value is not None:
            return node.value

        if x[node.feature] <= node.threshold:
            return self._predict_sample(x, node.left)
        else:
            return self._predict_sample(x, node.right)

    def predict(self, X):
        """预测"""
        return np.array([self._predict_sample(x, self.root) for x in X])

    def _print_tree(self, node, depth=0, prefix="Root: "):
        """打印决策树结构"""
        if node is not None:
            if node.value is not None:
                print("  " * depth + prefix + f"Predict {node.value} (samples: {node.samples}, impurity: {node.impurity:.3f})")
            else:
                print("  " * depth + prefix + f"Feature {node.feature} <= {node.threshold:.3f} (samples: {node.samples}, impurity: {node.impurity:.3f})")
                self._print_tree(node.left, depth + 1, "Left: ")
                self._print_tree(node.right, depth + 1, "Right: ")

    def print_tree(self):
        """打印决策树"""
        self._print_tree(self.root)

class RandomForest:
    """随机森林的完整实现"""

    def __init__(self, n_estimators=100, max_depth=None, min_samples_split=2,
                 min_samples_leaf=1, max_features='sqrt', bootstrap=True,
                 random_state=None):
        """
        参数:
        - n_estimators: 树的数量
        - max_depth: 每棵树的最大深度
        - min_samples_split: 分割所需的最小样本数
        - min_samples_leaf: 叶节点的最小样本数
        - max_features: 每次分割考虑的特征数量
        - bootstrap: 是否使用自助采样
        - random_state: 随机种子
        """
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.max_features = max_features
        self.bootstrap = bootstrap
        self.random_state = random_state

        self.trees = []
        self.feature_importances_ = None

        if random_state:
            np.random.seed(random_state)

    def _get_max_features(self, n_features):
        """计算每次分割考虑的特征数量"""
        if self.max_features == 'sqrt':
            return int(np.sqrt(n_features))
        elif self.max_features == 'log2':
            return int(np.log2(n_features))
        elif isinstance(self.max_features, int):
            return min(self.max_features, n_features)
        elif isinstance(self.max_features, float):
            return int(self.max_features * n_features)
        else:
            return n_features

    def _bootstrap_sample(self, X, y):
        """生成自助采样"""
        n_samples = X.shape[0]
        indices = np.random.choice(n_samples, n_samples, replace=True)
        return X[indices], y[indices], indices

    def fit(self, X, y):
        """训练随机森林"""
        n_samples, n_features = X.shape
        max_features = self._get_max_features(n_features)

        self.trees = []
        feature_importance_sum = np.zeros(n_features)

        print(f"训练随机森林: {self.n_estimators}棵树, 每次考虑{max_features}个特征")

        for i in range(self.n_estimators):
            # 创建决策树
            tree = DecisionTree(
                max_depth=self.max_depth,
                min_samples_split=self.min_samples_split,
                min_samples_leaf=self.min_samples_leaf,
                criterion='gini',
                random_state=self.random_state + i if self.random_state else None
            )

            # 自助采样
            if self.bootstrap:
                X_sample, y_sample, _ = self._bootstrap_sample(X, y)
            else:
                X_sample, y_sample = X, y

            # 特征随机选择（在决策树的分割过程中实现）
            # 这里简化实现，使用全部特征训练
            tree.fit(X_sample, y_sample)
            self.trees.append(tree)

            if (i + 1) % 20 == 0:
                print(f"  已训练 {i + 1}/{self.n_estimators} 棵树")

        print("随机森林训练完成")

    def predict(self, X):
        """预测（多数投票）"""
        predictions = np.array([tree.predict(X) for tree in self.trees])

        # 对每个样本进行多数投票
        final_predictions = []
        for i in range(X.shape[0]):
            votes = predictions[:, i]
            final_predictions.append(Counter(votes).most_common(1)[0][0])

        return np.array(final_predictions)

    def predict_proba(self, X):
        """预测概率"""
        predictions = np.array([tree.predict(X) for tree in self.trees])

        # 计算每个类别的投票比例
        n_samples = X.shape[0]
        n_classes = len(np.unique(predictions))
        probabilities = np.zeros((n_samples, n_classes))

        for i in range(n_samples):
            votes = predictions[:, i]
            vote_counts = Counter(votes)
            for class_label, count in vote_counts.items():
                probabilities[i, int(class_label)] = count / self.n_estimators

        return probabilities

    def score(self, X, y):
        """计算准确率"""
        return (self.predict(X) == y).mean()

# 演示决策树和随机森林
def demonstrate_tree_models():
    """演示决策树和随机森林"""

    print("=== 决策树和随机森林演示 ===")

    # 生成分类数据
    X, y = make_classification(n_samples=1000, n_features=4, n_redundant=0,
                              n_informative=4, n_clusters_per_class=1,
                              random_state=42)

    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )

    print(f"数据集: {X_train.shape[0]} 训练样本, {X_test.shape[0]} 测试样本")
    print(f"特征数: {X.shape[1]}, 类别数: {len(np.unique(y))}")

    # 1. 决策树演示
    print(f"\n1. 决策树演示")

    # 训练不同深度的决策树
    depths = [3, 5, 10, None]

    plt.figure(figsize=(15, 10))

    for i, depth in enumerate(depths):
        tree = DecisionTree(max_depth=depth, random_state=42)
        tree.fit(X_train, y_train)

        train_acc = (tree.predict(X_train) == y_train).mean()
        test_acc = (tree.predict(X_test) == y_test).mean()

        depth_str = str(depth) if depth is not None else "无限制"
        print(f"  最大深度 {depth_str}:")
        print(f"    训练准确率: {train_acc:.4f}")
        print(f"    测试准确率: {test_acc:.4f}")

        # 可视化决策边界（仅使用前两个特征）
        plt.subplot(2, 2, i+1)

        # 创建网格
        h = 0.02
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                            np.arange(y_min, y_max, h))

        # 预测网格点
        grid_points = np.c_[xx.ravel(), yy.ravel(),
                           np.zeros(xx.ravel().shape[0]),
                           np.zeros(xx.ravel().shape[0])]
        Z = tree.predict(grid_points)
        Z = Z.reshape(xx.shape)

        # 绘制决策边界
        plt.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)

        # 绘制数据点
        scatter = plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test,
                            cmap=plt.cm.RdYlBu, edgecolors='black')

        plt.title(f'决策树 (深度={depth_str})\n测试准确率: {test_acc:.3f}')
        plt.xlabel('特征 1')
        plt.ylabel('特征 2')

    plt.tight_layout()
    plt.show()

    # 2. 随机森林演示
    print(f"\n2. 随机森林演示")

    # 训练不同数量的树
    n_estimators_list = [10, 50, 100, 200]

    train_accuracies = []
    test_accuracies = []

    for n_est in n_estimators_list:
        rf = RandomForest(n_estimators=n_est, max_depth=10, random_state=42)
        rf.fit(X_train, y_train)

        train_acc = rf.score(X_train, y_train)
        test_acc = rf.score(X_test, y_test)

        train_accuracies.append(train_acc)
        test_accuracies.append(test_acc)

        print(f"  {n_est}棵树:")
        print(f"    训练准确率: {train_acc:.4f}")
        print(f"    测试准确率: {test_acc:.4f}")

    # 可视化性能随树数量的变化
    plt.figure(figsize=(10, 6))
    plt.plot(n_estimators_list, train_accuracies, 'o-', label='训练准确率', linewidth=2)
    plt.plot(n_estimators_list, test_accuracies, 's-', label='测试准确率', linewidth=2)
    plt.xlabel('树的数量')
    plt.ylabel('准确率')
    plt.title('随机森林性能随树数量的变化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# 运行演示
demonstrate_tree_models()
```

---

# 第七章：无监督学习算法全解析

> **核心理念**: 无监督学习从无标签数据中发现隐藏的模式和结构，是数据挖掘和知识发现的重要工具。

## 7.1 专业术语详解

### 7.1.1 聚类分析术语

**聚类 (Clustering)**
- **定义**: 将相似的数据点分组到同一个簇中，不相似的数据点分到不同簇中
- **目标**: 最大化簇内相似性，最小化簇间相似性
- **类型**:
  - **硬聚类**: 每个数据点只属于一个簇
  - **软聚类**: 每个数据点可以属于多个簇（带概率）
  - **层次聚类**: 构建簇的层次结构
  - **密度聚类**: 基于数据密度进行聚类
- **应用场景**: 客户分群、基因分析、图像分割、推荐系统

**相似性度量 (Similarity Measures)**
- **欧几里得距离**: $d(x,y) = \sqrt{\sum_{i=1}^n (x_i - y_i)^2}$
- **曼哈顿距离**: $d(x,y) = \sum_{i=1}^n |x_i - y_i|$
- **余弦相似度**: $\cos(\theta) = \frac{x \cdot y}{||x|| \cdot ||y||}$
- **杰卡德系数**: $J(A,B) = \frac{|A \cap B|}{|A \cup B|}$
- **马氏距离**: $d(x,y) = \sqrt{(x-y)^T S^{-1} (x-y)}$，其中S是协方差矩阵
- **汉明距离**: 用于分类变量，计算不同位置的数量

**K-Means聚类**
- **算法步骤**:
  1. 随机初始化k个聚类中心
  2. 将每个点分配给最近的聚类中心
  3. 更新聚类中心为簇内点的均值
  4. 重复步骤2-3直到收敛
- **目标函数**: $J = \sum_{i=1}^k \sum_{x \in C_i} ||x - \mu_i||^2$
- **K-Means++初始化**: 改进的初始化方法，选择距离已有中心较远的点
- **优势**: 简单高效，适合球形簇
- **缺点**: 需要预设k值，对初始化敏感，假设簇为球形

**DBSCAN聚类**
- **核心概念**:
  - **ε-邻域**: 距离点p不超过ε的所有点的集合
  - **核心点**: ε-邻域内至少包含MinPts个点的点
  - **边界点**: 不是核心点但在某个核心点的ε-邻域内
  - **噪声点**: 既不是核心点也不是边界点
- **算法特点**:
  - 可以发现任意形状的簇
  - 自动确定簇的数量
  - 能够识别噪声点
  - 对参数ε和MinPts敏感

**层次聚类 (Hierarchical Clustering)**
- **凝聚式聚类 (Agglomerative)**:
  - 自底向上：每个点开始为一个簇，逐步合并
  - 链接准则：
    - **单链接**: 最近点间距离
    - **全链接**: 最远点间距离
    - **平均链接**: 平均距离
    - **Ward链接**: 最小化方差增加
- **分裂式聚类 (Divisive)**:
  - 自顶向下：从一个大簇开始，逐步分割
- **树状图 (Dendrogram)**: 可视化聚类层次结构
- **优势**: 不需要预设簇数，提供层次结构
- **缺点**: 时间复杂度高O(n³)，对噪声敏感

### 7.1.2 降维技术术语

**维数灾难 (Curse of Dimensionality)**
- **定义**: 高维空间中数据分析面临的困难
- **表现**:
  - 数据稀疏性增加
  - 距离度量失效
  - 计算复杂度指数增长
  - 可视化困难
- **解决方案**: 降维技术、特征选择、正则化

**主成分分析 (Principal Component Analysis, PCA)**
- **目标**: 找到数据方差最大的方向作为主成分
- **数学原理**:
  - 协方差矩阵特征值分解
  - 主成分 = 特征向量
  - 方差贡献 = 特征值
- **算法步骤**:
  1. 数据中心化
  2. 计算协方差矩阵
  3. 特征值分解
  4. 选择前k个主成分
  5. 投影数据到新空间
- **解释方差比**: 每个主成分解释的方差占总方差的比例
- **应用**: 数据可视化、特征提取、噪声去除

**t-SNE (t-Distributed Stochastic Neighbor Embedding)**
- **目标**: 保持数据点间的局部邻域关系
- **核心思想**:
  - 在高维空间计算点间相似性
  - 在低维空间寻找相似的分布
  - 最小化两个分布间的KL散度
- **特点**:
  - 非线性降维
  - 擅长保持局部结构
  - 适合数据可视化
  - 计算复杂度高
- **参数**:
  - **困惑度 (Perplexity)**: 控制局部邻域大小
  - **学习率**: 控制优化步长

**独立成分分析 (Independent Component Analysis, ICA)**
- **目标**: 寻找统计独立的成分
- **假设**: 观测信号是独立源信号的线性混合
- **数学模型**: $X = AS$，其中A是混合矩阵，S是独立源
- **应用**: 盲源分离、信号处理、脑电图分析
- **与PCA区别**: PCA寻找不相关成分，ICA寻找独立成分

### 7.1.3 关联规则术语

**关联规则挖掘 (Association Rule Mining)**
- **定义**: 发现数据项之间的关联关系
- **规则形式**: X → Y（如果X则Y）
- **经典应用**: 购物篮分析、推荐系统

**支持度 (Support)**
- **定义**: 项集在数据集中出现的频率
- **计算**: $Support(X) = \frac{包含X的事务数}{总事务数}$
- **意义**: 衡量规则的普遍性

**置信度 (Confidence)**
- **定义**: 在包含X的事务中也包含Y的比例
- **计算**: $Confidence(X \rightarrow Y) = \frac{Support(X \cup Y)}{Support(X)}$
- **意义**: 衡量规则的可靠性

**提升度 (Lift)**
- **定义**: 规则的置信度与Y的支持度的比值
- **计算**: $Lift(X \rightarrow Y) = \frac{Confidence(X \rightarrow Y)}{Support(Y)}$
- **意义**:
  - Lift > 1: 正相关
  - Lift = 1: 独立
  - Lift < 1: 负相关

**Apriori算法**
- **核心思想**: 频繁项集的子集也是频繁的
- **算法步骤**:
  1. 找出所有频繁1-项集
  2. 由频繁k-项集生成候选(k+1)-项集
  3. 扫描数据库，计算候选项集支持度
  4. 删除不频繁项集
  5. 重复直到无新的频繁项集
- **优化**: 剪枝策略减少候选项集数量

## 7.2 无监督学习算法实现

### 7.2.1 聚类算法实现

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_blobs, make_circles, make_moons
from sklearn.preprocessing import StandardScaler
from collections import defaultdict
import seaborn as sns

class KMeans:
    """K-Means聚类算法的完整实现"""

    def __init__(self, n_clusters=3, max_iters=100, tol=1e-4, init='k-means++', random_state=None):
        """
        参数:
        - n_clusters: 聚类数量
        - max_iters: 最大迭代次数
        - tol: 收敛容忍度
        - init: 初始化方法 ('random', 'k-means++')
        - random_state: 随机种子
        """
        self.n_clusters = n_clusters
        self.max_iters = max_iters
        self.tol = tol
        self.init = init
        self.random_state = random_state

        self.centroids = None
        self.labels = None
        self.inertia = None
        self.n_iter = None

        if random_state:
            np.random.seed(random_state)

    def _init_centroids(self, X):
        """初始化聚类中心"""
        n_samples, n_features = X.shape

        if self.init == 'random':
            # 随机初始化
            self.centroids = np.random.randn(self.n_clusters, n_features)

        elif self.init == 'k-means++':
            # K-Means++初始化
            self.centroids = np.zeros((self.n_clusters, n_features))

            # 随机选择第一个中心
            self.centroids[0] = X[np.random.randint(n_samples)]

            # 选择剩余的中心
            for i in range(1, self.n_clusters):
                # 计算每个点到最近中心的距离
                distances = np.array([min([np.linalg.norm(x - c)**2 for c in self.centroids[:i]])
                                    for x in X])

                # 按距离的概率选择下一个中心
                probabilities = distances / distances.sum()
                cumulative_probs = probabilities.cumsum()
                r = np.random.rand()

                for j, p in enumerate(cumulative_probs):
                    if r < p:
                        self.centroids[i] = X[j]
                        break
        else:
            raise ValueError(f"不支持的初始化方法: {self.init}")

    def _assign_clusters(self, X):
        """分配数据点到最近的聚类中心"""
        distances = np.sqrt(((X - self.centroids[:, np.newaxis])**2).sum(axis=2))
        return np.argmin(distances, axis=0)

    def _update_centroids(self, X, labels):
        """更新聚类中心"""
        new_centroids = np.zeros_like(self.centroids)

        for i in range(self.n_clusters):
            cluster_points = X[labels == i]
            if len(cluster_points) > 0:
                new_centroids[i] = cluster_points.mean(axis=0)
            else:
                # 如果簇为空，保持原中心
                new_centroids[i] = self.centroids[i]

        return new_centroids

    def _calculate_inertia(self, X, labels):
        """计算簇内平方和"""
        inertia = 0
        for i in range(self.n_clusters):
            cluster_points = X[labels == i]
            if len(cluster_points) > 0:
                inertia += np.sum((cluster_points - self.centroids[i])**2)
        return inertia

    def fit(self, X):
        """训练K-Means模型"""

        # 初始化聚类中心
        self._init_centroids(X)

        print(f"开始K-Means聚类:")
        print(f"  聚类数: {self.n_clusters}")
        print(f"  初始化方法: {self.init}")
        print(f"  最大迭代次数: {self.max_iters}")

        # 迭代优化
        for iteration in range(self.max_iters):
            # 分配聚类
            labels = self._assign_clusters(X)

            # 更新聚类中心
            new_centroids = self._update_centroids(X, labels)

            # 检查收敛
            centroid_shift = np.linalg.norm(new_centroids - self.centroids)

            if centroid_shift < self.tol:
                print(f"  在第 {iteration + 1} 次迭代后收敛")
                break

            self.centroids = new_centroids

            if (iteration + 1) % 10 == 0:
                inertia = self._calculate_inertia(X, labels)
                print(f"  迭代 {iteration + 1}: 簇内平方和 = {inertia:.4f}")

        # 保存最终结果
        self.labels = labels
        self.inertia = self._calculate_inertia(X, labels)
        self.n_iter = iteration + 1

        print(f"聚类完成! 最终簇内平方和: {self.inertia:.4f}")

    def predict(self, X):
        """预测新数据点的聚类"""
        return self._assign_clusters(X)

    def fit_predict(self, X):
        """训练并预测"""
        self.fit(X)
        return self.labels

class DBSCAN:
    """DBSCAN聚类算法的完整实现"""

    def __init__(self, eps=0.5, min_samples=5):
        """
        参数:
        - eps: 邻域半径
        - min_samples: 核心点的最小邻居数
        """
        self.eps = eps
        self.min_samples = min_samples
        self.labels = None
        self.core_sample_indices = None

    def _get_neighbors(self, X, point_idx):
        """获取点的邻居"""
        distances = np.linalg.norm(X - X[point_idx], axis=1)
        return np.where(distances <= self.eps)[0]

    def fit_predict(self, X):
        """训练并预测DBSCAN聚类"""
        n_samples = X.shape[0]

        # 初始化标签（-1表示噪声点）
        labels = np.full(n_samples, -1)

        # 标记核心点
        core_samples = []

        # 找出所有核心点
        for i in range(n_samples):
            neighbors = self._get_neighbors(X, i)
            if len(neighbors) >= self.min_samples:
                core_samples.append(i)

        self.core_sample_indices = np.array(core_samples)

        print(f"DBSCAN聚类:")
        print(f"  邻域半径 (eps): {self.eps}")
        print(f"  最小样本数: {self.min_samples}")
        print(f"  核心点数量: {len(core_samples)}")

        # 聚类编号
        cluster_id = 0

        # 对每个核心点进行聚类
        for core_point in core_samples:
            # 如果已经被分配到某个簇，跳过
            if labels[core_point] != -1:
                continue

            # 开始新的簇
            labels[core_point] = cluster_id

            # 获取核心点的邻居
            neighbors = self._get_neighbors(X, core_point)

            # 深度优先搜索扩展簇
            i = 0
            while i < len(neighbors):
                neighbor = neighbors[i]

                # 如果是噪声点，将其加入当前簇
                if labels[neighbor] == -1:
                    labels[neighbor] = cluster_id

                # 如果邻居也是核心点，扩展搜索
                if neighbor in core_samples and labels[neighbor] == -1:
                    labels[neighbor] = cluster_id
                    new_neighbors = self._get_neighbors(X, neighbor)
                    neighbors = np.concatenate([neighbors, new_neighbors])

                i += 1

            cluster_id += 1

        self.labels = labels

        # 统计结果
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        n_noise = list(labels).count(-1)

        print(f"  发现簇数: {n_clusters}")
        print(f"  噪声点数: {n_noise}")

        return labels

class HierarchicalClustering:
    """层次聚类算法的完整实现"""

    def __init__(self, n_clusters=3, linkage='ward'):
        """
        参数:
        - n_clusters: 最终聚类数
        - linkage: 链接准则 ('single', 'complete', 'average', 'ward')
        """
        self.n_clusters = n_clusters
        self.linkage = linkage
        self.labels = None
        self.linkage_matrix = None

    def _calculate_distance(self, cluster1, cluster2, X):
        """计算两个簇之间的距离"""
        points1 = X[cluster1]
        points2 = X[cluster2]

        if self.linkage == 'single':
            # 单链接：最近点间距离
            distances = np.linalg.norm(points1[:, np.newaxis] - points2, axis=2)
            return np.min(distances)

        elif self.linkage == 'complete':
            # 全链接：最远点间距离
            distances = np.linalg.norm(points1[:, np.newaxis] - points2, axis=2)
            return np.max(distances)

        elif self.linkage == 'average':
            # 平均链接：平均距离
            distances = np.linalg.norm(points1[:, np.newaxis] - points2, axis=2)
            return np.mean(distances)

        elif self.linkage == 'ward':
            # Ward链接：最小化方差增加
            centroid1 = np.mean(points1, axis=0)
            centroid2 = np.mean(points2, axis=0)

            # 合并后的质心
            merged_centroid = (len(points1) * centroid1 + len(points2) * centroid2) / (len(points1) + len(points2))

            # 计算方差增加
            var_increase = len(points1) * np.sum((centroid1 - merged_centroid)**2) + \
                          len(points2) * np.sum((centroid2 - merged_centroid)**2)

            return var_increase

        else:
            raise ValueError(f"不支持的链接准则: {self.linkage}")

    def fit_predict(self, X):
        """训练并预测层次聚类"""
        n_samples = X.shape[0]

        print(f"层次聚类:")
        print(f"  目标聚类数: {self.n_clusters}")
        print(f"  链接准则: {self.linkage}")

        # 初始化：每个点为一个簇
        clusters = [[i] for i in range(n_samples)]
        linkage_matrix = []

        # 合并簇直到达到目标数量
        while len(clusters) > self.n_clusters:
            min_distance = float('inf')
            merge_i, merge_j = -1, -1

            # 找到距离最近的两个簇
            for i in range(len(clusters)):
                for j in range(i + 1, len(clusters)):
                    distance = self._calculate_distance(clusters[i], clusters[j], X)

                    if distance < min_distance:
                        min_distance = distance
                        merge_i, merge_j = i, j

            # 记录合并信息
            linkage_matrix.append([merge_i, merge_j, min_distance, len(clusters[merge_i]) + len(clusters[merge_j])])

            # 合并簇
            merged_cluster = clusters[merge_i] + clusters[merge_j]

            # 移除原来的簇（从后往前删除避免索引问题）
            if merge_i > merge_j:
                clusters.pop(merge_i)
                clusters.pop(merge_j)
            else:
                clusters.pop(merge_j)
                clusters.pop(merge_i)

            # 添加合并后的簇
            clusters.append(merged_cluster)

            if len(clusters) % 50 == 0:
                print(f"  剩余簇数: {len(clusters)}")

        # 分配标签
        labels = np.zeros(n_samples, dtype=int)
        for cluster_id, cluster in enumerate(clusters):
            for point_idx in cluster:
                labels[point_idx] = cluster_id

        self.labels = labels
        self.linkage_matrix = np.array(linkage_matrix)

        print(f"层次聚类完成!")

        return labels

# 演示聚类算法
def demonstrate_clustering_algorithms():
    """演示不同的聚类算法"""

    print("=== 聚类算法演示 ===")

    # 生成不同类型的数据集
    datasets = []

    # 1. 球形簇数据
    X1, y1 = make_blobs(n_samples=300, centers=4, cluster_std=0.60, random_state=0)
    datasets.append((X1, "球形簇数据"))

    # 2. 月牙形数据
    X2, y2 = make_moons(n_samples=300, noise=0.1, random_state=0)
    datasets.append((X2, "月牙形数据"))

    # 3. 同心圆数据
    X3, y3 = make_circles(n_samples=300, noise=0.05, factor=0.6, random_state=0)
    datasets.append((X3, "同心圆数据"))

    # 聚类算法
    algorithms = [
        (KMeans(n_clusters=4, random_state=42), "K-Means"),
        (DBSCAN(eps=0.3, min_samples=10), "DBSCAN"),
        (HierarchicalClustering(n_clusters=4, linkage='ward'), "层次聚类")
    ]

    # 可视化结果
    fig, axes = plt.subplots(len(datasets), len(algorithms), figsize=(15, 12))

    for i, (X, data_name) in enumerate(datasets):
        # 数据标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        for j, (algorithm, alg_name) in enumerate(algorithms):
            # 训练聚类算法
            if hasattr(algorithm, 'fit_predict'):
                labels = algorithm.fit_predict(X_scaled)
            else:
                algorithm.fit(X_scaled)
                labels = algorithm.labels

            # 绘制结果
            ax = axes[i, j]

            # 绘制数据点
            unique_labels = set(labels)
            colors = plt.cm.Spectral(np.linspace(0, 1, len(unique_labels)))

            for k, col in zip(unique_labels, colors):
                if k == -1:
                    # 噪声点用黑色表示
                    col = 'black'
                    marker = 'x'
                else:
                    marker = 'o'

                class_member_mask = (labels == k)
                xy = X_scaled[class_member_mask]
                ax.scatter(xy[:, 0], xy[:, 1], c=[col], marker=marker, s=50, alpha=0.7)

            # 如果是K-Means，绘制聚类中心
            if alg_name == "K-Means" and hasattr(algorithm, 'centroids'):
                ax.scatter(algorithm.centroids[:, 0], algorithm.centroids[:, 1],
                          c='red', marker='x', s=200, linewidths=3, label='中心点')

            ax.set_title(f'{alg_name}\n{data_name}')
            ax.set_xlabel('特征 1')
            ax.set_ylabel('特征 2')
            ax.grid(True, alpha=0.3)

            # 统计聚类结果
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            n_noise = list(labels).count(-1) if -1 in labels else 0

            info_text = f'簇数: {n_clusters}'
            if n_noise > 0:
                info_text += f'\n噪声: {n_noise}'

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.show()

# 运行演示
demonstrate_clustering_algorithms()
```

### 7.2.2 降维算法实现

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import load_digits, make_swiss_roll

class PrincipalComponentAnalysis:
    """主成分分析的完整实现"""

    def __init__(self, n_components=None):
        """
        PCA核心概念：
        1. 方差最大化：寻找数据方差最大的方向
        2. 特征值分解：通过协方差矩阵的特征值分解实现
        3. 降维：将高维数据投影到低维空间
        4. 信息保留：尽可能保留原始数据的信息
        """
        self.n_components = n_components
        self.components = None
        self.explained_variance = None
        self.explained_variance_ratio = None
        self.mean = None

    def fit(self, X):
        """训练PCA模型"""
        n_samples, n_features = X.shape

        # 数据中心化
        self.mean = np.mean(X, axis=0)
        X_centered = X - self.mean

        # 计算协方差矩阵
        cov_matrix = np.cov(X_centered.T)

        # 特征值分解
        eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)

        # 按特征值降序排列
        idx = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]

        # 选择主成分数量
        if self.n_components is None:
            self.n_components = n_features

        # 保存主成分
        self.components = eigenvectors[:, :self.n_components].T
        self.explained_variance = eigenvalues[:self.n_components]
        self.explained_variance_ratio = self.explained_variance / np.sum(eigenvalues)

        print(f"PCA训练完成:")
        print(f"  原始维度: {n_features}")
        print(f"  降维后维度: {self.n_components}")
        print(f"  解释方差比: {self.explained_variance_ratio}")
        print(f"  累积解释方差比: {np.cumsum(self.explained_variance_ratio)}")

    def transform(self, X):
        """将数据投影到主成分空间"""
        X_centered = X - self.mean
        return X_centered @ self.components.T

    def inverse_transform(self, X_transformed):
        """从主成分空间重构原始数据"""
        return X_transformed @ self.components + self.mean

    def fit_transform(self, X):
        """训练并转换数据"""
        self.fit(X)
        return self.transform(X)

class tSNE:
    """t-SNE的简化实现"""

    def __init__(self, n_components=2, perplexity=30, learning_rate=200, n_iter=1000):
        """
        t-SNE核心概念：
        1. 概率分布：将距离转换为概率分布
        2. 对称SNE：使用对称的概率分布
        3. t分布：在低维空间使用t分布避免拥挤问题
        4. 梯度下降：通过优化KL散度学习低维表示
        """
        self.n_components = n_components
        self.perplexity = perplexity
        self.learning_rate = learning_rate
        self.n_iter = n_iter
        self.embedding = None

    def _compute_pairwise_distances(self, X):
        """计算成对距离"""
        n = X.shape[0]
        distances = np.zeros((n, n))

        for i in range(n):
            for j in range(i+1, n):
                dist = np.linalg.norm(X[i] - X[j])
                distances[i, j] = dist
                distances[j, i] = dist

        return distances

    def _compute_p_conditional(self, distances, sigma):
        """计算条件概率P(j|i)"""
        n = distances.shape[0]
        P = np.zeros((n, n))

        for i in range(n):
            # 计算P(j|i) = exp(-||xi - xj||^2 / 2σi^2) / Σk≠i exp(-||xi - xk||^2 / 2σi^2)
            numerator = np.exp(-distances[i] ** 2 / (2 * sigma[i] ** 2))
            numerator[i] = 0  # P(i|i) = 0

            denominator = np.sum(numerator)
            if denominator > 0:
                P[i] = numerator / denominator

        return P

    def _binary_search_sigma(self, distances, target_perplexity, tol=1e-5):
        """二分搜索找到合适的σ值"""
        n = distances.shape[0]
        sigmas = np.ones(n)

        for i in range(n):
            beta_min, beta_max = -np.inf, np.inf
            beta = 1.0

            for _ in range(50):  # 最大迭代次数
                # 计算当前β下的概率和熵
                numerator = np.exp(-distances[i] * beta)
                numerator[i] = 0

                denominator = np.sum(numerator)
                if denominator == 0:
                    break

                P_i = numerator / denominator

                # 计算熵和困惑度
                entropy = -np.sum(P_i * np.log2(P_i + 1e-8))
                perplexity = 2 ** entropy

                # 检查是否收敛
                if abs(perplexity - target_perplexity) < tol:
                    break

                # 调整β
                if perplexity > target_perplexity:
                    beta_min = beta
                    beta = (beta + beta_max) / 2 if beta_max != np.inf else beta * 2
                else:
                    beta_max = beta
                    beta = (beta + beta_min) / 2 if beta_min != -np.inf else beta / 2

            sigmas[i] = np.sqrt(1 / (2 * beta))

        return sigmas

    def fit_transform(self, X):
        """训练t-SNE并返回低维嵌入"""
        n_samples, n_features = X.shape

        print(f"t-SNE开始训练:")
        print(f"  样本数: {n_samples}")
        print(f"  原始维度: {n_features}")
        print(f"  目标维度: {self.n_components}")
        print(f"  困惑度: {self.perplexity}")

        # 计算高维空间的成对距离
        distances = self._compute_pairwise_distances(X)

        # 寻找合适的σ值
        sigmas = self._binary_search_sigma(distances, self.perplexity)

        # 计算高维空间的概率分布P
        P_conditional = self._compute_p_conditional(distances, sigmas)
        P = (P_conditional + P_conditional.T) / (2 * n_samples)  # 对称化
        P = np.maximum(P, 1e-12)  # 避免数值问题

        # 初始化低维嵌入
        self.embedding = np.random.randn(n_samples, self.n_components) * 1e-4

        # 梯度下降优化
        for iteration in range(self.n_iter):
            # 计算低维空间的概率分布Q
            distances_low = self._compute_pairwise_distances(self.embedding)

            # 使用t分布
            numerator = 1 / (1 + distances_low ** 2)
            np.fill_diagonal(numerator, 0)

            Q = numerator / np.sum(numerator)
            Q = np.maximum(Q, 1e-12)

            # 计算梯度
            PQ_diff = P - Q
            gradient = np.zeros_like(self.embedding)

            for i in range(n_samples):
                diff = self.embedding[i] - self.embedding
                gradient[i] = 4 * np.sum(
                    (PQ_diff[i] * numerator[i] / (1 + distances_low[i] ** 2))[:, np.newaxis] * diff,
                    axis=0
                )

            # 更新嵌入
            self.embedding -= self.learning_rate * gradient

            # 打印进度
            if (iteration + 1) % 100 == 0:
                kl_divergence = np.sum(P * np.log(P / Q))
                print(f"  迭代 {iteration + 1}: KL散度 = {kl_divergence:.4f}")

        print("t-SNE训练完成!")
        return self.embedding

# 演示降维算法
def demonstrate_dimensionality_reduction():
    """演示降维算法"""

    print("=== 降维算法演示 ===")

    # 场景1：PCA在手写数字数据上的应用
    print("\n场景1：PCA降维 - 手写数字数据")

    # 加载手写数字数据
    digits = load_digits()
    X_digits = digits.data  # 8x8=64维
    y_digits = digits.target

    print(f"原始数据:")
    print(f"  样本数: {X_digits.shape[0]}")
    print(f"  特征数: {X_digits.shape[1]}")
    print(f"  类别数: {len(np.unique(y_digits))}")

    # 应用PCA
    pca = PrincipalComponentAnalysis(n_components=10)
    X_pca = pca.fit_transform(X_digits)

    print(f"\nPCA降维结果:")
    print(f"  降维后维度: {X_pca.shape[1]}")
    print(f"  前10个主成分解释方差比: {pca.explained_variance_ratio}")
    print(f"  累积解释方差比: {np.cumsum(pca.explained_variance_ratio)[-1]:.3f}")

    # 重构误差分析
    X_reconstructed = pca.inverse_transform(X_pca)
    reconstruction_error = np.mean((X_digits - X_reconstructed) ** 2)
    print(f"  重构误差 (MSE): {reconstruction_error:.6f}")

    # 可视化不同主成分数量的效果
    plt.figure(figsize=(15, 10))

    # 原始图像
    plt.subplot(2, 4, 1)
    plt.imshow(X_digits[0].reshape(8, 8), cmap='gray')
    plt.title('原始图像')
    plt.axis('off')

    # 不同主成分数量的重构
    n_components_list = [1, 2, 5, 10, 20, 30, 50]

    for i, n_comp in enumerate(n_components_list):
        pca_temp = PrincipalComponentAnalysis(n_components=n_comp)
        X_temp = pca_temp.fit_transform(X_digits)
        X_recon = pca_temp.inverse_transform(X_temp)

        plt.subplot(2, 4, i+2)
        plt.imshow(X_recon[0].reshape(8, 8), cmap='gray')
        plt.title(f'{n_comp}个主成分\n解释方差: {np.sum(pca_temp.explained_variance_ratio):.3f}')
        plt.axis('off')

    plt.tight_layout()
    plt.show()

    # 场景2：t-SNE可视化
    print(f"\n场景2：t-SNE可视化 - 高维数据可视化")

    # 使用部分数字数据进行t-SNE（计算量大）
    n_samples = 500
    indices = np.random.choice(len(X_digits), n_samples, replace=False)
    X_sample = X_digits[indices]
    y_sample = y_digits[indices]

    print(f"t-SNE可视化数据:")
    print(f"  样本数: {len(X_sample)}")
    print(f"  原始维度: {X_sample.shape[1]}")

    # 先用PCA降到50维（预处理）
    pca_preprocess = PrincipalComponentAnalysis(n_components=50)
    X_pca_prep = pca_preprocess.fit_transform(X_sample)

    # 应用t-SNE
    tsne = tSNE(n_components=2, perplexity=30, learning_rate=200, n_iter=300)
    X_tsne = tsne.fit_transform(X_pca_prep)

    # 可视化t-SNE结果
    plt.figure(figsize=(12, 8))

    # t-SNE结果
    plt.subplot(1, 2, 1)
    scatter = plt.scatter(X_tsne[:, 0], X_tsne[:, 1], c=y_sample, cmap='tab10', alpha=0.7)
    plt.colorbar(scatter)
    plt.title('t-SNE可视化结果')
    plt.xlabel('t-SNE 1')
    plt.ylabel('t-SNE 2')

    # PCA前两个主成分
    pca_2d = PrincipalComponentAnalysis(n_components=2)
    X_pca_2d = pca_2d.fit_transform(X_sample)

    plt.subplot(1, 2, 2)
    scatter = plt.scatter(X_pca_2d[:, 0], X_pca_2d[:, 1], c=y_sample, cmap='tab10', alpha=0.7)
    plt.colorbar(scatter)
    plt.title('PCA前两个主成分')
    plt.xlabel('PC1')
    plt.ylabel('PC2')

    plt.tight_layout()
    plt.show()

    # 分析聚类效果
    print(f"\n降维效果分析:")

    # 计算类内距离和类间距离
    def compute_cluster_metrics(X_embedded, labels):
        """计算聚类质量指标"""
        unique_labels = np.unique(labels)
        intra_distances = []
        inter_distances = []

        for label in unique_labels:
            mask = labels == label
            cluster_points = X_embedded[mask]

            # 类内距离
            if len(cluster_points) > 1:
                centroid = np.mean(cluster_points, axis=0)
                intra_dist = np.mean([np.linalg.norm(point - centroid) for point in cluster_points])
                intra_distances.append(intra_dist)

            # 类间距离
            other_points = X_embedded[~mask]
            if len(other_points) > 0:
                cluster_centroid = np.mean(cluster_points, axis=0)
                other_centroid = np.mean(other_points, axis=0)
                inter_dist = np.linalg.norm(cluster_centroid - other_centroid)
                inter_distances.append(inter_dist)

        return np.mean(intra_distances), np.mean(inter_distances)

    # PCA聚类质量
    pca_intra, pca_inter = compute_cluster_metrics(X_pca_2d, y_sample)
    print(f"PCA聚类质量:")
    print(f"  平均类内距离: {pca_intra:.3f}")
    print(f"  平均类间距离: {pca_inter:.3f}")
    print(f"  类间/类内比值: {pca_inter/pca_intra:.3f}")

    # t-SNE聚类质量
    tsne_intra, tsne_inter = compute_cluster_metrics(X_tsne, y_sample)
    print(f"t-SNE聚类质量:")
    print(f"  平均类内距离: {tsne_intra:.3f}")
    print(f"  平均类间距离: {tsne_inter:.3f}")
    print(f"  类间/类内比值: {tsne_inter/tsne_intra:.3f}")

# 运行降维演示
demonstrate_dimensionality_reduction()
```

---

# 第八章：集成学习与模型融合

> **核心理念**: 集成学习通过组合多个学习器的预测来获得比单个学习器更好的性能，是提高模型准确性和鲁棒性的重要方法。

## 8.1 专业术语详解

### 8.1.1 集成学习基础概念

**集成学习 (Ensemble Learning)**
- **定义**: 通过构建并结合多个学习器来完成学习任务
- **核心思想**: "三个臭皮匠，顶个诸葛亮"
- **理论基础**:
  - **偏差-方差分解**: 集成可以同时减少偏差和方差
  - **多样性**: 个体学习器之间的差异性是关键
- **优势**:
  - 提高预测准确性
  - 增强模型鲁棒性
  - 减少过拟合风险
- **应用场景**: 几乎所有机器学习任务

**个体学习器 (Base Learner)**
- **定义**: 集成中的单个学习算法
- **要求**:
  - **准确性**: 性能要好于随机猜测
  - **多样性**: 不同学习器应有不同的错误模式
- **类型**:
  - **同质集成**: 使用相同类型的学习器
  - **异质集成**: 使用不同类型的学习器

**多样性 (Diversity)**
- **定义**: 个体学习器之间预测差异的度量
- **重要性**: 多样性是集成学习成功的关键
- **度量方法**:
  - **不一致度量**: 衡量分类器间的分歧
  - **相关系数**: 衡量预测结果的相关性
  - **Q统计量**: 衡量两个分类器的相关性
- **产生方式**:
  - 数据样本多样性
  - 输入特征多样性
  - 输出表示多样性
  - 参数多样性

### 8.1.2 集成策略术语

**Bagging (Bootstrap Aggregating)**
- **定义**: 基于自助采样的集成方法
- **算法流程**:
  1. 从原始训练集中有放回地采样，生成多个子训练集
  2. 在每个子训练集上训练一个基学习器
  3. 将所有基学习器的预测结果进行平均或投票
- **理论分析**:
  - 主要减少方差
  - 对不稳定学习器效果显著
- **代表算法**: 随机森林
- **优势**: 可并行训练、减少过拟合

**Boosting**
- **定义**: 基于错误驱动的集成方法
- **核心思想**: 序列化训练，后续学习器专注于前面学习器的错误
- **算法特点**:
  - 串行训练过程
  - 自适应调整样本权重
  - 强调困难样本
- **理论分析**:
  - 主要减少偏差
  - 可能增加方差
- **代表算法**: AdaBoost、梯度提升树

**Stacking (Stacked Generalization)**
- **定义**: 使用元学习器组合基学习器的方法
- **两层结构**:
  - **第一层**: 多个基学习器
  - **第二层**: 元学习器（学习如何组合基学习器）
- **训练过程**:
  1. 训练基学习器
  2. 使用基学习器的预测作为元学习器的输入
  3. 训练元学习器
- **优势**: 理论上可以学习最优组合方式

### 8.1.3 具体算法术语

**AdaBoost (Adaptive Boosting)**
- **定义**: 自适应提升算法，第一个实用的boosting算法
- **核心机制**:
  - **样本权重调整**: 增加错误分类样本的权重
  - **学习器权重**: 根据错误率确定学习器重要性
- **算法步骤**:
  1. 初始化样本权重
  2. 训练弱学习器
  3. 计算学习器权重
  4. 更新样本权重
  5. 重复直到达到停止条件
- **理论保证**: 训练误差指数下降
- **适用场景**: 二分类问题（可扩展到多分类）

**梯度提升 (Gradient Boosting)**
- **定义**: 基于梯度下降的boosting方法
- **核心思想**: 每个新学习器拟合前面模型的负梯度
- **算法框架**:
  1. 初始化模型
  2. 计算负梯度（残差）
  3. 训练新学习器拟合负梯度
  4. 更新模型
  5. 重复直到收敛
- **优势**:
  - 可处理各种损失函数
  - 理论基础扎实
  - 性能优秀

**XGBoost (eXtreme Gradient Boosting)**
- **定义**: 梯度提升的高效实现
- **创新点**:
  - **二阶梯度**: 使用二阶导数信息
  - **正则化**: 内置L1和L2正则化
  - **并行化**: 特征级别的并行化
  - **缺失值处理**: 自动学习缺失值的最佳分割方向
- **工程优化**:
  - 近似算法加速训练
  - 缓存优化
  - 块结构存储
- **应用**: 在各种机器学习竞赛中表现优异

## 8.2 集成学习算法实现

### 8.2.1 Bagging算法实现

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification, make_regression
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
from collections import Counter

class BaggingEnsemble:
    """Bagging集成算法的完整实现"""

    def __init__(self, base_estimator, n_estimators=10, max_samples=1.0,
                 max_features=1.0, bootstrap=True, random_state=None):
        """
        Bagging核心概念：
        1. 自助采样：有放回地采样生成多个训练集
        2. 并行训练：独立训练多个基学习器
        3. 投票/平均：组合多个学习器的预测
        4. 方差减少：通过平均减少预测方差
        """
        self.base_estimator = base_estimator
        self.n_estimators = n_estimators
        self.max_samples = max_samples
        self.max_features = max_features
        self.bootstrap = bootstrap
        self.random_state = random_state

        self.estimators = []
        self.estimators_samples = []
        self.estimators_features = []

        if random_state:
            np.random.seed(random_state)

    def _bootstrap_sample(self, X, y):
        """生成自助采样"""
        n_samples = X.shape[0]

        if self.bootstrap:
            # 有放回采样
            if isinstance(self.max_samples, float):
                n_samples_bootstrap = int(self.max_samples * n_samples)
            else:
                n_samples_bootstrap = min(self.max_samples, n_samples)

            indices = np.random.choice(n_samples, n_samples_bootstrap, replace=True)
        else:
            # 无放回采样
            if isinstance(self.max_samples, float):
                n_samples_bootstrap = int(self.max_samples * n_samples)
            else:
                n_samples_bootstrap = min(self.max_samples, n_samples)

            indices = np.random.choice(n_samples, n_samples_bootstrap, replace=False)

        return X[indices], y[indices], indices

    def _bootstrap_features(self, X):
        """特征自助采样"""
        n_features = X.shape[1]

        if isinstance(self.max_features, float):
            n_features_bootstrap = int(self.max_features * n_features)
        else:
            n_features_bootstrap = min(self.max_features, n_features)

        feature_indices = np.random.choice(n_features, n_features_bootstrap, replace=False)

        return X[:, feature_indices], feature_indices

    def fit(self, X, y):
        """训练Bagging集成"""

        print(f"开始训练Bagging集成:")
        print(f"  基学习器: {type(self.base_estimator).__name__}")
        print(f"  集成大小: {self.n_estimators}")
        print(f"  样本采样比例: {self.max_samples}")
        print(f"  特征采样比例: {self.max_features}")

        self.estimators = []
        self.estimators_samples = []
        self.estimators_features = []

        for i in range(self.n_estimators):
            # 创建基学习器副本
            estimator = type(self.base_estimator)(**self.base_estimator.get_params())

            # 样本采样
            X_bootstrap, y_bootstrap, sample_indices = self._bootstrap_sample(X, y)

            # 特征采样
            X_bootstrap_features, feature_indices = self._bootstrap_features(X_bootstrap)

            # 训练基学习器
            estimator.fit(X_bootstrap_features, y_bootstrap)

            # 保存学习器和采样信息
            self.estimators.append(estimator)
            self.estimators_samples.append(sample_indices)
            self.estimators_features.append(feature_indices)

            if (i + 1) % max(1, self.n_estimators // 10) == 0:
                print(f"  已训练 {i + 1}/{self.n_estimators} 个基学习器")

        print("Bagging集成训练完成!")

    def predict(self, X):
        """预测"""
        predictions = []

        for estimator, feature_indices in zip(self.estimators, self.estimators_features):
            X_subset = X[:, feature_indices]
            pred = estimator.predict(X_subset)
            predictions.append(pred)

        predictions = np.array(predictions)

        # 判断是分类还是回归
        if hasattr(self.base_estimator, 'predict_proba'):
            # 分类：多数投票
            final_predictions = []
            for i in range(X.shape[0]):
                votes = predictions[:, i]
                final_predictions.append(Counter(votes).most_common(1)[0][0])
            return np.array(final_predictions)
        else:
            # 回归：平均
            return np.mean(predictions, axis=0)

    def predict_proba(self, X):
        """预测概率（仅适用于分类）"""
        if not hasattr(self.base_estimator, 'predict_proba'):
            raise ValueError("基学习器不支持概率预测")

        probabilities = []

        for estimator, feature_indices in zip(self.estimators, self.estimators_features):
            X_subset = X[:, feature_indices]
            prob = estimator.predict_proba(X_subset)
            probabilities.append(prob)

        # 平均概率
        return np.mean(probabilities, axis=0)

    def score(self, X, y):
        """计算准确率或R²分数"""
        predictions = self.predict(X)

        if hasattr(self.base_estimator, 'predict_proba'):
            # 分类：准确率
            return np.mean(predictions == y)
        else:
            # 回归：R²分数
            ss_res = np.sum((y - predictions) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            return 1 - (ss_res / ss_tot)

class AdaBoostClassifier:
    """AdaBoost分类器的完整实现"""

    def __init__(self, base_estimator=None, n_estimators=50, learning_rate=1.0, random_state=None):
        """
        AdaBoost核心概念：
        1. 自适应权重：根据错误调整样本权重
        2. 序列训练：后续学习器关注前面的错误
        3. 加权投票：根据学习器性能确定投票权重
        4. 指数损失：最小化指数损失函数
        """
        self.base_estimator = base_estimator
        self.n_estimators = n_estimators
        self.learning_rate = learning_rate
        self.random_state = random_state

        self.estimators = []
        self.estimator_weights = []
        self.estimator_errors = []

        if random_state:
            np.random.seed(random_state)

    def fit(self, X, y):
        """训练AdaBoost分类器"""

        # 将标签转换为-1和1
        self.classes = np.unique(y)
        if len(self.classes) != 2:
            raise ValueError("AdaBoost只支持二分类")

        y_encoded = np.where(y == self.classes[0], -1, 1)

        print(f"开始训练AdaBoost分类器:")
        print(f"  基学习器: {type(self.base_estimator).__name__ if self.base_estimator else 'DecisionStump'}")
        print(f"  集成大小: {self.n_estimators}")
        print(f"  学习率: {self.learning_rate}")

        n_samples = X.shape[0]

        # 初始化样本权重
        sample_weights = np.ones(n_samples) / n_samples

        self.estimators = []
        self.estimator_weights = []
        self.estimator_errors = []

        for i in range(self.n_estimators):
            # 使用当前样本权重训练基学习器
            if self.base_estimator is None:
                # 使用决策树桩作为默认基学习器
                estimator = DecisionTreeClassifier(max_depth=1, random_state=self.random_state)
            else:
                estimator = type(self.base_estimator)(**self.base_estimator.get_params())

            # 根据样本权重进行采样训练
            sample_indices = np.random.choice(
                n_samples, n_samples, replace=True, p=sample_weights
            )

            estimator.fit(X[sample_indices], y_encoded[sample_indices])

            # 计算预测结果
            predictions = estimator.predict(X)
            predictions = np.where(predictions == self.classes[0], -1, 1)

            # 计算加权错误率
            incorrect = predictions != y_encoded
            error_rate = np.sum(sample_weights * incorrect)

            # 如果错误率为0或大于0.5，停止训练
            if error_rate <= 0:
                self.estimators.append(estimator)
                self.estimator_weights.append(1.0)
                self.estimator_errors.append(error_rate)
                break

            if error_rate >= 0.5:
                if len(self.estimators) == 0:
                    raise ValueError("基学习器性能太差，无法训练AdaBoost")
                break

            # 计算学习器权重
            alpha = self.learning_rate * 0.5 * np.log((1 - error_rate) / error_rate)

            # 更新样本权重
            sample_weights *= np.exp(-alpha * y_encoded * predictions)
            sample_weights /= np.sum(sample_weights)  # 归一化

            # 保存学习器
            self.estimators.append(estimator)
            self.estimator_weights.append(alpha)
            self.estimator_errors.append(error_rate)

            if (i + 1) % max(1, self.n_estimators // 10) == 0:
                print(f"  第 {i + 1} 轮: 错误率 = {error_rate:.4f}, 权重 = {alpha:.4f}")

        print(f"AdaBoost训练完成! 共训练了 {len(self.estimators)} 个基学习器")

    def predict(self, X):
        """预测"""
        if not self.estimators:
            raise ValueError("模型尚未训练")

        # 加权投票
        weighted_predictions = np.zeros(X.shape[0])

        for estimator, weight in zip(self.estimators, self.estimator_weights):
            predictions = estimator.predict(X)
            predictions = np.where(predictions == self.classes[0], -1, 1)
            weighted_predictions += weight * predictions

        # 转换回原始标签
        final_predictions = np.where(weighted_predictions >= 0, self.classes[1], self.classes[0])

        return final_predictions

    def predict_proba(self, X):
        """预测概率"""
        if not self.estimators:
            raise ValueError("模型尚未训练")

        # 计算加权投票得分
        weighted_predictions = np.zeros(X.shape[0])

        for estimator, weight in zip(self.estimators, self.estimator_weights):
            predictions = estimator.predict(X)
            predictions = np.where(predictions == self.classes[0], -1, 1)
            weighted_predictions += weight * predictions

        # 转换为概率
        probabilities = 1 / (1 + np.exp(-2 * weighted_predictions))

        # 返回两个类别的概率
        prob_class_0 = 1 - probabilities
        prob_class_1 = probabilities

        return np.column_stack([prob_class_0, prob_class_1])

    def score(self, X, y):
        """计算准确率"""
        predictions = self.predict(X)
        return np.mean(predictions == y)

# 演示集成学习算法
def demonstrate_ensemble_learning():
    """演示集成学习算法"""

    print("=== 集成学习算法演示 ===")

    # 生成分类数据
    X_class, y_class = make_classification(
        n_samples=1000, n_features=20, n_informative=10, n_redundant=10,
        n_clusters_per_class=1, random_state=42
    )

    X_train_c, X_test_c, y_train_c, y_test_c = train_test_split(
        X_class, y_class, test_size=0.2, random_state=42
    )

    print(f"分类数据集:")
    print(f"  训练样本: {X_train_c.shape[0]}")
    print(f"  测试样本: {X_test_c.shape[0]}")
    print(f"  特征数: {X_train_c.shape[1]}")
    print(f"  类别分布: {np.bincount(y_train_c)}")

    # 1. Bagging演示
    print(f"\n1. Bagging集成演示")

    # 基学习器
    base_tree = DecisionTreeClassifier(max_depth=10, random_state=42)

    # 单个决策树
    single_tree = DecisionTreeClassifier(max_depth=10, random_state=42)
    single_tree.fit(X_train_c, y_train_c)
    single_acc = single_tree.score(X_test_c, y_test_c)

    # Bagging集成
    bagging = BaggingEnsemble(
        base_estimator=base_tree,
        n_estimators=50,
        max_samples=0.8,
        max_features=0.8,
        random_state=42
    )

    bagging.fit(X_train_c, y_train_c)
    bagging_acc = bagging.score(X_test_c, y_test_c)

    print(f"性能比较:")
    print(f"  单个决策树准确率: {single_acc:.4f}")
    print(f"  Bagging集成准确率: {bagging_acc:.4f}")
    print(f"  性能提升: {bagging_acc - single_acc:.4f}")

    # 2. AdaBoost演示
    print(f"\n2. AdaBoost集成演示")

    # AdaBoost集成
    adaboost = AdaBoostClassifier(
        base_estimator=DecisionTreeClassifier(max_depth=1),
        n_estimators=50,
        learning_rate=1.0,
        random_state=42
    )

    adaboost.fit(X_train_c, y_train_c)
    adaboost_acc = adaboost.score(X_test_c, y_test_c)

    print(f"AdaBoost性能:")
    print(f"  AdaBoost准确率: {adaboost_acc:.4f}")
    print(f"  相比单树提升: {adaboost_acc - single_acc:.4f}")

    # 分析AdaBoost的学习过程
    print(f"\nAdaBoost学习过程分析:")
    print(f"  训练的基学习器数量: {len(adaboost.estimators)}")
    print(f"  学习器权重范围: [{min(adaboost.estimator_weights):.3f}, {max(adaboost.estimator_weights):.3f}]")
    print(f"  平均错误率: {np.mean(adaboost.estimator_errors):.4f}")

    # 可视化集成学习效果
    plt.figure(figsize=(15, 10))

    # 学习器权重分布
    plt.subplot(2, 3, 1)
    plt.bar(range(len(adaboost.estimator_weights)), adaboost.estimator_weights)
    plt.title('AdaBoost学习器权重')
    plt.xlabel('学习器索引')
    plt.ylabel('权重')
    plt.grid(True, alpha=0.3)

    # 错误率变化
    plt.subplot(2, 3, 2)
    plt.plot(adaboost.estimator_errors, 'o-')
    plt.title('AdaBoost错误率变化')
    plt.xlabel('学习器索引')
    plt.ylabel('错误率')
    plt.grid(True, alpha=0.3)

    # 集成大小对性能的影响
    plt.subplot(2, 3, 3)

    ensemble_sizes = [1, 5, 10, 20, 30, 40, 50]
    bagging_scores = []
    adaboost_scores = []

    for size in ensemble_sizes:
        # Bagging
        bag_temp = BaggingEnsemble(
            base_estimator=DecisionTreeClassifier(max_depth=10, random_state=42),
            n_estimators=size,
            random_state=42
        )
        bag_temp.fit(X_train_c, y_train_c)
        bagging_scores.append(bag_temp.score(X_test_c, y_test_c))

        # AdaBoost（使用已训练的模型的前size个学习器）
        if size <= len(adaboost.estimators):
            # 临时修改estimators数量来评估
            original_estimators = adaboost.estimators.copy()
            original_weights = adaboost.estimator_weights.copy()

            adaboost.estimators = adaboost.estimators[:size]
            adaboost.estimator_weights = adaboost.estimator_weights[:size]

            adaboost_scores.append(adaboost.score(X_test_c, y_test_c))

            # 恢复原始状态
            adaboost.estimators = original_estimators
            adaboost.estimator_weights = original_weights
        else:
            adaboost_scores.append(adaboost_scores[-1])  # 使用最后一个值

    plt.plot(ensemble_sizes, bagging_scores, 'o-', label='Bagging', linewidth=2)
    plt.plot(ensemble_sizes, adaboost_scores, 's-', label='AdaBoost', linewidth=2)
    plt.axhline(y=single_acc, color='r', linestyle='--', label='单个决策树')
    plt.title('集成大小对性能的影响')
    plt.xlabel('集成大小')
    plt.ylabel('准确率')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 预测概率分布
    plt.subplot(2, 3, 4)
    bagging_probs = bagging.predict_proba(X_test_c)[:, 1]
    plt.hist(bagging_probs, bins=20, alpha=0.7, label='Bagging')
    plt.title('Bagging预测概率分布')
    plt.xlabel('预测概率')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)

    plt.subplot(2, 3, 5)
    adaboost_probs = adaboost.predict_proba(X_test_c)[:, 1]
    plt.hist(adaboost_probs, bins=20, alpha=0.7, label='AdaBoost', color='orange')
    plt.title('AdaBoost预测概率分布')
    plt.xlabel('预测概率')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)

    # 预测概率对比
    plt.subplot(2, 3, 6)
    plt.scatter(bagging_probs, adaboost_probs, alpha=0.6)
    plt.plot([0, 1], [0, 1], 'r--', label='y=x')
    plt.title('Bagging vs AdaBoost预测概率')
    plt.xlabel('Bagging预测概率')
    plt.ylabel('AdaBoost预测概率')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 3. 集成多样性分析
    print(f"\n3. 集成多样性分析")

    # 计算Bagging中基学习器的多样性
    bagging_predictions = []
    for estimator, feature_indices in zip(bagging.estimators[:10], bagging.estimators_features[:10]):
        X_subset = X_test_c[:, feature_indices]
        pred = estimator.predict(X_subset)
        bagging_predictions.append(pred)

    bagging_predictions = np.array(bagging_predictions)

    # 计算不一致度量
    def disagreement_measure(predictions):
        """计算预测的不一致度量"""
        n_estimators, n_samples = predictions.shape
        disagreements = 0
        total_pairs = 0

        for i in range(n_estimators):
            for j in range(i+1, n_estimators):
                disagreements += np.sum(predictions[i] != predictions[j])
                total_pairs += n_samples

        return disagreements / total_pairs if total_pairs > 0 else 0

    bagging_disagreement = disagreement_measure(bagging_predictions)
    print(f"Bagging基学习器不一致度量: {bagging_disagreement:.4f}")

    # 计算相关系数
    correlations = []
    for i in range(len(bagging_predictions)):
        for j in range(i+1, len(bagging_predictions)):
            corr = np.corrcoef(bagging_predictions[i], bagging_predictions[j])[0, 1]
            correlations.append(corr)

    avg_correlation = np.mean(correlations)
    print(f"Bagging基学习器平均相关系数: {avg_correlation:.4f}")

    print(f"\n集成学习总结:")
    print(f"  Bagging通过减少方差提升性能")
    print(f"  AdaBoost通过减少偏差提升性能")
    print(f"  多样性是集成学习成功的关键")
    print(f"  集成大小需要在性能和计算成本间平衡")

# 运行集成学习演示
demonstrate_ensemble_learning()
```

---

# 第九章：支持向量机深度解析

> **核心理念**: 支持向量机是最优雅的机器学习算法之一，它将分类问题转化为寻找最大间隔超平面的优化问题。

## 9.1 专业术语详解

### 9.1.1 SVM核心概念

**支持向量机 (Support Vector Machine, SVM)**
- **定义**: 寻找最大间隔超平面进行分类的算法
- **核心思想**: 在特征空间中找到能够最大化类别间隔的决策边界
- **几何直觉**: 找到离两类数据点最远的分割线/面
- **数学本质**: 凸二次优化问题

**超平面 (Hyperplane)**
- **定义**: n维空间中的n-1维子空间
- **数学表示**: w^T x + b = 0
- **几何意义**: 将空间分割为两部分的边界
- **决策规则**: sign(w^T x + b)

**间隔 (Margin)**
- **函数间隔**: γ̂ = y(w^T x + b)
- **几何间隔**: γ = γ̂ / ||w||
- **最大间隔**: 2/||w||
- **支持向量**: 位于间隔边界上的数据点

**核技巧 (Kernel Trick)**
- **定义**: 通过核函数将数据映射到高维空间而不显式计算映射
- **数学基础**: K(x,y) = φ(x)^T φ(y)
- **优势**: 避免高维计算，处理非线性问题
- **常用核函数**:
  - **线性核**: K(x,y) = x^T y
  - **多项式核**: K(x,y) = (γx^T y + r)^d
  - **RBF核**: K(x,y) = exp(-γ||x-y||²)
  - **Sigmoid核**: K(x,y) = tanh(γx^T y + r)

## 9.2 SVM算法实现

### 9.2.1 完整SVM实现

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification, make_circles

class SupportVectorMachineComplete:
    """支持向量机的完整实现"""

    def __init__(self, C=1.0, kernel='linear', gamma='scale', degree=3, coef0=0.0):
        """
        SVM核心概念：
        1. 最大间隔：寻找能够最大化类别间隔的超平面
        2. 支持向量：决定决策边界的关键数据点
        3. 核技巧：通过核函数处理非线性问题
        4. 软间隔：允许部分数据点违反间隔约束
        """
        self.C = C  # 正则化参数
        self.kernel = kernel  # 核函数类型
        self.gamma = gamma
        self.degree = degree
        self.coef0 = coef0

        # 训练后的参数
        self.support_vectors = None
        self.support_vector_labels = None
        self.alpha = None
        self.intercept = None
        self.training_history = {}

    def kernel_function(self, X1, X2):
        """核函数计算"""
        if self.kernel == 'linear':
            return X1 @ X2.T

        elif self.kernel == 'polynomial':
            return (self.gamma * (X1 @ X2.T) + self.coef0) ** self.degree

        elif self.kernel == 'rbf':
            # RBF核：K(x,y) = exp(-γ||x-y||²)
            if self.gamma == 'scale':
                gamma_val = 1.0 / X1.shape[1]
            else:
                gamma_val = self.gamma

            # 计算欧几里得距离的平方
            X1_norm = np.sum(X1**2, axis=1, keepdims=True)
            X2_norm = np.sum(X2**2, axis=1, keepdims=True)
            distances_sq = X1_norm + X2_norm.T - 2 * (X1 @ X2.T)

            return np.exp(-gamma_val * distances_sq)

        elif self.kernel == 'sigmoid':
            return np.tanh(self.gamma * (X1 @ X2.T) + self.coef0)

        else:
            raise ValueError(f"不支持的核函数: {self.kernel}")

    def smo_algorithm(self, X, y, max_iter=1000, tol=1e-3):
        """
        SMO算法实现

        SMO (Sequential Minimal Optimization) 核心思想：
        1. 每次选择两个拉格朗日乘子进行优化
        2. 固定其他乘子，求解二次规划子问题
        3. 重复直到收敛

        算法步骤：
        1. 选择违反KKT条件的第一个乘子
        2. 启发式选择第二个乘子
        3. 求解二变量二次规划问题
        4. 更新乘子和偏置项
        5. 检查收敛条件
        """
        n_samples = X.shape[0]

        # 初始化拉格朗日乘子
        alpha = np.zeros(n_samples)
        intercept = 0.0

        # 计算核矩阵
        K = self.kernel_function(X, X)

        print(f"开始SMO算法训练:")
        print(f"  样本数: {n_samples}")
        print(f"  核函数: {self.kernel}")
        print(f"  正则化参数C: {self.C}")

        # SMO主循环
        for iteration in range(max_iter):
            alpha_prev = alpha.copy()

            for i in range(n_samples):
                # 计算预测值和误差
                prediction_i = np.sum(alpha * y * K[i, :]) + intercept
                error_i = prediction_i - y[i]

                # 检查KKT条件
                if (y[i] * error_i < -tol and alpha[i] < self.C) or \
                   (y[i] * error_i > tol and alpha[i] > 0):

                    # 启发式选择第二个变量j
                    j = self._select_second_alpha(i, error_i, X, y, alpha, intercept, K)

                    if j == i:
                        continue

                    prediction_j = np.sum(alpha * y * K[j, :]) + intercept
                    error_j = prediction_j - y[j]

                    # 保存旧的alpha值
                    alpha_i_old, alpha_j_old = alpha[i], alpha[j]

                    # 计算边界L和H
                    if y[i] != y[j]:
                        L = max(0, alpha[j] - alpha[i])
                        H = min(self.C, self.C + alpha[j] - alpha[i])
                    else:
                        L = max(0, alpha[i] + alpha[j] - self.C)
                        H = min(self.C, alpha[i] + alpha[j])

                    if L == H:
                        continue

                    # 计算eta
                    eta = 2 * K[i, j] - K[i, i] - K[j, j]
                    if eta >= 0:
                        continue

                    # 更新alpha[j]
                    alpha[j] = alpha[j] - (y[j] * (error_i - error_j)) / eta
                    alpha[j] = max(L, min(H, alpha[j]))

                    if abs(alpha[j] - alpha_j_old) < 1e-5:
                        continue

                    # 更新alpha[i]
                    alpha[i] = alpha[i] + y[i] * y[j] * (alpha_j_old - alpha[j])

                    # 更新截距
                    b1 = intercept - error_i - y[i] * (alpha[i] - alpha_i_old) * K[i, i] - \
                         y[j] * (alpha[j] - alpha_j_old) * K[i, j]
                    b2 = intercept - error_j - y[i] * (alpha[i] - alpha_i_old) * K[i, j] - \
                         y[j] * (alpha[j] - alpha_j_old) * K[j, j]

                    if 0 < alpha[i] < self.C:
                        intercept = b1
                    elif 0 < alpha[j] < self.C:
                        intercept = b2
                    else:
                        intercept = (b1 + b2) / 2

            # 检查收敛
            if np.linalg.norm(alpha - alpha_prev) < tol:
                print(f"  在第 {iteration + 1} 次迭代后收敛")
                break

            if (iteration + 1) % 100 == 0:
                n_sv = np.sum(alpha > 1e-5)
                print(f"  迭代 {iteration + 1}: 支持向量数 = {n_sv}")

        # 找到支持向量
        support_vector_indices = alpha > 1e-5
        self.support_vectors = X[support_vector_indices]
        self.support_vector_labels = y[support_vector_indices]
        self.alpha = alpha[support_vector_indices]
        self.intercept = intercept

        self.training_history['iterations'] = iteration + 1
        self.training_history['n_support_vectors'] = len(self.alpha)

        print(f"SMO训练完成!")
        print(f"  最终支持向量数: {len(self.alpha)}")
        print(f"  支持向量比例: {len(self.alpha)/n_samples:.3f}")

        return self

    def _select_second_alpha(self, i, error_i, X, y, alpha, intercept, K):
        """启发式选择第二个alpha"""
        # 简化版：随机选择
        candidates = [j for j in range(len(alpha)) if j != i]
        if not candidates:
            return i

        # 选择误差最大的点
        max_error_diff = 0
        best_j = i

        for j in candidates:
            prediction_j = np.sum(alpha * y * K[j, :]) + intercept
            error_j = prediction_j - y[j]
            error_diff = abs(error_i - error_j)

            if error_diff > max_error_diff:
                max_error_diff = error_diff
                best_j = j

        return best_j

    def predict(self, X):
        """预测"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        # 计算决策函数值
        K = self.kernel_function(X, self.support_vectors)
        decision_values = np.sum(self.alpha * self.support_vector_labels * K.T, axis=0) + self.intercept

        return np.sign(decision_values).astype(int)

    def decision_function(self, X):
        """决策函数值"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        K = self.kernel_function(X, self.support_vectors)
        return np.sum(self.alpha * self.support_vector_labels * K.T, axis=0) + self.intercept

    def margin_analysis(self):
        """间隔分析"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        # 计算几何间隔
        if self.kernel == 'linear':
            # 重构权重向量
            w = np.sum((self.alpha * self.support_vector_labels)[:, np.newaxis] *
                      self.support_vectors, axis=0)
            margin = 2.0 / np.linalg.norm(w)
        else:
            # 对于非线性SVM，使用支持向量到决策边界的距离
            sv_distances = np.abs(self.decision_function(self.support_vectors))
            margin = 2.0 * np.min(sv_distances)

        return {
            'margin': margin,
            'n_support_vectors': len(self.alpha),
            'support_vector_ratio': len(self.alpha) / len(self.support_vectors) if len(self.support_vectors) > 0 else 0
        }

# SVM演示
def demonstrate_svm_complete():
    """SVM完整功能演示"""

    print("=== 支持向量机完整演示 ===")

    # 1. 线性可分数据
    print("\n1. 线性可分数据集")
    X_linear, y_linear = make_classification(
        n_samples=200, n_features=2, n_redundant=0, n_informative=2,
        n_clusters_per_class=1, random_state=42
    )

    # 转换标签为{-1, 1}
    y_linear = 2 * y_linear - 1

    # 训练线性SVM
    svm_linear = SupportVectorMachineComplete(C=1.0, kernel='linear')
    svm_linear.smo_algorithm(X_linear, y_linear, max_iter=500)

    # 评估性能
    predictions = svm_linear.predict(X_linear)
    accuracy = np.mean(predictions == y_linear)
    margin_info = svm_linear.margin_analysis()

    print(f"线性SVM结果:")
    print(f"  训练准确率: {accuracy:.4f}")
    print(f"  几何间隔: {margin_info['margin']:.4f}")
    print(f"  支持向量数: {margin_info['n_support_vectors']}")

    # 2. 非线性数据
    print(f"\n2. 非线性数据集（同心圆）")
    X_nonlinear, y_nonlinear = make_circles(n_samples=200, noise=0.1, factor=0.3, random_state=42)
    y_nonlinear = 2 * y_nonlinear - 1

    # 测试不同核函数
    kernels = ['linear', 'polynomial', 'rbf']

    for kernel in kernels:
        print(f"\n{kernel.upper()}核函数:")

        # 训练SVM
        svm = SupportVectorMachineComplete(C=1.0, kernel=kernel, gamma=1.0, degree=3)
        svm.smo_algorithm(X_nonlinear, y_nonlinear, max_iter=300)

        # 预测和评估
        predictions = svm.predict(X_nonlinear)
        accuracy = np.mean(predictions == y_nonlinear)
        margin_info = svm.margin_analysis()

        print(f"  训练准确率: {accuracy:.4f}")
        print(f"  支持向量数: {margin_info['n_support_vectors']}")
        print(f"  支持向量比例: {margin_info['support_vector_ratio']:.3f}")

    # 3. C参数影响分析
    print(f"\n3. 正则化参数C的影响分析")
    C_values = [0.1, 1.0, 10.0, 100.0]

    for C in C_values:
        svm_c = SupportVectorMachineComplete(C=C, kernel='rbf', gamma=1.0)
        svm_c.smo_algorithm(X_nonlinear, y_nonlinear, max_iter=200)

        predictions_c = svm_c.predict(X_nonlinear)
        accuracy_c = np.mean(predictions_c == y_nonlinear)
        margin_c = svm_c.margin_analysis()

        print(f"C={C:6.1f}: 准确率={accuracy_c:.4f}, "
              f"支持向量={margin_c['n_support_vectors']:3d}, "
              f"间隔={margin_c['margin']:.4f}")

    # 4. 可视化决策边界
    print(f"\n4. 决策边界可视化")

    # 创建网格
    def plot_decision_boundary(X, y, svm_model, title):
        """绘制决策边界"""
        h = 0.02
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                            np.arange(y_min, y_max, h))

        # 预测网格点
        grid_points = np.c_[xx.ravel(), yy.ravel()]
        Z = svm_model.decision_function(grid_points)
        Z = Z.reshape(xx.shape)

        # 绘制等高线
        plt.contourf(xx, yy, Z, levels=50, alpha=0.8, cmap='RdYlBu')
        plt.contour(xx, yy, Z, levels=[0], colors='black', linestyles='--', linewidths=2)

        # 绘制数据点
        scatter = plt.scatter(X[:, 0], X[:, 1], c=y, cmap='RdYlBu', edgecolors='black')

        # 标记支持向量
        if hasattr(svm_model, 'support_vectors') and svm_model.support_vectors is not None:
            plt.scatter(svm_model.support_vectors[:, 0], svm_model.support_vectors[:, 1],
                       s=100, facecolors='none', edgecolors='red', linewidths=2, label='支持向量')

        plt.title(title)
        plt.xlabel('特征 1')
        plt.ylabel('特征 2')
        plt.legend()
        plt.colorbar(scatter)

    # 可视化不同核函数的决策边界
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 线性核
    plt.subplot(2, 2, 1)
    svm_vis_linear = SupportVectorMachineComplete(C=1.0, kernel='linear')
    svm_vis_linear.smo_algorithm(X_linear, y_linear, max_iter=200)
    plot_decision_boundary(X_linear, y_linear, svm_vis_linear, '线性核 - 线性可分数据')

    # RBF核 - 线性数据
    plt.subplot(2, 2, 2)
    svm_vis_rbf_linear = SupportVectorMachineComplete(C=1.0, kernel='rbf', gamma=1.0)
    svm_vis_rbf_linear.smo_algorithm(X_linear, y_linear, max_iter=200)
    plot_decision_boundary(X_linear, y_linear, svm_vis_rbf_linear, 'RBF核 - 线性可分数据')

    # 线性核 - 非线性数据
    plt.subplot(2, 2, 3)
    svm_vis_linear_nonlinear = SupportVectorMachineComplete(C=1.0, kernel='linear')
    svm_vis_linear_nonlinear.smo_algorithm(X_nonlinear, y_nonlinear, max_iter=200)
    plot_decision_boundary(X_nonlinear, y_nonlinear, svm_vis_linear_nonlinear, '线性核 - 非线性数据')

    # RBF核 - 非线性数据
    plt.subplot(2, 2, 4)
    svm_vis_rbf_nonlinear = SupportVectorMachineComplete(C=1.0, kernel='rbf', gamma=1.0)
    svm_vis_rbf_nonlinear.smo_algorithm(X_nonlinear, y_nonlinear, max_iter=200)
    plot_decision_boundary(X_nonlinear, y_nonlinear, svm_vis_rbf_nonlinear, 'RBF核 - 非线性数据')

    plt.tight_layout()
    plt.show()

    print(f"\nSVM算法总结:")
    print(f"  线性核适合线性可分数据")
    print(f"  RBF核能处理非线性问题")
    print(f"  C参数控制软间隔的程度")
    print(f"  支持向量决定决策边界")

# 运行SVM演示
demonstrate_svm_complete()
```

---

# 第十章：模型评估的工业标准

> **核心理念**: 模型评估是机器学习项目成功的关键，需要从多个维度全面评估模型性能，确保模型在实际应用中的可靠性和有效性。

## 10.1 专业术语详解

### 10.1.1 分类任务评估指标

**混淆矩阵 (Confusion Matrix)**
- **定义**: 显示分类模型预测结果与真实标签对比的表格
- **组成要素**:
  - **TP (True Positive)**: 正确预测为正类的样本数
  - **TN (True Negative)**: 正确预测为负类的样本数
  - **FP (False Positive)**: 错误预测为正类的样本数（第一类错误）
  - **FN (False Negative)**: 错误预测为负类的样本数（第二类错误）
- **应用**: 所有分类指标的计算基础

**准确率 (Accuracy)**
- **定义**: 正确预测的样本占总样本的比例
- **数学表示**: Accuracy = (TP + TN) / (TP + TN + FP + FN)
- **适用场景**: 类别平衡的数据集
- **局限性**: 在不平衡数据集上可能产生误导

**精确率 (Precision)**
- **定义**: 预测为正类的样本中实际为正类的比例
- **数学表示**: Precision = TP / (TP + FP)
- **业务含义**: 查准率，衡量预测正类的可靠性
- **应用场景**: 关注误报成本高的场景（如垃圾邮件检测）

**召回率 (Recall/Sensitivity)**
- **定义**: 实际正类样本中被正确预测的比例
- **数学表示**: Recall = TP / (TP + FN)
- **业务含义**: 查全率，衡量对正类的识别能力
- **应用场景**: 关注漏报成本高的场景（如疾病诊断）

**F1分数 (F1-Score)**
- **定义**: 精确率和召回率的调和平均数
- **数学表示**: F1 = 2 × (Precision × Recall) / (Precision + Recall)
- **优势**: 平衡精确率和召回率，适用于不平衡数据
- **变体**: F-beta分数，可调节精确率和召回率的权重

**ROC曲线与AUC**
- **ROC曲线**: 以假正率(FPR)为x轴，真正率(TPR)为y轴的曲线
- **FPR**: False Positive Rate = FP / (FP + TN)
- **TPR**: True Positive Rate = TP / (TP + FN) = Recall
- **AUC**: Area Under Curve，ROC曲线下的面积
- **解释**: AUC=1表示完美分类器，AUC=0.5表示随机分类器

### 10.1.2 回归任务评估指标

**均方误差 (Mean Squared Error, MSE)**
- **定义**: 预测值与真实值差值平方的平均数
- **数学表示**: MSE = (1/n) × Σ(yᵢ - ŷᵢ)²
- **特点**: 对异常值敏感，单位为原始单位的平方
- **应用**: 最常用的回归损失函数

**均方根误差 (Root Mean Squared Error, RMSE)**
- **定义**: MSE的平方根
- **数学表示**: RMSE = √MSE
- **优势**: 与原始数据同单位，便于解释
- **应用**: 回归模型性能比较的标准指标

**平均绝对误差 (Mean Absolute Error, MAE)**
- **定义**: 预测值与真实值差值绝对值的平均数
- **数学表示**: MAE = (1/n) × Σ|yᵢ - ŷᵢ|
- **特点**: 对异常值不敏感，鲁棒性好
- **应用**: 存在异常值时的稳健评估

**决定系数 (R-squared, R²)**
- **定义**: 模型解释的方差占总方差的比例
- **数学表示**: R² = 1 - SS_res/SS_tot
- **取值范围**: (-∞, 1]，越接近1越好
- **解释**: 表示模型对数据变异的解释程度

## 10.2 模型评估算法实现

### 10.2.1 评估指标计算

```python
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict

class ModelEvaluationMetrics:
    """模型评估指标的完整实现"""

    def __init__(self):
        """
        评估指标的核心思想：
        1. 准确性指标：模型预测的正确程度
        2. 稳定性指标：模型性能的一致性
        3. 鲁棒性指标：模型对异常值的抗干扰能力
        4. 业务指标：与具体业务目标相关的指标
        """
        pass

    def confusion_matrix(self, y_true, y_pred, labels=None):
        """计算混淆矩阵"""
        if labels is None:
            labels = np.unique(np.concatenate([y_true, y_pred]))

        n_labels = len(labels)
        label_to_idx = {label: idx for idx, label in enumerate(labels)}

        # 初始化混淆矩阵
        cm = np.zeros((n_labels, n_labels), dtype=int)

        # 填充混淆矩阵
        for true_label, pred_label in zip(y_true, y_pred):
            true_idx = label_to_idx[true_label]
            pred_idx = label_to_idx[pred_label]
            cm[true_idx, pred_idx] += 1

        return cm, labels

    def classification_metrics(self, y_true, y_pred, labels=None, average='weighted'):
        """计算分类指标"""
        cm, labels = self.confusion_matrix(y_true, y_pred, labels)
        n_classes = len(labels)

        # 计算每个类别的指标
        precision_per_class = []
        recall_per_class = []
        f1_per_class = []

        for i in range(n_classes):
            tp = cm[i, i]
            fp = np.sum(cm[:, i]) - tp
            fn = np.sum(cm[i, :]) - tp
            tn = np.sum(cm) - tp - fp - fn

            # 精确率
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            precision_per_class.append(precision)

            # 召回率
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            recall_per_class.append(recall)

            # F1分数
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            f1_per_class.append(f1)

        # 计算平均指标
        if average == 'macro':
            avg_precision = np.mean(precision_per_class)
            avg_recall = np.mean(recall_per_class)
            avg_f1 = np.mean(f1_per_class)
        elif average == 'weighted':
            weights = np.sum(cm, axis=1)  # 每个类别的样本数
            total_samples = np.sum(weights)

            avg_precision = np.sum([p * w for p, w in zip(precision_per_class, weights)]) / total_samples
            avg_recall = np.sum([r * w for r, w in zip(recall_per_class, weights)]) / total_samples
            avg_f1 = np.sum([f * w for f, w in zip(f1_per_class, weights)]) / total_samples
        else:  # micro
            total_tp = np.sum([cm[i, i] for i in range(n_classes)])
            total_fp = np.sum(cm) - np.sum(np.diag(cm))
            total_fn = total_fp  # 对于多分类，FP = FN

            avg_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
            avg_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
            avg_f1 = 2 * avg_precision * avg_recall / (avg_precision + avg_recall) if (avg_precision + avg_recall) > 0 else 0

        # 总体准确率
        accuracy = np.sum(np.diag(cm)) / np.sum(cm)

        return {
            'accuracy': accuracy,
            'precision': avg_precision,
            'recall': avg_recall,
            'f1_score': avg_f1,
            'precision_per_class': precision_per_class,
            'recall_per_class': recall_per_class,
            'f1_per_class': f1_per_class,
            'confusion_matrix': cm,
            'labels': labels
        }

    def roc_auc_analysis(self, y_true, y_scores):
        """ROC曲线和AUC分析"""
        # 获取不同阈值
        thresholds = np.unique(y_scores)
        thresholds = np.sort(thresholds)[::-1]  # 降序排列

        tpr_list = []
        fpr_list = []

        for threshold in thresholds:
            y_pred = (y_scores >= threshold).astype(int)

            tp = np.sum((y_true == 1) & (y_pred == 1))
            fp = np.sum((y_true == 0) & (y_pred == 1))
            tn = np.sum((y_true == 0) & (y_pred == 0))
            fn = np.sum((y_true == 1) & (y_pred == 0))

            tpr = tp / (tp + fn) if (tp + fn) > 0 else 0
            fpr = fp / (fp + tn) if (fp + tn) > 0 else 0

            tpr_list.append(tpr)
            fpr_list.append(fpr)

        # 计算AUC（使用梯形法则）
        auc = 0
        for i in range(1, len(fpr_list)):
            auc += (fpr_list[i] - fpr_list[i-1]) * (tpr_list[i] + tpr_list[i-1]) / 2

        return {
            'fpr': fpr_list,
            'tpr': tpr_list,
            'thresholds': thresholds,
            'auc': abs(auc)  # 取绝对值确保AUC为正
        }

    def regression_metrics(self, y_true, y_pred):
        """计算回归指标"""
        n = len(y_true)

        # 均方误差
        mse = np.mean((y_true - y_pred) ** 2)

        # 均方根误差
        rmse = np.sqrt(mse)

        # 平均绝对误差
        mae = np.mean(np.abs(y_true - y_pred))

        # 决定系数 R²
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

        # 调整R²
        p = 1  # 假设只有一个特征，实际应用中需要传入特征数
        adj_r2 = 1 - (1 - r2) * (n - 1) / (n - p - 1) if n > p + 1 else r2

        # 平均绝对百分比误差
        mape = np.mean(np.abs((y_true - y_pred) / np.maximum(np.abs(y_true), 1e-8))) * 100

        return {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'adjusted_r2': adj_r2,
            'mape': mape
        }

### 10.2.2 交叉验证框架

```python
class CrossValidationFramework:
    """交叉验证框架的完整实现"""

    def __init__(self):
        """
        交叉验证的核心思想：
        1. 将数据分成多个折(fold)
        2. 轮流使用不同折作为验证集
        3. 其余折作为训练集
        4. 计算平均性能和方差

        常见的交叉验证方法：
        - K-Fold CV: 标准k折交叉验证
        - Stratified K-Fold: 保持类别比例的k折
        - Leave-One-Out: 留一法
        - Time Series Split: 时间序列分割
        """
        pass

    def k_fold_split(self, X, y, k=5, shuffle=True, random_state=None):
        """K折交叉验证数据分割"""
        n_samples = len(X)
        indices = np.arange(n_samples)

        if shuffle:
            if random_state is not None:
                np.random.seed(random_state)
            np.random.shuffle(indices)

        # 计算每个折的大小
        fold_sizes = [n_samples // k] * k
        for i in range(n_samples % k):
            fold_sizes[i] += 1

        # 生成折的索引
        folds = []
        start_idx = 0

        for fold_size in fold_sizes:
            end_idx = start_idx + fold_size
            fold_indices = indices[start_idx:end_idx]
            folds.append(fold_indices)
            start_idx = end_idx

        # 生成训练/验证分割
        splits = []
        for i in range(k):
            val_indices = folds[i]
            train_indices = np.concatenate([folds[j] for j in range(k) if j != i])

            splits.append({
                'train_indices': train_indices,
                'val_indices': val_indices,
                'fold': i
            })

        return splits

    def stratified_k_fold_split(self, X, y, k=5, random_state=None):
        """分层K折交叉验证"""
        if random_state is not None:
            np.random.seed(random_state)

        # 获取每个类别的样本索引
        unique_labels = np.unique(y)
        class_indices = {}

        for label in unique_labels:
            indices = np.where(y == label)[0]
            np.random.shuffle(indices)
            class_indices[label] = indices

        # 为每个类别分配到k个折中
        folds = [[] for _ in range(k)]

        for label, indices in class_indices.items():
            n_samples = len(indices)
            samples_per_fold = n_samples // k
            remainder = n_samples % k

            start_idx = 0
            for fold_idx in range(k):
                fold_size = samples_per_fold + (1 if fold_idx < remainder else 0)
                end_idx = start_idx + fold_size

                folds[fold_idx].extend(indices[start_idx:end_idx])
                start_idx = end_idx

        # 生成训练/验证分割
        splits = []
        for i in range(k):
            val_indices = np.array(folds[i])
            train_indices = np.concatenate([folds[j] for j in range(k) if j != i])

            splits.append({
                'train_indices': train_indices,
                'val_indices': val_indices,
                'fold': i
            })

        return splits

    def cross_validate_model(self, model_class, model_params, X, y, cv_method='k_fold',
                           k=5, scoring='accuracy', random_state=None):
        """执行交叉验证"""
        # 获取数据分割
        if cv_method == 'k_fold':
            splits = self.k_fold_split(X, y, k=k, random_state=random_state)
        elif cv_method == 'stratified':
            splits = self.stratified_k_fold_split(X, y, k=k, random_state=random_state)
        else:
            raise ValueError(f"不支持的交叉验证方法: {cv_method}")

        # 存储每折的结果
        fold_scores = []
        fold_predictions = []
        fold_true_labels = []

        evaluator = ModelEvaluationMetrics()

        for split in splits:
            train_indices = split['train_indices']
            val_indices = split['val_indices']
            fold_num = split['fold']

            # 分割数据
            X_train, X_val = X[train_indices], X[val_indices]
            y_train, y_val = y[train_indices], y[val_indices]

            # 训练模型
            model = model_class(**model_params)
            model.fit(X_train, y_train)

            # 预测
            y_pred = model.predict(X_val)

            # 计算得分
            if scoring == 'accuracy':
                score = np.mean(y_pred == y_val)
            elif scoring == 'f1':
                metrics = evaluator.classification_metrics(y_val, y_pred)
                score = metrics['f1_score']
            elif scoring == 'precision':
                metrics = evaluator.classification_metrics(y_val, y_pred)
                score = metrics['precision']
            elif scoring == 'recall':
                metrics = evaluator.classification_metrics(y_val, y_pred)
                score = metrics['recall']
            else:
                raise ValueError(f"不支持的评分方法: {scoring}")

            fold_scores.append(score)
            fold_predictions.extend(y_pred)
            fold_true_labels.extend(y_val)

            print(f"  折 {fold_num + 1}: {scoring} = {score:.4f}")

        # 计算总体统计
        mean_score = np.mean(fold_scores)
        std_score = np.std(fold_scores)

        cv_results = {
            'scores': fold_scores,
            'mean_score': mean_score,
            'std_score': std_score,
            'all_predictions': np.array(fold_predictions),
            'all_true_labels': np.array(fold_true_labels)
        }

        return cv_results

    def grid_search_cv(self, model_class, param_grid, X, y, cv_method='k_fold',
                      k=5, scoring='accuracy', random_state=None):
        """网格搜索交叉验证"""
        # 生成所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())

        from itertools import product
        param_combinations = list(product(*param_values))

        best_score = -np.inf
        best_params = None
        all_results = []

        print(f"网格搜索开始，共{len(param_combinations)}个参数组合...")

        for i, param_combo in enumerate(param_combinations):
            # 构建参数字典
            current_params = dict(zip(param_names, param_combo))

            print(f"  测试参数组合 {i+1}/{len(param_combinations)}: {current_params}")

            # 执行交叉验证
            cv_results = self.cross_validate_model(
                model_class, current_params, X, y,
                cv_method=cv_method, k=k, scoring=scoring, random_state=random_state
            )

            mean_score = cv_results['mean_score']
            std_score = cv_results['std_score']

            # 保存结果
            result = {
                'params': current_params,
                'mean_score': mean_score,
                'std_score': std_score,
                'cv_results': cv_results
            }
            all_results.append(result)

            print(f"    平均{scoring}: {mean_score:.4f} (±{std_score:.4f})")

            # 更新最佳结果
            if mean_score > best_score:
                best_score = mean_score
                best_params = current_params

        print(f"\n网格搜索完成！")
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.4f}")

        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': all_results
        }

# 模型评估演示
def demonstrate_model_evaluation():
    """模型评估综合演示"""

    print("=== 模型评估综合演示 ===")

    # 生成模拟数据
    np.random.seed(42)

    # 场景1：二分类问题评估
    print("\n场景1：二分类模型评估")
    print("目标：全面评估二分类模型的性能")

    # 生成不平衡的二分类数据
    n_samples = 1000
    n_positive = 200  # 正类样本较少
    n_negative = 800  # 负类样本较多

    # 正类数据（集中分布）
    X_positive = np.random.multivariate_normal([2, 2], [[1, 0.5], [0.5, 1]], n_positive)
    y_positive = np.ones(n_positive)

    # 负类数据（分散分布）
    X_negative = np.random.multivariate_normal([0, 0], [[2, 0], [0, 2]], n_negative)
    y_negative = np.zeros(n_negative)

    # 合并数据
    X = np.vstack([X_positive, X_negative])
    y = np.concatenate([y_positive, y_negative])

    # 打乱数据
    indices = np.random.permutation(len(X))
    X, y = X[indices], y[indices]

    print(f"数据集信息:")
    print(f"  总样本数: {len(X)}")
    print(f"  正类样本: {np.sum(y == 1)}")
    print(f"  负类样本: {np.sum(y == 0)}")
    print(f"  类别比例: {np.sum(y == 1) / len(y):.3f} : {np.sum(y == 0) / len(y):.3f}")

    # 模拟模型预测结果
    # 创建一个有一定错误率的预测
    y_scores = np.random.beta(2, 5, len(y))  # 生成0-1之间的分数
    y_scores[y == 1] += 0.3  # 正类样本分数偏高
    y_scores = np.clip(y_scores, 0, 1)

    y_pred = (y_scores > 0.5).astype(int)

    # 计算评估指标
    evaluator = ModelEvaluationMetrics()

    # 分类指标
    classification_results = evaluator.classification_metrics(y, y_pred)

    print(f"\n分类性能指标:")
    print(f"  准确率: {classification_results['accuracy']:.4f}")
    print(f"  精确率: {classification_results['precision']:.4f}")
    print(f"  召回率: {classification_results['recall']:.4f}")
    print(f"  F1分数: {classification_results['f1_score']:.4f}")

    # 混淆矩阵
    cm = classification_results['confusion_matrix']
    print(f"\n混淆矩阵:")
    print(f"        预测")
    print(f"真实   0    1")
    print(f"  0   {cm[0,0]:4d} {cm[0,1]:4d}")
    print(f"  1   {cm[1,0]:4d} {cm[1,1]:4d}")

    # ROC-AUC分析
    roc_results = evaluator.roc_auc_analysis(y, y_scores)
    print(f"\nROC-AUC分析:")
    print(f"  AUC值: {roc_results['auc']:.4f}")

    # 场景2：交叉验证演示
    print(f"\n场景2：交叉验证模型选择")
    print("目标：使用交叉验证选择最佳模型参数")

    # 定义一个简单的模型类用于演示
    class SimpleLogisticRegression:
        def __init__(self, learning_rate=0.01, max_iter=100, regularization=0.0):
            self.learning_rate = learning_rate
            self.max_iter = max_iter
            self.regularization = regularization
            self.weights = None
            self.bias = None

        def sigmoid(self, z):
            return 1 / (1 + np.exp(-np.clip(z, -500, 500)))

        def fit(self, X, y):
            n_samples, n_features = X.shape
            self.weights = np.random.randn(n_features) * 0.01
            self.bias = 0

            for _ in range(self.max_iter):
                z = X @ self.weights + self.bias
                predictions = self.sigmoid(z)

                # 计算梯度
                dw = (1/n_samples) * X.T @ (predictions - y) + self.regularization * self.weights
                db = (1/n_samples) * np.sum(predictions - y)

                # 更新参数
                self.weights -= self.learning_rate * dw
                self.bias -= self.learning_rate * db

        def predict(self, X):
            z = X @ self.weights + self.bias
            probabilities = self.sigmoid(z)
            return (probabilities > 0.5).astype(int)

    # 初始化交叉验证框架
    cv_framework = CrossValidationFramework()

    # 定义参数网格
    param_grid = {
        'learning_rate': [0.001, 0.01, 0.1],
        'max_iter': [50, 100, 200],
        'regularization': [0.0, 0.01, 0.1]
    }

    # 执行网格搜索
    grid_results = cv_framework.grid_search_cv(
        SimpleLogisticRegression, param_grid, X, y,
        cv_method='stratified', k=5, scoring='f1', random_state=42
    )

    print(f"\n网格搜索结果:")
    print(f"最佳参数组合: {grid_results['best_params']}")
    print(f"最佳F1分数: {grid_results['best_score']:.4f}")

    # 显示前5个最佳结果
    sorted_results = sorted(grid_results['all_results'],
                           key=lambda x: x['mean_score'], reverse=True)

    print(f"\n前5个最佳参数组合:")
    for i, result in enumerate(sorted_results[:5]):
        params = result['params']
        score = result['mean_score']
        std = result['std_score']
        print(f"  {i+1}. {params}")
        print(f"     F1分数: {score:.4f} (±{std:.4f})")

    print(f"\n模型评估总结:")
    print(f"  1. 使用多个指标全面评估模型性能")
    print(f"  2. 混淆矩阵提供详细的分类结果分析")
    print(f"  3. ROC-AUC评估模型的排序能力")
    print(f"  4. 交叉验证提供可靠的性能估计")
    print(f"  5. 网格搜索自动化超参数优化")

# 运行模型评估演示
demonstrate_model_evaluation()
```

---

# 第十一章：概率图模型与推断算法

> **核心理念**: 概率图模型将概率论与图论结合，为处理不确定性和复杂依赖关系提供了强大的数学框架，是现代AI的重要基础。

## 11.1 马尔可夫链与随机过程

### 11.1.1 马尔可夫链基础理论

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import eig
import seaborn as sns
from collections import defaultdict

class MarkovChain:
    """马尔可夫链的完整实现"""

    def __init__(self, states, transition_matrix):
        """
        马尔可夫链核心概念：
        1. 马尔可夫性质：未来状态只依赖于当前状态
        2. 状态转移矩阵：描述状态间转移概率
        3. 平稳分布：长期运行后的状态分布
        4. 遍历性：从任意状态可达任意其他状态

        数学表示：P(X_{t+1} = j | X_t = i, X_{t-1}, ..., X_0) = P(X_{t+1} = j | X_t = i)
        """
        self.states = states
        self.n_states = len(states)
        self.state_to_index = {state: i for i, state in enumerate(states)}
        self.index_to_state = {i: state for i, state in enumerate(states)}

        # 验证转移矩阵
        self.transition_matrix = np.array(transition_matrix)
        self._validate_transition_matrix()

        # 计算平稳分布
        self.stationary_distribution = self._compute_stationary_distribution()

    def _validate_transition_matrix(self):
        """验证转移矩阵的有效性"""
        # 检查矩阵形状
        if self.transition_matrix.shape != (self.n_states, self.n_states):
            raise ValueError(f"转移矩阵形状应为 ({self.n_states}, {self.n_states})")

        # 检查概率和为1
        row_sums = np.sum(self.transition_matrix, axis=1)
        if not np.allclose(row_sums, 1.0):
            raise ValueError("转移矩阵每行概率和必须为1")

        # 检查非负性
        if np.any(self.transition_matrix < 0):
            raise ValueError("转移矩阵元素必须非负")

    def _compute_stationary_distribution(self):
        """计算平稳分布"""
        # 求解 π = πP 的特征向量
        eigenvalues, eigenvectors = eig(self.transition_matrix.T)

        # 找到特征值为1的特征向量
        stationary_index = np.argmin(np.abs(eigenvalues - 1.0))
        stationary_vector = np.real(eigenvectors[:, stationary_index])

        # 归一化为概率分布
        stationary_vector = np.abs(stationary_vector)
        stationary_distribution = stationary_vector / np.sum(stationary_vector)

        return stationary_distribution

    def simulate(self, initial_state, n_steps):
        """模拟马尔可夫链"""
        if isinstance(initial_state, str):
            current_state_index = self.state_to_index[initial_state]
        else:
            current_state_index = initial_state

        path = [current_state_index]

        for _ in range(n_steps):
            # 根据当前状态的转移概率选择下一状态
            transition_probs = self.transition_matrix[current_state_index]
            next_state_index = np.random.choice(self.n_states, p=transition_probs)
            path.append(next_state_index)
            current_state_index = next_state_index

        # 转换为状态名称
        state_path = [self.index_to_state[i] for i in path]
        return state_path, path

    def n_step_transition_probability(self, n):
        """计算n步转移概率矩阵"""
        return np.linalg.matrix_power(self.transition_matrix, n)

    def hitting_time(self, start_state, target_state, max_steps=10000):
        """计算首达时间"""
        if isinstance(start_state, str):
            start_index = self.state_to_index[start_state]
        else:
            start_index = start_state

        if isinstance(target_state, str):
            target_index = self.state_to_index[target_state]
        else:
            target_index = target_state

        hitting_times = []
        n_simulations = 1000

        for _ in range(n_simulations):
            current_state = start_index
            steps = 0

            while current_state != target_index and steps < max_steps:
                transition_probs = self.transition_matrix[current_state]
                current_state = np.random.choice(self.n_states, p=transition_probs)
                steps += 1

            if steps < max_steps:
                hitting_times.append(steps)

        return {
            'mean_hitting_time': np.mean(hitting_times) if hitting_times else float('inf'),
            'std_hitting_time': np.std(hitting_times) if hitting_times else 0,
            'success_rate': len(hitting_times) / n_simulations
        }

    def is_irreducible(self):
        """检查马尔可夫链是否不可约"""
        # 计算可达性矩阵
        reachability = np.eye(self.n_states)
        temp = np.eye(self.n_states)

        for _ in range(self.n_states):
            temp = temp @ self.transition_matrix
            reachability = reachability + temp

        # 如果所有元素都大于0，则不可约
        return np.all(reachability > 0)

    def period_of_state(self, state):
        """计算状态的周期"""
        if isinstance(state, str):
            state_index = self.state_to_index[state]
        else:
            state_index = state

        # 计算返回到该状态的所有可能步数
        powers = []
        current_matrix = self.transition_matrix.copy()

        for n in range(1, self.n_states + 1):
            if current_matrix[state_index, state_index] > 0:
                powers.append(n)
            current_matrix = current_matrix @ self.transition_matrix

        if not powers:
            return float('inf')  # 不可达

        # 计算最大公约数
        from math import gcd
        result = powers[0]
        for p in powers[1:]:
            result = gcd(result, p)

        return result

class HiddenMarkovModel:
    """隐马尔可夫模型实现"""

    def __init__(self, states, observations, transition_probs, emission_probs, initial_probs):
        """
        HMM核心组件：
        1. 状态转移概率矩阵 A
        2. 观测概率矩阵 B
        3. 初始状态概率 π

        三个基本问题：
        1. 评估问题：给定模型参数，计算观测序列概率
        2. 解码问题：给定观测序列，找到最可能的状态序列
        3. 学习问题：给定观测序列，学习模型参数
        """
        self.states = states
        self.observations = observations
        self.n_states = len(states)
        self.n_observations = len(observations)

        self.transition_probs = np.array(transition_probs)
        self.emission_probs = np.array(emission_probs)
        self.initial_probs = np.array(initial_probs)

        # 状态和观测的索引映射
        self.state_to_index = {state: i for i, state in enumerate(states)}
        self.obs_to_index = {obs: i for i, obs in enumerate(observations)}

    def forward_algorithm(self, observation_sequence):
        """前向算法：计算观测序列的概率"""
        T = len(observation_sequence)
        alpha = np.zeros((T, self.n_states))

        # 观测序列转换为索引
        obs_indices = [self.obs_to_index[obs] for obs in observation_sequence]

        # 初始化
        alpha[0] = self.initial_probs * self.emission_probs[:, obs_indices[0]]

        # 递推
        for t in range(1, T):
            for j in range(self.n_states):
                alpha[t, j] = np.sum(alpha[t-1] * self.transition_probs[:, j]) * \
                             self.emission_probs[j, obs_indices[t]]

        # 观测序列概率
        sequence_prob = np.sum(alpha[T-1])

        return alpha, sequence_prob

    def backward_algorithm(self, observation_sequence):
        """后向算法"""
        T = len(observation_sequence)
        beta = np.zeros((T, self.n_states))

        obs_indices = [self.obs_to_index[obs] for obs in observation_sequence]

        # 初始化
        beta[T-1] = 1

        # 递推
        for t in range(T-2, -1, -1):
            for i in range(self.n_states):
                beta[t, i] = np.sum(self.transition_probs[i] *
                                   self.emission_probs[:, obs_indices[t+1]] *
                                   beta[t+1])

        return beta

    def viterbi_algorithm(self, observation_sequence):
        """维特比算法：找到最可能的状态序列"""
        T = len(observation_sequence)
        delta = np.zeros((T, self.n_states))
        psi = np.zeros((T, self.n_states), dtype=int)

        obs_indices = [self.obs_to_index[obs] for obs in observation_sequence]

        # 初始化
        delta[0] = self.initial_probs * self.emission_probs[:, obs_indices[0]]
        psi[0] = 0

        # 递推
        for t in range(1, T):
            for j in range(self.n_states):
                # 找到最大概率路径
                prob_candidates = delta[t-1] * self.transition_probs[:, j]
                delta[t, j] = np.max(prob_candidates) * self.emission_probs[j, obs_indices[t]]
                psi[t, j] = np.argmax(prob_candidates)

        # 回溯最优路径
        path = np.zeros(T, dtype=int)
        path[T-1] = np.argmax(delta[T-1])

        for t in range(T-2, -1, -1):
            path[t] = psi[t+1, path[t+1]]

        # 转换为状态名称
        state_path = [self.states[i] for i in path]
        max_prob = np.max(delta[T-1])

        return state_path, path, max_prob

    def baum_welch_algorithm(self, observation_sequences, max_iterations=100, tolerance=1e-6):
        """Baum-Welch算法：学习HMM参数"""

        for iteration in range(max_iterations):
            # E步：计算期望
            gamma_sum = np.zeros(self.n_states)
            xi_sum = np.zeros((self.n_states, self.n_states))
            emission_counts = np.zeros((self.n_states, self.n_observations))

            total_log_likelihood = 0

            for obs_seq in observation_sequences:
                alpha, seq_prob = self.forward_algorithm(obs_seq)
                beta = self.backward_algorithm(obs_seq)

                total_log_likelihood += np.log(seq_prob + 1e-10)

                T = len(obs_seq)
                obs_indices = [self.obs_to_index[obs] for obs in obs_seq]

                # 计算gamma (状态后验概率)
                gamma = alpha * beta
                gamma = gamma / np.sum(gamma, axis=1, keepdims=True)

                # 计算xi (状态转移后验概率)
                xi = np.zeros((T-1, self.n_states, self.n_states))
                for t in range(T-1):
                    for i in range(self.n_states):
                        for j in range(self.n_states):
                            xi[t, i, j] = alpha[t, i] * self.transition_probs[i, j] * \
                                         self.emission_probs[j, obs_indices[t+1]] * beta[t+1, j]
                    xi[t] = xi[t] / np.sum(xi[t])

                # 累积统计量
                gamma_sum += np.sum(gamma, axis=0)
                xi_sum += np.sum(xi, axis=0)

                for t in range(T):
                    for i in range(self.n_states):
                        emission_counts[i, obs_indices[t]] += gamma[t, i]

            # M步：更新参数
            old_transition = self.transition_probs.copy()
            old_emission = self.emission_probs.copy()

            # 更新转移概率
            for i in range(self.n_states):
                if np.sum(xi_sum[i]) > 0:
                    self.transition_probs[i] = xi_sum[i] / np.sum(xi_sum[i])

            # 更新发射概率
            for i in range(self.n_states):
                if gamma_sum[i] > 0:
                    self.emission_probs[i] = emission_counts[i] / gamma_sum[i]

            # 检查收敛
            transition_change = np.max(np.abs(self.transition_probs - old_transition))
            emission_change = np.max(np.abs(self.emission_probs - old_emission))

            if max(transition_change, emission_change) < tolerance:
                print(f"Baum-Welch算法在第{iteration+1}次迭代后收敛")
                break

        return total_log_likelihood

# 马尔可夫链演示
def demonstrate_markov_chains():
    """马尔可夫链和HMM演示"""

    print("=== 马尔可夫链与隐马尔可夫模型演示 ===")

    # 1. 天气预测马尔可夫链
    print("\n1. 天气预测马尔可夫链")

    # 定义状态和转移矩阵
    weather_states = ['晴天', '雨天', '阴天']
    weather_transition = [
        [0.7, 0.2, 0.1],  # 晴天 -> [晴天, 雨天, 阴天]
        [0.3, 0.4, 0.3],  # 雨天 -> [晴天, 雨天, 阴天]
        [0.4, 0.3, 0.3]   # 阴天 -> [晴天, 雨天, 阴天]
    ]

    weather_mc = MarkovChain(weather_states, weather_transition)

    print(f"天气转移矩阵:")
    for i, state in enumerate(weather_states):
        print(f"  {state}: {weather_transition[i]}")

    print(f"\n平稳分布: {dict(zip(weather_states, weather_mc.stationary_distribution))}")
    print(f"马尔可夫链是否不可约: {weather_mc.is_irreducible()}")

    # 模拟天气序列
    weather_path, _ = weather_mc.simulate('晴天', 30)
    print(f"\n30天天气模拟: {weather_path[:10]}...")

    # 计算状态频率
    state_counts = {state: weather_path.count(state) for state in weather_states}
    state_frequencies = {state: count/len(weather_path) for state, count in state_counts.items()}
    print(f"模拟频率: {state_frequencies}")

    # 2. 隐马尔可夫模型 - 词性标注
    print(f"\n2. 隐马尔可夫模型 - 简化词性标注")

    # 定义隐状态（词性）和观测（单词）
    pos_states = ['名词', '动词', '形容词']
    words = ['猫', '跑', '快', '狗', '吃', '好']

    # HMM参数
    pos_initial = [0.6, 0.3, 0.1]  # 初始词性概率

    pos_transition = [
        [0.3, 0.5, 0.2],  # 名词 -> [名词, 动词, 形容词]
        [0.4, 0.2, 0.4],  # 动词 -> [名词, 动词, 形容词]
        [0.6, 0.1, 0.3]   # 形容词 -> [名词, 动词, 形容词]
    ]

    pos_emission = [
        [0.4, 0.1, 0.1, 0.3, 0.05, 0.05],  # 名词发射概率
        [0.1, 0.4, 0.1, 0.1, 0.25, 0.05],  # 动词发射概率
        [0.1, 0.1, 0.3, 0.1, 0.05, 0.35]   # 形容词发射概率
    ]

    hmm = HiddenMarkovModel(pos_states, words, pos_transition, pos_emission, pos_initial)

    # 测试句子
    test_sentence = ['猫', '跑', '快']

    # 前向算法计算概率
    alpha, sentence_prob = hmm.forward_algorithm(test_sentence)
    print(f"句子 '{' '.join(test_sentence)}' 的概率: {sentence_prob:.6f}")

    # 维特比算法找最可能的词性序列
    best_pos_sequence, _, max_prob = hmm.viterbi_algorithm(test_sentence)
    print(f"最可能的词性序列: {best_pos_sequence}")
    print(f"最大概率: {max_prob:.6f}")

    # 3. 可视化分析
    print(f"\n3. 马尔可夫链可视化分析")

    plt.figure(figsize=(15, 10))

    # 转移矩阵热力图
    plt.subplot(2, 3, 1)
    sns.heatmap(weather_transition, annot=True, cmap='Blues',
                xticklabels=weather_states, yticklabels=weather_states)
    plt.title('天气转移矩阵')
    plt.xlabel('下一状态')
    plt.ylabel('当前状态')

    # 平稳分布
    plt.subplot(2, 3, 2)
    plt.bar(weather_states, weather_mc.stationary_distribution, alpha=0.7)
    plt.title('平稳分布')
    plt.ylabel('概率')
    plt.xticks(rotation=45)

    # n步转移概率演化
    plt.subplot(2, 3, 3)
    steps = range(1, 11)
    sunny_probs = []

    for n in steps:
        n_step_matrix = weather_mc.n_step_transition_probability(n)
        sunny_probs.append(n_step_matrix[0, 0])  # 晴天->晴天的概率

    plt.plot(steps, sunny_probs, 'o-', label='晴天→晴天')
    plt.axhline(y=weather_mc.stationary_distribution[0], color='r',
                linestyle='--', label='平稳概率')
    plt.xlabel('步数')
    plt.ylabel('转移概率')
    plt.title('转移概率收敛')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 状态序列模拟
    plt.subplot(2, 3, 4)
    long_path, long_indices = weather_mc.simulate('晴天', 1000)

    # 计算滑动平均
    window_size = 50
    moving_avg = []
    for i in range(window_size, len(long_indices)):
        window = long_indices[i-window_size:i]
        sunny_ratio = sum(1 for x in window if x == 0) / window_size
        moving_avg.append(sunny_ratio)

    plt.plot(range(window_size, len(long_indices)), moving_avg, alpha=0.7)
    plt.axhline(y=weather_mc.stationary_distribution[0], color='r',
                linestyle='--', label='理论平稳概率')
    plt.xlabel('时间步')
    plt.ylabel('晴天比例')
    plt.title('状态频率收敛')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # HMM前向概率
    plt.subplot(2, 3, 5)
    plt.imshow(alpha.T, cmap='Blues', aspect='auto')
    plt.colorbar(label='前向概率')
    plt.xlabel('时间步')
    plt.ylabel('隐状态')
    plt.yticks(range(len(pos_states)), pos_states)
    plt.title('HMM前向概率')

    # 首达时间分布
    plt.subplot(2, 3, 6)
    hitting_times = []
    for _ in range(1000):
        path, _ = weather_mc.simulate('晴天', 100)
        try:
            first_rainy = path.index('雨天')
            hitting_times.append(first_rainy)
        except ValueError:
            pass  # 没有遇到雨天

    plt.hist(hitting_times, bins=20, alpha=0.7, density=True)
    plt.xlabel('首达时间')
    plt.ylabel('概率密度')
    plt.title('晴天→雨天首达时间分布')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 4. 理论分析
    print(f"\n4. 马尔可夫链理论分析")

    # 计算各状态的周期
    for state in weather_states:
        period = weather_mc.period_of_state(state)
        print(f"  {state}的周期: {period}")

    # 计算首达时间
    hitting_stats = weather_mc.hitting_time('晴天', '雨天')
    print(f"  晴天到雨天的平均首达时间: {hitting_stats['mean_hitting_time']:.2f}")
    print(f"  标准差: {hitting_stats['std_hitting_time']:.2f}")
    print(f"  成功率: {hitting_stats['success_rate']:.3f}")

    print(f"\n马尔可夫链总结:")
    print(f"  马尔可夫性质：无记忆性，未来只依赖现在")
    print(f"  平稳分布：长期运行的状态分布")
    print(f"  遍历性：所有状态相互可达")
    print(f"  HMM：隐状态的马尔可夫链，观测依赖隐状态")
    print(f"  应用：语音识别、词性标注、生物信息学")

# 运行马尔可夫链演示
demonstrate_markov_chains()
```

## 11.2 变分推断与近似推理

### 11.2.1 变分推断理论基础

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
from scipy.stats import multivariate_normal, norm
from sklearn.mixture import GaussianMixture
import warnings
warnings.filterwarnings('ignore')

class VariationalInference:
    """变分推断的完整实现"""

    def __init__(self):
        """
        变分推断核心思想：
        1. 将复杂的后验推断转化为优化问题
        2. 用简单分布族近似复杂的后验分布
        3. 最小化KL散度或最大化证据下界(ELBO)

        数学基础：
        - 后验分布：p(z|x) = p(x|z)p(z) / p(x)
        - 变分分布：q(z|θ) ≈ p(z|x)
        - KL散度：KL(q||p) = ∫ q(z) log(q(z)/p(z)) dz
        - ELBO：L = E_q[log p(x,z)] - E_q[log q(z)]
        """
        pass

    def kl_divergence_gaussian(self, mu1, sigma1, mu2, sigma2):
        """计算两个高斯分布的KL散度"""
        # KL(N(μ₁,σ₁²) || N(μ₂,σ₂²)) = log(σ₂/σ₁) + (σ₁² + (μ₁-μ₂)²)/(2σ₂²) - 1/2
        kl = np.log(sigma2 / sigma1) + (sigma1**2 + (mu1 - mu2)**2) / (2 * sigma2**2) - 0.5
        return kl

    def elbo_gaussian_mixture(self, X, mu_q, sigma_q, pi_q, mu_prior, sigma_prior):
        """计算高斯混合模型的ELBO"""
        n_samples, n_features = X.shape
        n_components = len(pi_q)

        # 计算期望对数似然 E_q[log p(x|z)]
        log_likelihood = 0
        for k in range(n_components):
            for i in range(n_samples):
                # 计算样本i属于组件k的概率
                responsibility = pi_q[k] * multivariate_normal.pdf(X[i], mu_q[k], np.eye(n_features) * sigma_q[k]**2)
                if responsibility > 1e-10:
                    log_likelihood += responsibility * multivariate_normal.logpdf(X[i], mu_q[k], np.eye(n_features) * sigma_q[k]**2)

        # 计算KL散度项 KL(q(z) || p(z))
        kl_divergence = 0
        for k in range(n_components):
            # 假设先验是标准高斯分布
            kl_divergence += self.kl_divergence_gaussian(mu_q[k][0], sigma_q[k], mu_prior, sigma_prior)

        # ELBO = E_q[log p(x|z)] - KL(q(z) || p(z))
        elbo = log_likelihood - kl_divergence
        return elbo

    def mean_field_vi_gaussian_mixture(self, X, n_components=2, max_iterations=100, tolerance=1e-6):
        """平均场变分推断用于高斯混合模型"""
        n_samples, n_features = X.shape

        # 初始化变分参数
        mu_q = [np.random.randn(n_features) for _ in range(n_components)]
        sigma_q = [1.0 for _ in range(n_components)]
        pi_q = np.ones(n_components) / n_components

        # 先验参数
        mu_prior = 0.0
        sigma_prior = 1.0

        elbo_history = []

        for iteration in range(max_iterations):
            # E步：更新责任度（隐变量的后验）
            responsibilities = np.zeros((n_samples, n_components))

            for i in range(n_samples):
                for k in range(n_components):
                    responsibilities[i, k] = pi_q[k] * multivariate_normal.pdf(
                        X[i], mu_q[k], np.eye(n_features) * sigma_q[k]**2
                    )

                # 归一化
                row_sum = np.sum(responsibilities[i])
                if row_sum > 1e-10:
                    responsibilities[i] /= row_sum

            # M步：更新变分参数
            old_mu_q = [mu.copy() for mu in mu_q]
            old_sigma_q = sigma_q.copy()

            for k in range(n_components):
                # 更新混合权重
                pi_q[k] = np.mean(responsibilities[:, k])

                # 更新均值
                if pi_q[k] > 1e-10:
                    weighted_sum = np.sum(responsibilities[:, k:k+1] * X, axis=0)
                    mu_q[k] = weighted_sum / (np.sum(responsibilities[:, k]) + 1e-10)

                # 更新方差
                if pi_q[k] > 1e-10:
                    weighted_var = 0
                    for i in range(n_samples):
                        diff = X[i] - mu_q[k]
                        weighted_var += responsibilities[i, k] * np.sum(diff**2)
                    sigma_q[k] = np.sqrt(weighted_var / (np.sum(responsibilities[:, k]) * n_features + 1e-10))

            # 计算ELBO
            elbo = self.elbo_gaussian_mixture(X, mu_q, sigma_q, pi_q, mu_prior, sigma_prior)
            elbo_history.append(elbo)

            # 检查收敛
            mu_change = max(np.linalg.norm(mu_q[k] - old_mu_q[k]) for k in range(n_components))
            sigma_change = np.linalg.norm(np.array(sigma_q) - np.array(old_sigma_q))

            if max(mu_change, sigma_change) < tolerance:
                print(f"变分推断在第{iteration+1}次迭代后收敛")
                break

        return {
            'mu_q': mu_q,
            'sigma_q': sigma_q,
            'pi_q': pi_q,
            'responsibilities': responsibilities,
            'elbo_history': elbo_history
        }

class VariationalAutoencoder:
    """变分自编码器简化实现"""

    def __init__(self, input_dim, latent_dim, hidden_dim=64):
        """
        VAE核心思想：
        1. 编码器：q(z|x) - 将输入映射到潜在空间
        2. 解码器：p(x|z) - 从潜在空间重构输入
        3. 变分目标：最大化ELBO = E_q[log p(x|z)] - KL(q(z|x) || p(z))

        重参数化技巧：z = μ + σ * ε, ε ~ N(0,I)
        """
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.hidden_dim = hidden_dim

        # 简化的线性层参数
        self.encoder_weights = {
            'W1': np.random.randn(input_dim, hidden_dim) * 0.1,
            'b1': np.zeros(hidden_dim),
            'W_mu': np.random.randn(hidden_dim, latent_dim) * 0.1,
            'b_mu': np.zeros(latent_dim),
            'W_logvar': np.random.randn(hidden_dim, latent_dim) * 0.1,
            'b_logvar': np.zeros(latent_dim)
        }

        self.decoder_weights = {
            'W1': np.random.randn(latent_dim, hidden_dim) * 0.1,
            'b1': np.zeros(hidden_dim),
            'W2': np.random.randn(hidden_dim, input_dim) * 0.1,
            'b2': np.zeros(input_dim)
        }

    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)

    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

    def encode(self, x):
        """编码器：输入 -> 潜在分布参数"""
        # 隐藏层
        h = self.relu(x @ self.encoder_weights['W1'] + self.encoder_weights['b1'])

        # 潜在分布参数
        mu = h @ self.encoder_weights['W_mu'] + self.encoder_weights['b_mu']
        logvar = h @ self.encoder_weights['W_logvar'] + self.encoder_weights['b_logvar']

        return mu, logvar

    def reparameterize(self, mu, logvar):
        """重参数化技巧"""
        std = np.exp(0.5 * logvar)
        eps = np.random.randn(*mu.shape)
        return mu + std * eps

    def decode(self, z):
        """解码器：潜在变量 -> 重构输出"""
        # 隐藏层
        h = self.relu(z @ self.decoder_weights['W1'] + self.decoder_weights['b1'])

        # 输出层
        x_recon = self.sigmoid(h @ self.decoder_weights['W2'] + self.decoder_weights['b2'])

        return x_recon

    def compute_loss(self, x):
        """计算VAE损失"""
        # 编码
        mu, logvar = self.encode(x)

        # 重参数化
        z = self.reparameterize(mu, logvar)

        # 解码
        x_recon = self.decode(z)

        # 重构损失 (负对数似然)
        recon_loss = np.mean((x - x_recon)**2)

        # KL散度损失
        kl_loss = -0.5 * np.mean(1 + logvar - mu**2 - np.exp(logvar))

        # 总损失
        total_loss = recon_loss + kl_loss

        return {
            'total_loss': total_loss,
            'recon_loss': recon_loss,
            'kl_loss': kl_loss,
            'x_recon': x_recon,
            'mu': mu,
            'logvar': logvar,
            'z': z
        }

# 变分推断演示
def demonstrate_variational_inference():
    """变分推断演示"""

    print("=== 变分推断与近似推理演示 ===")

    # 1. 高斯混合模型的变分推断
    print("\n1. 高斯混合模型变分推断")

    # 生成混合高斯数据
    np.random.seed(42)
    n_samples = 300

    # 第一个高斯分量
    X1 = np.random.multivariate_normal([2, 2], [[1, 0.5], [0.5, 1]], n_samples//2)
    # 第二个高斯分量
    X2 = np.random.multivariate_normal([-1, -1], [[1, -0.3], [-0.3, 1]], n_samples//2)

    X = np.vstack([X1, X2])

    print(f"生成数据: {X.shape[0]}个样本, {X.shape[1]}个特征")

    # 变分推断
    vi = VariationalInference()
    vi_results = vi.mean_field_vi_gaussian_mixture(X, n_components=2, max_iterations=50)

    print(f"变分推断结果:")
    for k in range(2):
        print(f"  组件 {k+1}:")
        print(f"    均值: {vi_results['mu_q'][k]}")
        print(f"    标准差: {vi_results['sigma_q'][k]:.3f}")
        print(f"    权重: {vi_results['pi_q'][k]:.3f}")

    # 与标准EM算法比较
    gmm = GaussianMixture(n_components=2, random_state=42)
    gmm.fit(X)

    print(f"\n标准EM算法结果:")
    for k in range(2):
        print(f"  组件 {k+1}:")
        print(f"    均值: {gmm.means_[k]}")
        print(f"    协方差对角线: {np.diag(gmm.covariances_[k])}")
        print(f"    权重: {gmm.weights_[k]:.3f}")

    # 2. 变分自编码器演示
    print(f"\n2. 变分自编码器演示")

    # 生成简单的2D数据
    n_data = 100
    input_dim = 4
    latent_dim = 2

    # 创建一些结构化数据
    data = np.random.randn(n_data, input_dim)
    data[:n_data//2, :2] += 2  # 第一组数据偏移
    data[n_data//2:, 2:] += 2  # 第二组数据偏移

    # 归一化到[0,1]
    data = (data - data.min()) / (data.max() - data.min())

    print(f"输入数据形状: {data.shape}")

    # 创建VAE
    vae = VariationalAutoencoder(input_dim, latent_dim)

    # 计算损失
    loss_info = vae.compute_loss(data)

    print(f"VAE损失分析:")
    print(f"  总损失: {loss_info['total_loss']:.4f}")
    print(f"  重构损失: {loss_info['recon_loss']:.4f}")
    print(f"  KL散度损失: {loss_info['kl_loss']:.4f}")

    # 3. 可视化分析
    print(f"\n3. 变分推断可视化分析")

    plt.figure(figsize=(20, 12))

    # 原始数据和聚类结果
    plt.subplot(3, 4, 1)
    plt.scatter(X[:, 0], X[:, 1], alpha=0.6, c='blue')
    plt.title('原始混合高斯数据')
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.grid(True, alpha=0.3)

    # 变分推断聚类结果
    plt.subplot(3, 4, 2)
    colors = ['red', 'blue']
    for k in range(2):
        mask = vi_results['responsibilities'][:, k] > 0.5
        plt.scatter(X[mask, 0], X[mask, 1], alpha=0.6, c=colors[k], label=f'组件{k+1}')

    # 绘制变分分布的均值
    for k in range(2):
        plt.scatter(vi_results['mu_q'][k][0], vi_results['mu_q'][k][1],
                   c=colors[k], marker='x', s=200, linewidths=3)

    plt.title('变分推断聚类结果')
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # EM算法聚类结果
    plt.subplot(3, 4, 3)
    em_labels = gmm.predict(X)
    for k in range(2):
        mask = em_labels == k
        plt.scatter(X[mask, 0], X[mask, 1], alpha=0.6, c=colors[k], label=f'组件{k+1}')

    # 绘制EM算法的均值
    for k in range(2):
        plt.scatter(gmm.means_[k, 0], gmm.means_[k, 1],
                   c=colors[k], marker='x', s=200, linewidths=3)

    plt.title('EM算法聚类结果')
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # ELBO收敛曲线
    plt.subplot(3, 4, 4)
    plt.plot(vi_results['elbo_history'], 'b-', linewidth=2)
    plt.title('ELBO收敛曲线')
    plt.xlabel('迭代次数')
    plt.ylabel('ELBO')
    plt.grid(True, alpha=0.3)

    # 责任度热力图
    plt.subplot(3, 4, 5)
    plt.imshow(vi_results['responsibilities'].T, cmap='Blues', aspect='auto')
    plt.colorbar(label='责任度')
    plt.title('样本-组件责任度')
    plt.xlabel('样本索引')
    plt.ylabel('组件')

    # VAE潜在空间可视化
    plt.subplot(3, 4, 6)
    z_samples = loss_info['z']
    plt.scatter(z_samples[:, 0], z_samples[:, 1], alpha=0.6)
    plt.title('VAE潜在空间')
    plt.xlabel('潜在维度1')
    plt.ylabel('潜在维度2')
    plt.grid(True, alpha=0.3)

    # VAE重构误差
    plt.subplot(3, 4, 7)
    recon_errors = np.mean((data - loss_info['x_recon'])**2, axis=1)
    plt.hist(recon_errors, bins=20, alpha=0.7, edgecolor='black')
    plt.title('重构误差分布')
    plt.xlabel('重构误差')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)

    # KL散度分析
    plt.subplot(3, 4, 8)
    mu = loss_info['mu']
    logvar = loss_info['logvar']
    kl_per_sample = -0.5 * np.sum(1 + logvar - mu**2 - np.exp(logvar), axis=1)

    plt.scatter(range(len(kl_per_sample)), kl_per_sample, alpha=0.6)
    plt.title('每样本KL散度')
    plt.xlabel('样本索引')
    plt.ylabel('KL散度')
    plt.grid(True, alpha=0.3)

    # 变分分布vs先验分布
    plt.subplot(3, 4, 9)

    # 绘制潜在变量的边际分布
    z_dim1 = z_samples[:, 0]
    z_dim2 = z_samples[:, 1]

    plt.hist(z_dim1, bins=20, alpha=0.5, label='潜在维度1', density=True)
    plt.hist(z_dim2, bins=20, alpha=0.5, label='潜在维度2', density=True)

    # 绘制标准正态分布
    x_range = np.linspace(-3, 3, 100)
    plt.plot(x_range, norm.pdf(x_range, 0, 1), 'r--', label='标准正态分布')

    plt.title('潜在分布 vs 先验分布')
    plt.xlabel('值')
    plt.ylabel('密度')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 重构质量比较
    plt.subplot(3, 4, 10)

    # 选择几个样本进行重构比较
    sample_indices = [0, 25, 50, 75]
    x_pos = np.arange(len(sample_indices))

    original_norms = [np.linalg.norm(data[i]) for i in sample_indices]
    recon_norms = [np.linalg.norm(loss_info['x_recon'][i]) for i in sample_indices]

    width = 0.35
    plt.bar(x_pos - width/2, original_norms, width, label='原始', alpha=0.7)
    plt.bar(x_pos + width/2, recon_norms, width, label='重构', alpha=0.7)

    plt.xlabel('样本')
    plt.ylabel('L2范数')
    plt.title('原始 vs 重构数据')
    plt.xticks(x_pos, [f'样本{i}' for i in sample_indices])
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 变分推断理论图解
    plt.subplot(3, 4, 11)

    # 绘制概念图
    plt.text(0.5, 0.9, '变分推断框架', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    plt.text(0.2, 0.7, '真实后验\np(z|x)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))

    plt.text(0.8, 0.7, '变分分布\nq(z|θ)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

    plt.text(0.5, 0.5, '最小化\nKL(q||p)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))

    plt.text(0.5, 0.3, '最大化\nELBO', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

    # 绘制箭头
    plt.annotate('', xy=(0.7, 0.7), xytext=(0.3, 0.7),
                arrowprops=dict(arrowstyle='<->', lw=2))
    plt.annotate('', xy=(0.5, 0.6), xytext=(0.5, 0.4),
                arrowprops=dict(arrowstyle='<->', lw=2))

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 算法比较
    plt.subplot(3, 4, 12)

    methods = ['精确推断', '变分推断', 'MCMC', 'EM算法']
    accuracy = [5, 3, 4, 3]
    speed = [1, 4, 2, 4]
    scalability = [1, 4, 2, 3]

    x = np.arange(len(methods))
    width = 0.25

    plt.bar(x - width, accuracy, width, label='准确性', alpha=0.7)
    plt.bar(x, speed, width, label='速度', alpha=0.7)
    plt.bar(x + width, scalability, width, label='可扩展性', alpha=0.7)

    plt.xlabel('方法')
    plt.ylabel('评分 (1-5)')
    plt.title('推断方法比较')
    plt.xticks(x, methods, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    print(f"\n变分推断总结:")
    print(f"  核心思想：用简单分布近似复杂后验分布")
    print(f"  优化目标：最小化KL散度或最大化ELBO")
    print(f"  平均场假设：假设变量间相互独立")
    print(f"  应用场景：大规模数据、复杂模型的近似推断")
    print(f"  优势：计算效率高、可扩展性好")
    print(f"  劣势：近似质量依赖于变分族的选择")

# 运行变分推断演示
demonstrate_variational_inference()
```

## 11.3 博弈论与纳什均衡

### 11.3.1 博弈论基础与纳什均衡

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize, linprog
import itertools

class GameTheory:
    """博弈论与纳什均衡的完整实现"""

    def __init__(self):
        """
        博弈论核心概念：
        1. 博弈：多个理性决策者的策略互动
        2. 策略：参与者可选择的行动方案
        3. 支付：每种策略组合下的收益
        4. 纳什均衡：所有参与者都无法通过单方面改变策略获得更高收益

        数学表示：
        - 博弈 G = (N, S, u)
        - N: 参与者集合
        - S: 策略空间
        - u: 支付函数
        """
        pass

    def find_pure_nash_equilibria(self, payoff_matrix_1, payoff_matrix_2):
        """寻找纯策略纳什均衡"""
        m, n = payoff_matrix_1.shape
        equilibria = []

        for i in range(m):
            for j in range(n):
                # 检查是否为纳什均衡
                is_equilibrium = True

                # 检查参与者1是否有偏离的动机
                for i_prime in range(m):
                    if payoff_matrix_1[i_prime, j] > payoff_matrix_1[i, j]:
                        is_equilibrium = False
                        break

                # 检查参与者2是否有偏离的动机
                if is_equilibrium:
                    for j_prime in range(n):
                        if payoff_matrix_2[i, j_prime] > payoff_matrix_2[i, j]:
                            is_equilibrium = False
                            break

                if is_equilibrium:
                    equilibria.append((i, j))

        return equilibria

    def find_mixed_nash_equilibrium_2x2(self, payoff_matrix_1, payoff_matrix_2):
        """寻找2x2博弈的混合策略纳什均衡"""
        # 参与者1的混合策略概率
        # 参与者2必须对参与者1的两个纯策略无差异

        # 计算参与者1的混合策略概率
        a11, a12 = payoff_matrix_1[0, 0], payoff_matrix_1[0, 1]
        a21, a22 = payoff_matrix_1[1, 0], payoff_matrix_1[1, 1]

        b11, b12 = payoff_matrix_2[0, 0], payoff_matrix_2[0, 1]
        b21, b22 = payoff_matrix_2[1, 0], payoff_matrix_2[1, 1]

        # 参与者2的无差异条件：b11*p + b21*(1-p) = b12*p + b22*(1-p)
        # 解得：p = (b22 - b21) / (b11 - b12 - b21 + b22)
        denominator_p = b11 - b12 - b21 + b22
        if abs(denominator_p) > 1e-10:
            p = (b22 - b21) / denominator_p
        else:
            p = None

        # 参与者1的无差异条件：a11*q + a12*(1-q) = a21*q + a22*(1-q)
        # 解得：q = (a22 - a12) / (a11 - a12 - a21 + a22)
        denominator_q = a11 - a12 - a21 + a22
        if abs(denominator_q) > 1e-10:
            q = (a22 - a12) / denominator_q
        else:
            q = None

        # 检查概率是否有效
        if p is not None and q is not None and 0 <= p <= 1 and 0 <= q <= 1:
            return (p, 1-p), (q, 1-q)
        else:
            return None

    def compute_expected_payoff(self, payoff_matrix, strategy_1, strategy_2):
        """计算期望支付"""
        expected_payoff = 0
        for i in range(len(strategy_1)):
            for j in range(len(strategy_2)):
                expected_payoff += strategy_1[i] * strategy_2[j] * payoff_matrix[i, j]
        return expected_payoff

    def is_nash_equilibrium(self, payoff_matrix_1, payoff_matrix_2, strategy_1, strategy_2, tolerance=1e-6):
        """检验是否为纳什均衡"""
        m, n = payoff_matrix_1.shape

        # 计算当前期望支付
        current_payoff_1 = self.compute_expected_payoff(payoff_matrix_1, strategy_1, strategy_2)
        current_payoff_2 = self.compute_expected_payoff(payoff_matrix_2, strategy_1, strategy_2)

        # 检查参与者1是否有偏离动机
        for i in range(m):
            pure_strategy_1 = [0] * m
            pure_strategy_1[i] = 1
            deviation_payoff_1 = self.compute_expected_payoff(payoff_matrix_1, pure_strategy_1, strategy_2)

            if deviation_payoff_1 > current_payoff_1 + tolerance:
                return False

        # 检查参与者2是否有偏离动机
        for j in range(n):
            pure_strategy_2 = [0] * n
            pure_strategy_2[j] = 1
            deviation_payoff_2 = self.compute_expected_payoff(payoff_matrix_2, strategy_1, pure_strategy_2)

            if deviation_payoff_2 > current_payoff_2 + tolerance:
                return False

        return True

class ZeroSumGame:
    """零和博弈求解"""

    def __init__(self, payoff_matrix):
        """
        零和博弈特点：
        1. 一方的收益等于另一方的损失
        2. 总收益为零
        3. 可以用线性规划求解

        最大最小定理：
        max_x min_y x^T A y = min_y max_x x^T A y
        """
        self.payoff_matrix = np.array(payoff_matrix)
        self.m, self.n = self.payoff_matrix.shape

    def solve_with_linear_programming(self):
        """用线性规划求解零和博弈"""

        # 求解行参与者的最优混合策略
        # max v subject to:
        # sum_i x_i * A_ij >= v for all j
        # sum_i x_i = 1
        # x_i >= 0

        # 转换为标准形式：min -v
        c = np.zeros(self.m + 1)
        c[-1] = -1  # 最大化v等价于最小化-v

        # 不等式约束：-sum_i x_i * A_ij + v <= 0
        A_ub = np.hstack([-self.payoff_matrix.T, np.ones((self.n, 1))])
        b_ub = np.zeros(self.n)

        # 等式约束：sum_i x_i = 1
        A_eq = np.zeros((1, self.m + 1))
        A_eq[0, :self.m] = 1
        b_eq = np.array([1])

        # 变量边界：x_i >= 0, v无约束
        bounds = [(0, None)] * self.m + [(None, None)]

        # 求解
        result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq,
                        bounds=bounds, method='highs')

        if result.success:
            row_strategy = result.x[:self.m]
            game_value = result.x[-1]

            # 求解列参与者的最优策略
            # 对偶问题
            c_dual = np.zeros(self.n + 1)
            c_dual[-1] = 1  # 最小化u

            A_ub_dual = np.hstack([self.payoff_matrix, -np.ones((self.m, 1))])
            b_ub_dual = np.zeros(self.m)

            A_eq_dual = np.zeros((1, self.n + 1))
            A_eq_dual[0, :self.n] = 1
            b_eq_dual = np.array([1])

            bounds_dual = [(0, None)] * self.n + [(None, None)]

            result_dual = linprog(c_dual, A_ub=A_ub_dual, b_ub=b_ub_dual,
                                 A_eq=A_eq_dual, b_eq=b_eq_dual,
                                 bounds=bounds_dual, method='highs')

            if result_dual.success:
                col_strategy = result_dual.x[:self.n]
                return {
                    'row_strategy': row_strategy,
                    'col_strategy': col_strategy,
                    'game_value': game_value,
                    'success': True
                }

        return {'success': False}

    def minimax_value(self):
        """计算最大最小值"""
        # 行参与者的最大最小策略
        row_minimax = []
        for i in range(self.m):
            min_payoff = np.min(self.payoff_matrix[i, :])
            row_minimax.append(min_payoff)

        maximin_value = np.max(row_minimax)
        maximin_strategy = np.argmax(row_minimax)

        # 列参与者的最小最大策略
        col_maximin = []
        for j in range(self.n):
            max_payoff = np.max(self.payoff_matrix[:, j])
            col_maximin.append(max_payoff)

        minimax_value = np.min(col_maximin)
        minimax_strategy = np.argmin(col_maximin)

        return {
            'maximin_value': maximin_value,
            'maximin_strategy': maximin_strategy,
            'minimax_value': minimax_value,
            'minimax_strategy': minimax_strategy,
            'has_saddle_point': abs(maximin_value - minimax_value) < 1e-10
        }

# 博弈论演示
def demonstrate_game_theory():
    """博弈论与纳什均衡演示"""

    print("=== 博弈论与纳什均衡演示 ===")

    # 1. 经典博弈：囚徒困境
    print("\n1. 囚徒困境")

    # 支付矩阵 (合作, 背叛)
    # 第一个数字是行参与者收益，第二个是列参与者收益
    prisoner_payoff_1 = np.array([
        [3, 0],  # 合作: (合作,合作)=3, (合作,背叛)=0
        [5, 1]   # 背叛: (背叛,合作)=5, (背叛,背叛)=1
    ])

    prisoner_payoff_2 = np.array([
        [3, 5],  # 对方合作: (合作,合作)=3, (背叛,合作)=5
        [0, 1]   # 对方背叛: (合作,背叛)=0, (背叛,背叛)=1
    ])

    game_theory = GameTheory()

    print("囚徒困境支付矩阵:")
    print("参与者1 \\ 参与者2  合作    背叛")
    print("合作                (3,3)   (0,5)")
    print("背叛                (5,0)   (1,1)")

    # 寻找纯策略纳什均衡
    pure_equilibria = game_theory.find_pure_nash_equilibria(prisoner_payoff_1, prisoner_payoff_2)
    print(f"\n纯策略纳什均衡: {pure_equilibria}")

    strategies = ['合作', '背叛']
    for eq in pure_equilibria:
        print(f"  ({strategies[eq[0]]}, {strategies[eq[1]]})")

    # 2. 性别战争博弈
    print(f"\n2. 性别战争博弈")

    # 支付矩阵 (足球, 购物)
    battle_payoff_1 = np.array([
        [2, 0],  # 足球: (足球,足球)=2, (足球,购物)=0
        [0, 1]   # 购物: (购物,足球)=0, (购物,购物)=1
    ])

    battle_payoff_2 = np.array([
        [1, 0],  # 足球: (足球,足球)=1, (足球,购物)=0
        [0, 2]   # 购物: (购物,足球)=0, (购物,购物)=2
    ])

    print("性别战争支付矩阵:")
    print("参与者1 \\ 参与者2  足球    购物")
    print("足球                (2,1)   (0,0)")
    print("购物                (0,0)   (1,2)")

    pure_equilibria_battle = game_theory.find_pure_nash_equilibria(battle_payoff_1, battle_payoff_2)
    print(f"\n纯策略纳什均衡: {pure_equilibria_battle}")

    activities = ['足球', '购物']
    for eq in pure_equilibria_battle:
        print(f"  ({activities[eq[0]]}, {activities[eq[1]]})")

    # 寻找混合策略纳什均衡
    mixed_equilibrium = game_theory.find_mixed_nash_equilibrium_2x2(battle_payoff_1, battle_payoff_2)
    if mixed_equilibrium:
        strategy_1, strategy_2 = mixed_equilibrium
        print(f"\n混合策略纳什均衡:")
        print(f"  参与者1: 足球概率={strategy_1[0]:.3f}, 购物概率={strategy_1[1]:.3f}")
        print(f"  参与者2: 足球概率={strategy_2[0]:.3f}, 购物概率={strategy_2[1]:.3f}")

        # 验证是否为纳什均衡
        is_equilibrium = game_theory.is_nash_equilibrium(
            battle_payoff_1, battle_payoff_2, strategy_1, strategy_2
        )
        print(f"  验证结果: {'是纳什均衡' if is_equilibrium else '不是纳什均衡'}")

    # 3. 零和博弈：石头剪刀布
    print(f"\n3. 零和博弈：石头剪刀布")

    # 支付矩阵 (石头, 剪刀, 布)
    rps_payoff = np.array([
        [ 0, 1, -1],  # 石头 vs [石头, 剪刀, 布]
        [-1, 0,  1],  # 剪刀 vs [石头, 剪刀, 布]
        [ 1, -1, 0]   # 布 vs [石头, 剪刀, 布]
    ])

    print("石头剪刀布支付矩阵:")
    print("参与者1 \\ 参与者2  石头  剪刀   布")
    print("石头                  0     1    -1")
    print("剪刀                 -1     0     1")
    print("布                    1    -1     0")

    zero_sum_game = ZeroSumGame(rps_payoff)

    # 计算最大最小值
    minimax_result = zero_sum_game.minimax_value()
    print(f"\n最大最小分析:")
    print(f"  最大最小值: {minimax_result['maximin_value']}")
    print(f"  最小最大值: {minimax_result['minimax_value']}")
    print(f"  是否有鞍点: {minimax_result['has_saddle_point']}")

    # 用线性规划求解
    lp_solution = zero_sum_game.solve_with_linear_programming()
    if lp_solution['success']:
        print(f"\n线性规划求解结果:")
        print(f"  行参与者最优策略: {lp_solution['row_strategy']}")
        print(f"  列参与者最优策略: {lp_solution['col_strategy']}")
        print(f"  博弈值: {lp_solution['game_value']:.6f}")

        choices = ['石头', '剪刀', '布']
        print(f"  策略解释:")
        for i, prob in enumerate(lp_solution['row_strategy']):
            print(f"    {choices[i]}: {prob:.3f}")

    # 4. 可视化分析
    print(f"\n4. 博弈论可视化分析")

    plt.figure(figsize=(20, 12))

    # 囚徒困境支付矩阵可视化
    plt.subplot(3, 4, 1)
    combined_payoff = prisoner_payoff_1 + prisoner_payoff_2  # 总收益
    plt.imshow(combined_payoff, cmap='RdYlBu', aspect='auto')
    plt.colorbar(label='总收益')
    plt.title('囚徒困境总收益')
    plt.xticks([0, 1], ['合作', '背叛'])
    plt.yticks([0, 1], ['合作', '背叛'])
    plt.xlabel('参与者2')
    plt.ylabel('参与者1')

    # 在每个格子中显示收益
    for i in range(2):
        for j in range(2):
            plt.text(j, i, f'({prisoner_payoff_1[i,j]},{prisoner_payoff_2[i,j]})',
                    ha='center', va='center', fontsize=10, weight='bold')

    # 性别战争支付矩阵
    plt.subplot(3, 4, 2)
    combined_battle = battle_payoff_1 + battle_payoff_2
    plt.imshow(combined_battle, cmap='RdYlBu', aspect='auto')
    plt.colorbar(label='总收益')
    plt.title('性别战争总收益')
    plt.xticks([0, 1], ['足球', '购物'])
    plt.yticks([0, 1], ['足球', '购物'])
    plt.xlabel('参与者2')
    plt.ylabel('参与者1')

    for i in range(2):
        for j in range(2):
            plt.text(j, i, f'({battle_payoff_1[i,j]},{battle_payoff_2[i,j]})',
                    ha='center', va='center', fontsize=10, weight='bold')

    # 零和博弈支付矩阵
    plt.subplot(3, 4, 3)
    plt.imshow(rps_payoff, cmap='RdBu', aspect='auto')
    plt.colorbar(label='参与者1收益')
    plt.title('石头剪刀布支付矩阵')
    plt.xticks([0, 1, 2], ['石头', '剪刀', '布'])
    plt.yticks([0, 1, 2], ['石头', '剪刀', '布'])
    plt.xlabel('参与者2')
    plt.ylabel('参与者1')

    for i in range(3):
        for j in range(3):
            plt.text(j, i, f'{rps_payoff[i,j]}',
                    ha='center', va='center', fontsize=12, weight='bold')

    # 混合策略概率分布
    plt.subplot(3, 4, 4)
    if mixed_equilibrium and lp_solution['success']:
        strategies = ['策略1', '策略2', '策略3']

        # 性别战争混合策略
        battle_probs_1 = list(mixed_equilibrium[0])
        battle_probs_2 = list(mixed_equilibrium[1])

        x = np.arange(2)
        width = 0.35

        plt.bar(x - width/2, battle_probs_1, width, label='参与者1', alpha=0.7)
        plt.bar(x + width/2, battle_probs_2, width, label='参与者2', alpha=0.7)

        plt.xlabel('策略')
        plt.ylabel('概率')
        plt.title('性别战争混合策略')
        plt.xticks(x, ['足球', '购物'])
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 石头剪刀布最优策略
    plt.subplot(3, 4, 5)
    if lp_solution['success']:
        choices = ['石头', '剪刀', '布']
        plt.pie(lp_solution['row_strategy'], labels=choices, autopct='%1.1f%%')
        plt.title('石头剪刀布最优策略')

    # 博弈类型比较
    plt.subplot(3, 4, 6)
    game_types = ['囚徒困境', '性别战争', '零和博弈']
    equilibrium_counts = [1, 3, 1]  # 纳什均衡数量

    plt.bar(game_types, equilibrium_counts, alpha=0.7)
    plt.title('纳什均衡数量比较')
    plt.ylabel('均衡数量')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 收益分析
    plt.subplot(3, 4, 7)

    # 囚徒困境中不同策略组合的收益
    outcomes = ['(合作,合作)', '(合作,背叛)', '(背叛,合作)', '(背叛,背叛)']
    player1_payoffs = [3, 0, 5, 1]
    player2_payoffs = [3, 5, 0, 1]

    x = np.arange(len(outcomes))
    width = 0.35

    plt.bar(x - width/2, player1_payoffs, width, label='参与者1', alpha=0.7)
    plt.bar(x + width/2, player2_payoffs, width, label='参与者2', alpha=0.7)

    plt.xlabel('策略组合')
    plt.ylabel('收益')
    plt.title('囚徒困境收益分析')
    plt.xticks(x, outcomes, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 博弈论概念图
    plt.subplot(3, 4, 8)

    plt.text(0.5, 0.9, '博弈论核心概念', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    plt.text(0.2, 0.7, '参与者\n(Players)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

    plt.text(0.8, 0.7, '策略\n(Strategies)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

    plt.text(0.2, 0.3, '支付\n(Payoffs)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))

    plt.text(0.8, 0.3, '纳什均衡\n(Nash Equilibrium)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 策略类型比较
    plt.subplot(3, 4, 9)

    strategy_types = ['纯策略', '混合策略']
    advantages = ['简单直观', '更灵活']
    disadvantages = ['可能不存在均衡', '计算复杂']

    plt.text(0.5, 0.9, '策略类型比较', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    plt.text(0.25, 0.7, '纯策略', ha='center', va='center',
             transform=plt.gca().transAxes, fontsize=12, weight='bold')
    plt.text(0.25, 0.6, '• 确定性选择', ha='center', va='center',
             transform=plt.gca().transAxes)
    plt.text(0.25, 0.5, '• 简单直观', ha='center', va='center',
             transform=plt.gca().transAxes)
    plt.text(0.25, 0.4, '• 可能不存在', ha='center', va='center',
             transform=plt.gca().transAxes)

    plt.text(0.75, 0.7, '混合策略', ha='center', va='center',
             transform=plt.gca().transAxes, fontsize=12, weight='bold')
    plt.text(0.75, 0.6, '• 概率性选择', ha='center', va='center',
             transform=plt.gca().transAxes)
    plt.text(0.75, 0.5, '• 总是存在', ha='center', va='center',
             transform=plt.gca().transAxes)
    plt.text(0.75, 0.4, '• 计算复杂', ha='center', va='center',
             transform=plt.gca().transAxes)

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 应用领域
    plt.subplot(3, 4, 10)

    applications = ['经济学', '计算机科学', '生物学', '政治学', '军事']
    importance = [5, 4, 3, 4, 3]

    plt.barh(applications, importance, alpha=0.7)
    plt.xlabel('重要性评分')
    plt.title('博弈论应用领域')
    plt.grid(True, alpha=0.3)

    # 算法复杂度比较
    plt.subplot(3, 4, 11)

    algorithms = ['纯策略搜索', '混合策略计算', '线性规划', '迭代算法']
    complexity = [2, 4, 3, 4]  # 相对复杂度

    colors = ['green', 'orange', 'blue', 'red']
    plt.bar(algorithms, complexity, color=colors, alpha=0.7)
    plt.ylabel('复杂度等级')
    plt.title('求解算法复杂度')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 博弈分类
    plt.subplot(3, 4, 12)

    # 创建博弈分类树状图
    plt.text(0.5, 0.9, '博弈分类', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    plt.text(0.25, 0.7, '合作博弈', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

    plt.text(0.75, 0.7, '非合作博弈', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))

    plt.text(0.15, 0.5, '零和', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow"))

    plt.text(0.35, 0.5, '非零和', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow"))

    plt.text(0.65, 0.5, '完全信息', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.2", facecolor="lightblue"))

    plt.text(0.85, 0.5, '不完全信息', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.2", facecolor="lightblue"))

    # 绘制连接线
    plt.plot([0.5, 0.25], [0.85, 0.75], 'k-', transform=plt.gca().transAxes)
    plt.plot([0.5, 0.75], [0.85, 0.75], 'k-', transform=plt.gca().transAxes)
    plt.plot([0.25, 0.15], [0.65, 0.55], 'k-', transform=plt.gca().transAxes)
    plt.plot([0.25, 0.35], [0.65, 0.55], 'k-', transform=plt.gca().transAxes)
    plt.plot([0.75, 0.65], [0.65, 0.55], 'k-', transform=plt.gca().transAxes)
    plt.plot([0.75, 0.85], [0.65, 0.55], 'k-', transform=plt.gca().transAxes)

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    plt.tight_layout()
    plt.show()

    print(f"\n博弈论总结:")
    print(f"  纳什均衡：所有参与者都无法通过单方面改变策略获益")
    print(f"  纯策略均衡：参与者选择确定的策略")
    print(f"  混合策略均衡：参与者按概率选择策略")
    print(f"  零和博弈：一方收益等于另一方损失")
    print(f"  应用：拍卖理论、机制设计、多智能体系统")

# 运行博弈论演示
demonstrate_game_theory()
```

---

# 第十二章：高级集成学习方法

> **核心理念**: 集成学习通过组合多个学习器的预测来提高整体性能，是现代机器学习中最重要的技术之一，在各种竞赛和实际应用中都表现出色。

## 12.1 Stacking与多层集成

### 12.1.1 Stacking算法实现

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import KFold, cross_val_predict
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.datasets import make_classification
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class StackingEnsemble:
    """Stacking集成学习的完整实现"""

    def __init__(self, base_models, meta_model, cv_folds=5):
        """
        Stacking核心思想：
        1. 第一层：训练多个不同的基学习器
        2. 第二层：使用基学习器的预测作为特征训练元学习器
        3. 交叉验证：避免过拟合，生成无偏的元特征

        数学表示：
        - 基学习器：h₁, h₂, ..., hₘ
        - 元学习器：g(h₁(x), h₂(x), ..., hₘ(x))
        - 最终预测：ŷ = g(h₁(x), h₂(x), ..., hₘ(x))
        """
        self.base_models = base_models
        self.meta_model = meta_model
        self.cv_folds = cv_folds
        self.trained_base_models = []
        self.trained_meta_model = None

    def fit(self, X, y):
        """训练Stacking集成模型"""
        n_samples = X.shape[0]
        n_models = len(self.base_models)

        # 生成元特征矩阵
        meta_features = np.zeros((n_samples, n_models))

        # 使用交叉验证生成元特征
        kfold = KFold(n_splits=self.cv_folds, shuffle=True, random_state=42)

        for i, (model_name, model) in enumerate(self.base_models.items()):
            print(f"训练基学习器: {model_name}")

            # 交叉验证预测
            cv_predictions = cross_val_predict(model, X, y, cv=kfold, method='predict_proba')

            # 对于二分类，取正类概率；多分类取最大概率
            if cv_predictions.shape[1] == 2:
                meta_features[:, i] = cv_predictions[:, 1]
            else:
                meta_features[:, i] = np.max(cv_predictions, axis=1)

        # 在全部数据上训练基学习器
        self.trained_base_models = []
        for model_name, model in self.base_models.items():
            trained_model = model.__class__(**model.get_params())
            trained_model.fit(X, y)
            self.trained_base_models.append((model_name, trained_model))

        # 训练元学习器
        print("训练元学习器...")
        self.trained_meta_model = self.meta_model.__class__(**self.meta_model.get_params())
        self.trained_meta_model.fit(meta_features, y)

        return self

    def predict(self, X):
        """预测"""
        if not self.trained_base_models or self.trained_meta_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")

        # 生成基学习器预测
        base_predictions = np.zeros((X.shape[0], len(self.trained_base_models)))

        for i, (model_name, model) in enumerate(self.trained_base_models):
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X)
                if proba.shape[1] == 2:
                    base_predictions[:, i] = proba[:, 1]
                else:
                    base_predictions[:, i] = np.max(proba, axis=1)
            else:
                base_predictions[:, i] = model.predict(X)

        # 元学习器预测
        final_predictions = self.trained_meta_model.predict(base_predictions)
        return final_predictions

    def predict_proba(self, X):
        """预测概率"""
        if not self.trained_base_models or self.trained_meta_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")

        # 生成基学习器预测
        base_predictions = np.zeros((X.shape[0], len(self.trained_base_models)))

        for i, (model_name, model) in enumerate(self.trained_base_models):
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X)
                if proba.shape[1] == 2:
                    base_predictions[:, i] = proba[:, 1]
                else:
                    base_predictions[:, i] = np.max(proba, axis=1)
            else:
                base_predictions[:, i] = model.predict(X)

        # 元学习器预测概率
        if hasattr(self.trained_meta_model, 'predict_proba'):
            return self.trained_meta_model.predict_proba(base_predictions)
        else:
            # 如果元学习器不支持概率预测，返回硬预测
            predictions = self.trained_meta_model.predict(base_predictions)
            n_classes = len(np.unique(predictions))
            proba = np.zeros((len(predictions), n_classes))
            for i, pred in enumerate(predictions):
                proba[i, pred] = 1.0
            return proba

class VotingEnsemble:
    """投票集成的完整实现"""

    def __init__(self, models, voting='hard', weights=None):
        """
        投票集成类型：
        1. 硬投票：基于预测类别的多数投票
        2. 软投票：基于预测概率的加权平均

        权重策略：
        - 等权重：所有模型权重相等
        - 性能权重：根据验证性能分配权重
        - 自适应权重：动态调整权重
        """
        self.models = models
        self.voting = voting
        self.weights = weights
        self.trained_models = []

    def fit(self, X, y):
        """训练投票集成"""
        self.trained_models = []

        for model_name, model in self.models.items():
            print(f"训练模型: {model_name}")
            trained_model = model.__class__(**model.get_params())
            trained_model.fit(X, y)
            self.trained_models.append((model_name, trained_model))

        # 如果没有指定权重，使用等权重
        if self.weights is None:
            self.weights = np.ones(len(self.trained_models)) / len(self.trained_models)

        return self

    def predict(self, X):
        """投票预测"""
        if not self.trained_models:
            raise ValueError("模型尚未训练，请先调用fit方法")

        if self.voting == 'hard':
            # 硬投票
            predictions = np.zeros((X.shape[0], len(self.trained_models)))

            for i, (model_name, model) in enumerate(self.trained_models):
                predictions[:, i] = model.predict(X)

            # 加权投票
            final_predictions = []
            for i in range(X.shape[0]):
                votes = {}
                for j, pred in enumerate(predictions[i]):
                    pred = int(pred)
                    if pred not in votes:
                        votes[pred] = 0
                    votes[pred] += self.weights[j]

                final_pred = max(votes.keys(), key=lambda k: votes[k])
                final_predictions.append(final_pred)

            return np.array(final_predictions)

        else:
            # 软投票
            probabilities = self.predict_proba(X)
            return np.argmax(probabilities, axis=1)

    def predict_proba(self, X):
        """预测概率"""
        if not self.trained_models:
            raise ValueError("模型尚未训练，请先调用fit方法")

        # 收集所有模型的概率预测
        all_probas = []

        for model_name, model in self.trained_models:
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X)
            else:
                # 对于不支持概率预测的模型，使用硬预测
                pred = model.predict(X)
                n_classes = len(np.unique(pred))
                proba = np.zeros((len(pred), n_classes))
                for i, p in enumerate(pred):
                    proba[i, int(p)] = 1.0

            all_probas.append(proba)

        # 加权平均
        weighted_proba = np.zeros_like(all_probas[0])
        for i, proba in enumerate(all_probas):
            weighted_proba += self.weights[i] * proba

        return weighted_proba

class BlendingEnsemble:
    """Blending集成学习实现"""

    def __init__(self, base_models, meta_model, holdout_ratio=0.2):
        """
        Blending与Stacking的区别：
        1. Blending使用固定的holdout集合生成元特征
        2. Stacking使用交叉验证生成元特征
        3. Blending更简单但可能过拟合
        """
        self.base_models = base_models
        self.meta_model = meta_model
        self.holdout_ratio = holdout_ratio
        self.trained_base_models = []
        self.trained_meta_model = None

    def fit(self, X, y):
        """训练Blending集成"""
        # 分割数据
        n_samples = X.shape[0]
        n_holdout = int(n_samples * self.holdout_ratio)

        # 随机分割
        indices = np.random.permutation(n_samples)
        train_indices = indices[n_holdout:]
        holdout_indices = indices[:n_holdout]

        X_train, X_holdout = X[train_indices], X[holdout_indices]
        y_train, y_holdout = y[train_indices], y[holdout_indices]

        # 训练基学习器
        meta_features = np.zeros((n_holdout, len(self.base_models)))
        self.trained_base_models = []

        for i, (model_name, model) in enumerate(self.base_models.items()):
            print(f"训练基学习器: {model_name}")

            # 在训练集上训练
            trained_model = model.__class__(**model.get_params())
            trained_model.fit(X_train, y_train)
            self.trained_base_models.append((model_name, trained_model))

            # 在holdout集上预测
            if hasattr(trained_model, 'predict_proba'):
                proba = trained_model.predict_proba(X_holdout)
                if proba.shape[1] == 2:
                    meta_features[:, i] = proba[:, 1]
                else:
                    meta_features[:, i] = np.max(proba, axis=1)
            else:
                meta_features[:, i] = trained_model.predict(X_holdout)

        # 训练元学习器
        print("训练元学习器...")
        self.trained_meta_model = self.meta_model.__class__(**self.meta_model.get_params())
        self.trained_meta_model.fit(meta_features, y_holdout)

        return self

    def predict(self, X):
        """预测"""
        if not self.trained_base_models or self.trained_meta_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")

        # 生成基学习器预测
        base_predictions = np.zeros((X.shape[0], len(self.trained_base_models)))

        for i, (model_name, model) in enumerate(self.trained_base_models):
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X)
                if proba.shape[1] == 2:
                    base_predictions[:, i] = proba[:, 1]
                else:
                    base_predictions[:, i] = np.max(proba, axis=1)
            else:
                base_predictions[:, i] = model.predict(X)

        # 元学习器预测
        return self.trained_meta_model.predict(base_predictions)

# 高级集成学习演示
def demonstrate_advanced_ensemble():
    """高级集成学习方法演示"""

    print("=== 高级集成学习方法演示 ===")

    # 1. 生成数据
    print("\n1. 数据准备")
    X, y = make_classification(
        n_samples=2000, n_features=20, n_informative=15,
        n_redundant=5, n_clusters_per_class=2, random_state=42
    )

    # 标准化特征
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # 分割数据
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )

    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    print(f"类别分布: {np.bincount(y_train)}")

    # 2. 定义基学习器
    print(f"\n2. 基学习器定义")

    base_models = {
        'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'gradient_boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
        'svm': SVC(probability=True, random_state=42),
        'knn': KNeighborsClassifier(n_neighbors=5),
        'naive_bayes': GaussianNB()
    }

    meta_model = LogisticRegression(random_state=42)

    print("基学习器:")
    for name, model in base_models.items():
        print(f"  {name}: {model.__class__.__name__}")
    print(f"元学习器: {meta_model.__class__.__name__}")

    # 3. Stacking集成
    print(f"\n3. Stacking集成训练")

    stacking = StackingEnsemble(base_models, meta_model, cv_folds=5)
    stacking.fit(X_train, y_train)

    stacking_pred = stacking.predict(X_test)
    stacking_accuracy = accuracy_score(y_test, stacking_pred)

    print(f"Stacking准确率: {stacking_accuracy:.4f}")

    # 4. 投票集成
    print(f"\n4. 投票集成训练")

    # 硬投票
    hard_voting = VotingEnsemble(base_models, voting='hard')
    hard_voting.fit(X_train, y_train)

    hard_voting_pred = hard_voting.predict(X_test)
    hard_voting_accuracy = accuracy_score(y_test, hard_voting_pred)

    print(f"硬投票准确率: {hard_voting_accuracy:.4f}")

    # 软投票
    soft_voting = VotingEnsemble(base_models, voting='soft')
    soft_voting.fit(X_train, y_train)

    soft_voting_pred = soft_voting.predict(X_test)
    soft_voting_accuracy = accuracy_score(y_test, soft_voting_pred)

    print(f"软投票准确率: {soft_voting_accuracy:.4f}")

    # 5. Blending集成
    print(f"\n5. Blending集成训练")

    blending = BlendingEnsemble(base_models, meta_model, holdout_ratio=0.2)
    blending.fit(X_train, y_train)

    blending_pred = blending.predict(X_test)
    blending_accuracy = accuracy_score(y_test, blending_pred)

    print(f"Blending准确率: {blending_accuracy:.4f}")

    # 6. 单个基学习器性能
    print(f"\n6. 基学习器性能对比")

    base_accuracies = {}
    for name, model in base_models.items():
        model.fit(X_train, y_train)
        pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, pred)
        base_accuracies[name] = accuracy
        print(f"{name}: {accuracy:.4f}")

    # 7. 可视化分析
    print(f"\n7. 集成学习可视化分析")

    plt.figure(figsize=(20, 15))

    # 性能比较
    plt.subplot(3, 4, 1)

    methods = ['RF', 'GB', 'SVM', 'KNN', 'NB', 'Hard Vote', 'Soft Vote', 'Stacking', 'Blending']
    accuracies = [
        base_accuracies['random_forest'],
        base_accuracies['gradient_boosting'],
        base_accuracies['svm'],
        base_accuracies['knn'],
        base_accuracies['naive_bayes'],
        hard_voting_accuracy,
        soft_voting_accuracy,
        stacking_accuracy,
        blending_accuracy
    ]

    colors = ['lightblue'] * 5 + ['orange'] * 2 + ['red'] * 2
    bars = plt.bar(methods, accuracies, color=colors, alpha=0.7)
    plt.title('模型性能比较')
    plt.ylabel('准确率')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                f'{acc:.3f}', ha='center', va='bottom', fontsize=8)

    # 集成方法分类
    plt.subplot(3, 4, 2)

    ensemble_types = ['Bagging', 'Boosting', 'Voting', 'Stacking', 'Blending']
    characteristics = ['并行训练', '串行训练', '简单组合', '层次结构', '固定分割']

    plt.text(0.5, 0.9, '集成学习分类', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    for i, (etype, char) in enumerate(zip(ensemble_types, characteristics)):
        y_pos = 0.7 - i * 0.15
        plt.text(0.2, y_pos, etype, ha='left', va='center',
                transform=plt.gca().transAxes, fontsize=10, weight='bold')
        plt.text(0.6, y_pos, char, ha='left', va='center',
                transform=plt.gca().transAxes, fontsize=9)

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 预测概率分布
    plt.subplot(3, 4, 3)

    # 获取Stacking的预测概率
    stacking_proba = stacking.predict_proba(X_test)[:, 1]

    plt.hist(stacking_proba, bins=30, alpha=0.7, edgecolor='black')
    plt.title('Stacking预测概率分布')
    plt.xlabel('预测概率')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)

    # 基学习器相关性分析
    plt.subplot(3, 4, 4)

    # 计算基学习器预测的相关性
    base_predictions = np.zeros((len(X_test), len(base_models)))

    for i, (name, model) in enumerate(base_models.items()):
        if hasattr(model, 'predict_proba'):
            proba = model.predict_proba(X_test)
            base_predictions[:, i] = proba[:, 1] if proba.shape[1] == 2 else np.max(proba, axis=1)
        else:
            base_predictions[:, i] = model.predict(X_test)

    correlation_matrix = np.corrcoef(base_predictions.T)

    import seaborn as sns
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                xticklabels=list(base_models.keys()),
                yticklabels=list(base_models.keys()))
    plt.title('基学习器相关性')

    # 集成方法复杂度比较
    plt.subplot(3, 4, 5)

    methods_complex = ['Voting', 'Bagging', 'Boosting', 'Stacking', 'Blending']
    training_complexity = [2, 3, 4, 5, 4]
    prediction_complexity = [2, 2, 2, 3, 3]

    x = np.arange(len(methods_complex))
    width = 0.35

    plt.bar(x - width/2, training_complexity, width, label='训练复杂度', alpha=0.7)
    plt.bar(x + width/2, prediction_complexity, width, label='预测复杂度', alpha=0.7)

    plt.xlabel('集成方法')
    plt.ylabel('复杂度等级')
    plt.title('集成方法复杂度比较')
    plt.xticks(x, methods_complex, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 误差分析
    plt.subplot(3, 4, 6)

    # 计算不同方法的预测误差
    methods_names = ['Stacking', 'Hard Voting', 'Soft Voting', 'Blending']
    predictions = [stacking_pred, hard_voting_pred, soft_voting_pred, blending_pred]

    error_rates = []
    for pred in predictions:
        error_rate = 1 - accuracy_score(y_test, pred)
        error_rates.append(error_rate)

    plt.bar(methods_names, error_rates, alpha=0.7, color='red')
    plt.title('集成方法错误率')
    plt.ylabel('错误率')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 学习曲线模拟
    plt.subplot(3, 4, 7)

    # 模拟不同训练集大小下的性能
    train_sizes = np.linspace(0.1, 1.0, 10)
    stacking_scores = []
    single_model_scores = []

    for size in train_sizes:
        n_samples = int(size * len(X_train))
        X_subset = X_train[:n_samples]
        y_subset = y_train[:n_samples]

        # Stacking性能
        temp_stacking = StackingEnsemble(base_models, meta_model, cv_folds=3)
        temp_stacking.fit(X_subset, y_subset)
        stacking_score = accuracy_score(y_test, temp_stacking.predict(X_test))
        stacking_scores.append(stacking_score)

        # 单个最佳模型性能
        best_model = RandomForestClassifier(n_estimators=100, random_state=42)
        best_model.fit(X_subset, y_subset)
        single_score = accuracy_score(y_test, best_model.predict(X_test))
        single_model_scores.append(single_score)

    plt.plot(train_sizes, stacking_scores, 'o-', label='Stacking', linewidth=2)
    plt.plot(train_sizes, single_model_scores, 's-', label='单个模型', linewidth=2)
    plt.xlabel('训练集比例')
    plt.ylabel('测试准确率')
    plt.title('学习曲线比较')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 集成学习优势分析
    plt.subplot(3, 4, 8)

    advantages = ['减少过拟合', '提高泛化', '增强鲁棒性', '处理复杂模式']
    importance = [4, 5, 4, 3]

    plt.barh(advantages, importance, alpha=0.7)
    plt.xlabel('重要性评分')
    plt.title('集成学习优势')
    plt.grid(True, alpha=0.3)

    # Stacking架构图
    plt.subplot(3, 4, 9)

    plt.text(0.5, 0.9, 'Stacking架构', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    # 第一层
    base_names = ['RF', 'GB', 'SVM', 'KNN', 'NB']
    for i, name in enumerate(base_names):
        x_pos = 0.1 + i * 0.15
        plt.text(x_pos, 0.7, name, ha='center', va='center',
                transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

    # 第二层
    plt.text(0.5, 0.4, '元学习器\n(Logistic Regression)', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))

    # 输出
    plt.text(0.5, 0.1, '最终预测', ha='center', va='center',
             transform=plt.gca().transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

    # 绘制连接线
    for i in range(5):
        x_pos = 0.1 + i * 0.15
        plt.plot([x_pos, 0.5], [0.65, 0.45], 'k-', alpha=0.5, transform=plt.gca().transAxes)

    plt.plot([0.5, 0.5], [0.35, 0.15], 'k-', alpha=0.5, transform=plt.gca().transAxes)

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 投票机制比较
    plt.subplot(3, 4, 10)

    voting_types = ['硬投票', '软投票', '加权投票']
    descriptions = ['多数决定', '概率平均', '性能加权']

    plt.text(0.5, 0.9, '投票机制比较', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    for i, (vtype, desc) in enumerate(zip(voting_types, descriptions)):
        y_pos = 0.7 - i * 0.2
        plt.text(0.2, y_pos, vtype, ha='center', va='center',
                transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
        plt.text(0.7, y_pos, desc, ha='center', va='center',
                transform=plt.gca().transAxes)

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 集成学习决策边界可视化（2D降维）
    plt.subplot(3, 4, 11)

    from sklearn.decomposition import PCA

    # 降维到2D进行可视化
    pca = PCA(n_components=2, random_state=42)
    X_test_2d = pca.fit_transform(X_test)

    # 创建网格
    h = 0.02
    x_min, x_max = X_test_2d[:, 0].min() - 1, X_test_2d[:, 0].max() + 1
    y_min, y_max = X_test_2d[:, 1].min() - 1, X_test_2d[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                         np.arange(y_min, y_max, h))

    # 绘制数据点
    scatter = plt.scatter(X_test_2d[:, 0], X_test_2d[:, 1], c=y_test,
                         cmap='RdYlBu', alpha=0.8)
    plt.colorbar(scatter)
    plt.title('测试数据分布 (PCA降维)')
    plt.xlabel('主成分1')
    plt.ylabel('主成分2')

    # 模型选择建议
    plt.subplot(3, 4, 12)

    plt.text(0.5, 0.9, '集成方法选择建议', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    suggestions = [
        '小数据集 → Voting',
        '中等数据集 → Stacking',
        '大数据集 → Bagging/Boosting',
        '高方差模型 → Bagging',
        '高偏差模型 → Boosting',
        '追求最优性能 → Stacking'
    ]

    for i, suggestion in enumerate(suggestions):
        y_pos = 0.75 - i * 0.1
        plt.text(0.1, y_pos, f'• {suggestion}', ha='left', va='center',
                transform=plt.gca().transAxes, fontsize=10)

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    plt.tight_layout()
    plt.show()

    print(f"\n集成学习总结:")
    print(f"  Stacking: 层次化集成，性能最优但复杂度高")
    print(f"  Voting: 简单有效，适合快速原型")
    print(f"  Blending: Stacking的简化版本，计算效率高")
    print(f"  关键要素: 基学习器多样性、元学习器选择、交叉验证")
    print(f"  应用场景: 竞赛、高精度要求的生产系统")

# 运行高级集成学习演示
demonstrate_advanced_ensemble()
```

---

# 第十一章：算法选择的决策框架

> **核心理念**: 在众多机器学习算法中选择最适合的方法是成功的关键。本章提供系统性的决策框架，帮助从业者在不同场景下做出最优选择。

## 11.1 算法选择的系统性方法

### 11.1.1 问题类型分析框架

```python
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification, make_regression
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
import time

class AlgorithmSelectionFramework:
    """算法选择决策框架"""

    def __init__(self):
        """
        算法选择的核心考虑因素：
        1. 问题类型：分类、回归、聚类、降维
        2. 数据特征：样本量、特征数、数据质量
        3. 性能要求：准确性、速度、可解释性
        4. 资源约束：计算资源、时间限制
        5. 业务需求：实时性、可扩展性、维护成本
        """
        self.algorithm_database = self._build_algorithm_database()
        self.selection_criteria = self._define_selection_criteria()

    def _build_algorithm_database(self):
        """构建算法知识库"""
        return {
            'classification': {
                'logistic_regression': {
                    'complexity': 'low',
                    'interpretability': 'high',
                    'training_speed': 'fast',
                    'prediction_speed': 'fast',
                    'memory_usage': 'low',
                    'handles_missing': 'no',
                    'handles_categorical': 'with_encoding',
                    'nonlinear': 'no',
                    'probabilistic': 'yes',
                    'best_for': ['linear_separable', 'small_medium_data', 'interpretability_required'],
                    'avoid_when': ['highly_nonlinear', 'many_features', 'multicollinearity']
                },
                'random_forest': {
                    'complexity': 'medium',
                    'interpretability': 'medium',
                    'training_speed': 'medium',
                    'prediction_speed': 'fast',
                    'memory_usage': 'medium',
                    'handles_missing': 'yes',
                    'handles_categorical': 'yes',
                    'nonlinear': 'yes',
                    'probabilistic': 'yes',
                    'best_for': ['mixed_data_types', 'feature_importance', 'robust_performance'],
                    'avoid_when': ['very_large_data', 'real_time_strict', 'simple_linear']
                },
                'svm': {
                    'complexity': 'high',
                    'interpretability': 'low',
                    'training_speed': 'slow',
                    'prediction_speed': 'fast',
                    'memory_usage': 'high',
                    'handles_missing': 'no',
                    'handles_categorical': 'with_encoding',
                    'nonlinear': 'with_kernel',
                    'probabilistic': 'with_calibration',
                    'best_for': ['high_dimensional', 'small_medium_data', 'complex_boundaries'],
                    'avoid_when': ['very_large_data', 'many_noise', 'interpretability_required']
                },
                'naive_bayes': {
                    'complexity': 'low',
                    'interpretability': 'high',
                    'training_speed': 'very_fast',
                    'prediction_speed': 'very_fast',
                    'memory_usage': 'very_low',
                    'handles_missing': 'depends',
                    'handles_categorical': 'yes',
                    'nonlinear': 'no',
                    'probabilistic': 'yes',
                    'best_for': ['text_classification', 'small_data', 'baseline_model'],
                    'avoid_when': ['feature_correlation', 'continuous_features', 'complex_patterns']
                },
                'knn': {
                    'complexity': 'low',
                    'interpretability': 'high',
                    'training_speed': 'very_fast',
                    'prediction_speed': 'slow',
                    'memory_usage': 'high',
                    'handles_missing': 'no',
                    'handles_categorical': 'with_encoding',
                    'nonlinear': 'yes',
                    'probabilistic': 'yes',
                    'best_for': ['local_patterns', 'irregular_boundaries', 'recommendation'],
                    'avoid_when': ['high_dimensional', 'large_data', 'real_time']
                },
                'decision_tree': {
                    'complexity': 'medium',
                    'interpretability': 'very_high',
                    'training_speed': 'fast',
                    'prediction_speed': 'very_fast',
                    'memory_usage': 'low',
                    'handles_missing': 'yes',
                    'handles_categorical': 'yes',
                    'nonlinear': 'yes',
                    'probabilistic': 'yes',
                    'best_for': ['rule_extraction', 'mixed_data', 'interpretability'],
                    'avoid_when': ['overfitting_prone', 'continuous_smooth', 'ensemble_available']
                }
            },
            'regression': {
                'linear_regression': {
                    'complexity': 'low',
                    'interpretability': 'very_high',
                    'training_speed': 'very_fast',
                    'prediction_speed': 'very_fast',
                    'memory_usage': 'very_low',
                    'handles_missing': 'no',
                    'handles_categorical': 'with_encoding',
                    'nonlinear': 'no',
                    'best_for': ['linear_relationship', 'baseline', 'interpretability'],
                    'avoid_when': ['nonlinear', 'multicollinearity', 'many_features']
                },
                'random_forest_reg': {
                    'complexity': 'medium',
                    'interpretability': 'medium',
                    'training_speed': 'medium',
                    'prediction_speed': 'fast',
                    'memory_usage': 'medium',
                    'handles_missing': 'yes',
                    'handles_categorical': 'yes',
                    'nonlinear': 'yes',
                    'best_for': ['nonlinear', 'feature_importance', 'robust'],
                    'avoid_when': ['simple_linear', 'very_large_data', 'extrapolation']
                },
                'svr': {
                    'complexity': 'high',
                    'interpretability': 'low',
                    'training_speed': 'slow',
                    'prediction_speed': 'fast',
                    'memory_usage': 'high',
                    'handles_missing': 'no',
                    'handles_categorical': 'with_encoding',
                    'nonlinear': 'with_kernel',
                    'best_for': ['high_dimensional', 'robust_outliers', 'complex_patterns'],
                    'avoid_when': ['large_data', 'interpretability_required', 'simple_linear']
                }
            }
        }

    def _define_selection_criteria(self):
        """定义选择标准"""
        return {
            'data_size': {
                'small': {'threshold': 1000, 'weight': 0.3},
                'medium': {'threshold': 100000, 'weight': 0.2},
                'large': {'threshold': float('inf'), 'weight': 0.1}
            },
            'feature_count': {
                'low': {'threshold': 10, 'weight': 0.2},
                'medium': {'threshold': 100, 'weight': 0.15},
                'high': {'threshold': float('inf'), 'weight': 0.1}
            },
            'interpretability': {
                'required': {'weight': 0.4},
                'preferred': {'weight': 0.2},
                'not_important': {'weight': 0.0}
            },
            'speed': {
                'critical': {'weight': 0.3},
                'important': {'weight': 0.2},
                'not_important': {'weight': 0.0}
            }
        }

    def analyze_dataset_characteristics(self, X, y=None):
        """分析数据集特征"""
        n_samples, n_features = X.shape

        characteristics = {
            'n_samples': n_samples,
            'n_features': n_features,
            'sample_size_category': self._categorize_size(n_samples),
            'feature_size_category': self._categorize_features(n_features),
            'missing_values': np.isnan(X).sum().sum() if hasattr(X, 'sum') else 0,
            'data_types': self._analyze_data_types(X),
        }

        if y is not None:
            characteristics.update({
                'problem_type': self._infer_problem_type(y),
                'class_balance': self._analyze_class_balance(y),
                'target_distribution': self._analyze_target_distribution(y)
            })

        return characteristics

    def _categorize_size(self, n_samples):
        """分类样本量大小"""
        if n_samples < 1000:
            return 'small'
        elif n_samples < 100000:
            return 'medium'
        else:
            return 'large'

    def _categorize_features(self, n_features):
        """分类特征数量"""
        if n_features < 10:
            return 'low'
        elif n_features < 100:
            return 'medium'
        else:
            return 'high'

    def _analyze_data_types(self, X):
        """分析数据类型"""
        if hasattr(X, 'dtypes'):
            return {
                'numeric': sum(X.dtypes.apply(lambda x: np.issubdtype(x, np.number))),
                'categorical': sum(X.dtypes.apply(lambda x: x == 'object')),
                'mixed': True if sum(X.dtypes.apply(lambda x: x == 'object')) > 0 and
                              sum(X.dtypes.apply(lambda x: np.issubdtype(x, np.number))) > 0 else False
            }
        else:
            return {'numeric': X.shape[1], 'categorical': 0, 'mixed': False}

    def _infer_problem_type(self, y):
        """推断问题类型"""
        unique_values = len(np.unique(y))
        if unique_values == 2:
            return 'binary_classification'
        elif unique_values < 20 and np.issubdtype(y.dtype, np.integer):
            return 'multiclass_classification'
        else:
            return 'regression'

    def _analyze_class_balance(self, y):
        """分析类别平衡性"""
        if self._infer_problem_type(y) in ['binary_classification', 'multiclass_classification']:
            unique, counts = np.unique(y, return_counts=True)
            balance_ratio = min(counts) / max(counts)
            if balance_ratio > 0.8:
                return 'balanced'
            elif balance_ratio > 0.3:
                return 'slightly_imbalanced'
            else:
                return 'highly_imbalanced'
        return 'not_applicable'

    def _analyze_target_distribution(self, y):
        """分析目标变量分布"""
        if self._infer_problem_type(y) == 'regression':
            return {
                'mean': np.mean(y),
                'std': np.std(y),
                'skewness': self._calculate_skewness(y),
                'outliers': self._detect_outliers(y)
            }
        return None

    def _calculate_skewness(self, data):
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 3)

    def _detect_outliers(self, data):
        """检测异常值"""
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = np.sum((data < lower_bound) | (data > upper_bound))
        return outliers / len(data)

    def recommend_algorithms(self, X, y, requirements=None):
        """推荐算法"""
        # 分析数据特征
        characteristics = self.analyze_dataset_characteristics(X, y)

        # 获取问题类型对应的算法
        problem_type = characteristics['problem_type']
        if 'classification' in problem_type:
            candidate_algorithms = self.algorithm_database['classification']
        else:
            candidate_algorithms = self.algorithm_database['regression']

        # 计算每个算法的适合度分数
        algorithm_scores = {}

        for alg_name, alg_info in candidate_algorithms.items():
            score = self._calculate_algorithm_score(alg_info, characteristics, requirements)
            algorithm_scores[alg_name] = score

        # 排序并返回推荐
        sorted_algorithms = sorted(algorithm_scores.items(), key=lambda x: x[1], reverse=True)

        return {
            'data_characteristics': characteristics,
            'recommendations': sorted_algorithms,
            'top_3': sorted_algorithms[:3]
        }

    def _calculate_algorithm_score(self, alg_info, characteristics, requirements):
        """计算算法适合度分数"""
        score = 0.0

        # 基于数据大小的评分
        if characteristics['sample_size_category'] == 'small':
            if alg_info['training_speed'] in ['fast', 'very_fast']:
                score += 0.2
        elif characteristics['sample_size_category'] == 'large':
            if alg_info['training_speed'] in ['fast', 'very_fast']:
                score += 0.3
            if alg_info['memory_usage'] in ['low', 'very_low']:
                score += 0.2

        # 基于特征数量的评分
        if characteristics['feature_size_category'] == 'high':
            if alg_info.get('handles_high_dim', True):
                score += 0.2

        # 基于缺失值处理能力
        if characteristics['missing_values'] > 0:
            if alg_info['handles_missing'] == 'yes':
                score += 0.15
            elif alg_info['handles_missing'] == 'no':
                score -= 0.1

        # 基于数据类型
        if characteristics['data_types']['mixed']:
            if alg_info['handles_categorical'] == 'yes':
                score += 0.15

        # 基于需求的评分
        if requirements:
            if requirements.get('interpretability') == 'required':
                if alg_info['interpretability'] in ['high', 'very_high']:
                    score += 0.3
                elif alg_info['interpretability'] == 'low':
                    score -= 0.2

            if requirements.get('speed') == 'critical':
                if alg_info['prediction_speed'] in ['fast', 'very_fast']:
                    score += 0.25
                elif alg_info['prediction_speed'] == 'slow':
                    score -= 0.15

        return max(0, score)  # 确保分数非负

# 算法选择演示
def demonstrate_algorithm_selection():
    """算法选择框架演示"""

    print("=== 算法选择决策框架演示 ===")

    # 创建选择框架
    selector = AlgorithmSelectionFramework()

    # 场景1：小数据集分类问题
    print("\n场景1：小数据集二分类问题")
    X_small, y_small = make_classification(
        n_samples=500, n_features=10, n_informative=5,
        n_redundant=2, n_clusters_per_class=1, random_state=42
    )

    requirements_small = {
        'interpretability': 'required',
        'speed': 'not_important'
    }

    recommendations_small = selector.recommend_algorithms(X_small, y_small, requirements_small)

    print(f"数据特征:")
    chars = recommendations_small['data_characteristics']
    print(f"  样本数: {chars['n_samples']}")
    print(f"  特征数: {chars['n_features']}")
    print(f"  问题类型: {chars['problem_type']}")
    print(f"  类别平衡: {chars['class_balance']}")

    print(f"\n推荐算法 (前3名):")
    for i, (alg_name, score) in enumerate(recommendations_small['top_3'], 1):
        print(f"  {i}. {alg_name}: 适合度分数 {score:.3f}")

    # 场景2：大数据集回归问题
    print(f"\n场景2：大数据集回归问题")
    X_large, y_large = make_regression(
        n_samples=50000, n_features=20, noise=0.1, random_state=42
    )

    requirements_large = {
        'interpretability': 'not_important',
        'speed': 'critical'
    }

    recommendations_large = selector.recommend_algorithms(X_large, y_large, requirements_large)

    print(f"数据特征:")
    chars = recommendations_large['data_characteristics']
    print(f"  样本数: {chars['n_samples']}")
    print(f"  特征数: {chars['n_features']}")
    print(f"  问题类型: {chars['problem_type']}")

    print(f"\n推荐算法 (前3名):")
    for i, (alg_name, score) in enumerate(recommendations_large['top_3'], 1):
        print(f"  {i}. {alg_name}: 适合度分数 {score:.3f}")

    # 场景3：实际性能比较
    print(f"\n场景3：推荐算法的实际性能验证")

    # 使用小数据集进行实际测试
    algorithms_to_test = {
        'Logistic Regression': LogisticRegression(random_state=42),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'Decision Tree': DecisionTreeClassifier(random_state=42),
        'Naive Bayes': GaussianNB(),
        'KNN': KNeighborsClassifier(n_neighbors=5)
    }

    print(f"在小数据集上的5折交叉验证结果:")
    results = {}

    for name, model in algorithms_to_test.items():
        start_time = time.time()
        scores = cross_val_score(model, X_small, y_small, cv=5, scoring='accuracy')
        training_time = time.time() - start_time

        results[name] = {
            'accuracy': scores.mean(),
            'std': scores.std(),
            'time': training_time
        }

        print(f"  {name}:")
        print(f"    准确率: {scores.mean():.4f} (±{scores.std():.4f})")
        print(f"    训练时间: {training_time:.4f}秒")

    # 可视化结果
    plt.figure(figsize=(15, 10))

    # 准确率比较
    plt.subplot(2, 3, 1)
    names = list(results.keys())
    accuracies = [results[name]['accuracy'] for name in names]
    errors = [results[name]['std'] for name in names]

    plt.bar(names, accuracies, yerr=errors, capsize=5, alpha=0.7)
    plt.title('算法准确率比较')
    plt.ylabel('准确率')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 训练时间比较
    plt.subplot(2, 3, 2)
    times = [results[name]['time'] for name in names]
    plt.bar(names, times, alpha=0.7, color='orange')
    plt.title('训练时间比较')
    plt.ylabel('时间 (秒)')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 准确率vs时间散点图
    plt.subplot(2, 3, 3)
    plt.scatter(times, accuracies, s=100, alpha=0.7)
    for i, name in enumerate(names):
        plt.annotate(name, (times[i], accuracies[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    plt.xlabel('训练时间 (秒)')
    plt.ylabel('准确率')
    plt.title('准确率 vs 训练时间')
    plt.grid(True, alpha=0.3)

    # 算法特征雷达图
    plt.subplot(2, 3, 4)

    # 选择前3个推荐算法的特征
    top_algorithms = [alg for alg, _ in recommendations_small['top_3']]
    features = ['interpretability', 'training_speed', 'prediction_speed', 'memory_usage']

    # 创建特征评分映射
    feature_scores = {
        'very_high': 5, 'high': 4, 'medium': 3, 'low': 2, 'very_low': 1,
        'very_fast': 5, 'fast': 4, 'slow': 2, 'very_slow': 1
    }

    angles = np.linspace(0, 2 * np.pi, len(features), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    ax = plt.subplot(2, 3, 4, projection='polar')

    for alg_name in top_algorithms[:3]:  # 只显示前3个
        if alg_name in selector.algorithm_database['classification']:
            alg_info = selector.algorithm_database['classification'][alg_name]
            scores = [feature_scores.get(alg_info[feature], 3) for feature in features]
            scores += scores[:1]  # 闭合图形

            ax.plot(angles, scores, 'o-', linewidth=2, label=alg_name)
            ax.fill(angles, scores, alpha=0.25)

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(features)
    ax.set_ylim(0, 5)
    ax.set_title('算法特征比较')
    ax.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))

    # 决策树可视化
    plt.subplot(2, 3, 5)

    # 创建决策流程图
    decision_flow = {
        'interpretability_required': {
            'yes': ['logistic_regression', 'decision_tree', 'naive_bayes'],
            'no': ['random_forest', 'svm']
        },
        'data_size': {
            'small': ['naive_bayes', 'knn', 'decision_tree'],
            'large': ['logistic_regression', 'random_forest']
        },
        'speed_critical': {
            'yes': ['naive_bayes', 'logistic_regression'],
            'no': ['random_forest', 'svm']
        }
    }

    # 简化的决策树可视化
    plt.text(0.5, 0.9, '算法选择决策树', ha='center', va='center',
             fontsize=14, weight='bold', transform=plt.gca().transAxes)

    plt.text(0.5, 0.7, '需要可解释性？', ha='center', va='center',
             transform=plt.gca().transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

    plt.text(0.2, 0.5, '是\n→ 线性模型\n→ 决策树', ha='center', va='center',
             transform=plt.gca().transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

    plt.text(0.8, 0.5, '否\n→ 集成方法\n→ 深度学习', ha='center', va='center',
             transform=plt.gca().transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))

    plt.text(0.5, 0.3, '数据量大小？', ha='center', va='center',
             transform=plt.gca().transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))

    plt.text(0.2, 0.1, '小数据\n→ 简单模型', ha='center', va='center',
             transform=plt.gca().transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.text(0.8, 0.1, '大数据\n→ 可扩展模型', ha='center', va='center',
             transform=plt.gca().transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')

    # 算法复杂度对比
    plt.subplot(2, 3, 6)

    complexity_data = {
        'Logistic Regression': {'time': 'O(n*d)', 'space': 'O(d)', 'score': 1},
        'Decision Tree': {'time': 'O(n*d*log(n))', 'space': 'O(n)', 'score': 2},
        'Random Forest': {'time': 'O(n*d*log(n)*k)', 'space': 'O(n*k)', 'score': 3},
        'SVM': {'time': 'O(n²*d)', 'space': 'O(n²)', 'score': 4},
        'KNN': {'time': 'O(1)', 'space': 'O(n*d)', 'score': 2}
    }

    alg_names = list(complexity_data.keys())
    complexity_scores = [complexity_data[name]['score'] for name in alg_names]

    colors = ['green', 'yellow', 'orange', 'red', 'yellow']
    plt.bar(alg_names, complexity_scores, color=colors, alpha=0.7)
    plt.title('算法复杂度比较')
    plt.ylabel('复杂度等级')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 添加复杂度标签
    for i, (name, score) in enumerate(zip(alg_names, complexity_scores)):
        plt.text(i, score + 0.1, complexity_data[name]['time'],
                ha='center', va='bottom', fontsize=8, rotation=45)

    plt.tight_layout()
    plt.show()

    print(f"\n算法选择总结:")
    print(f"  1. 小数据+可解释性需求 → 逻辑回归、决策树")
    print(f"  2. 大数据+性能优先 → 随机森林、梯度提升")
    print(f"  3. 高维数据+复杂边界 → SVM、神经网络")
    print(f"  4. 实时预测+简单模型 → 朴素贝叶斯、线性模型")
    print(f"  5. 特征重要性+鲁棒性 → 随机森林、XGBoost")

# 运行算法选择演示
demonstrate_algorithm_selection()
