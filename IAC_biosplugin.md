kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

https://************:32443/applications    ---> argocd website
admin/CDOpIAIFwvm0ub2p

http://************:30030/gitea/IaC-biosplugin    --> gitea website
gitea/admin@cloud 


submit the helm chart of biosplugin


kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d
kubectl get secret argocd-redis -n argocd -o yaml
echo "Z3FsOFVSV0pyM08wZFY5UA==" | base64 -d

kubectl get po -n argocd
kubectl exec -it argocd-server-6496dc8859-sj7fp -n argocd -- bash
kubectl cp cluster-b-kubeconfig.yaml argocd/argocd-server-6496dc8859-sj7fp:/tmp


kubectl exec -it argocd-redis-794f68fb68-nd8cm -n argocd -- redis-cli -h localhost -p 6379 -a CDOpIAIFwvm0ub2p

kubectl rollout restart deployment argocd-server -n argocd


kubectl config get-contexts
kubectl config get-contexts --kubeconfig cluster-b-kubeconfig.yaml
vi cluster-b-kubeconfig.yaml   --> change localhost to real ip
scp cluster-b-kubeconfig.yaml root@************:/root





