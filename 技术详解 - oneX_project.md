# OneX Cloud Native Platform - Technical Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Component Architecture](#component-architecture)
4. [Data Flow Diagrams](#data-flow-diagrams)
5. [Database Schema](#database-schema)
6. [API Architecture](#api-architecture)
7. [Technology Stack](#technology-stack)
8. [Deployment Architecture](#deployment-architecture)
9. [Security Architecture](#security-architecture)
10. [Monitoring and Observability](#monitoring-and-observability)

## Project Overview

OneX is a comprehensive cloud-native mining platform that serves as an enterprise-level Go project scaffold. It demonstrates modern microservices architecture, Kubernetes-native design patterns, and blockchain integration.

### Key Features
- **Microservices Architecture**: Distributed system with multiple specialized services
- **Kubernetes-Native**: Built with Kubernetes controller patterns and CRDs
- **Blockchain Integration**: Private blockchain network management
- **Enterprise-Grade**: Production-ready with monitoring, logging, and security
- **Cloud-Native**: Container-first design with Helm charts and operators

## System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        FE[Frontend Console]
        CLI[onexctl CLI]
    end
    
    subgraph "API Gateway Layer"
        GW[onex-gateway]
        UC[onex-usercenter]
    end
    
    subgraph "Core Services Layer"
        API[onex-apiserver]
        CM[onex-controller-manager]
        MSC[onex-minerset-controller]
        MC[onex-miner-controller]
    end
    
    subgraph "Processing Layer"
        NW[onex-nightwatch]
        PUMP[onex-pump]
        TBC[onex-toyblc]
    end
    
    subgraph "Storage Layer"
        ETCD[(Etcd)]
        MYSQL[(MySQL)]
        REDIS[(Redis)]
        KAFKA[(Kafka)]
        MONGO[(MongoDB)]
    end
    
    subgraph "Infrastructure"
        K8S[Kubernetes Cluster]
        BC[Blockchain Cluster]
    end
    
    FE --> GW
    CLI --> GW
    GW --> UC
    GW --> API
    UC --> MYSQL
    UC --> REDIS
    UC --> KAFKA
    
    API --> ETCD
    CM --> API
    MSC --> API
    MC --> API
    MC --> K8S
    
    NW --> MYSQL
    NW --> REDIS
    PUMP --> KAFKA
    PUMP --> MONGO
    
    TBC --> BC
    
    style FE fill:#e1f5fe
    style GW fill:#f3e5f5
    style API fill:#e8f5e8
    style ETCD fill:#fff3e0
    style K8S fill:#fce4ec
```

## Component Architecture

### Core Components Overview

```mermaid
graph LR
    subgraph "Control Plane"
        A[onex-apiserver<br/>API Server]
        B[onex-controller-manager<br/>Controller Manager]
        C[onex-minerset-controller<br/>MinerSet Controller]
        D[onex-miner-controller<br/>Miner Controller]
    end
    
    subgraph "Data Plane"
        E[onex-gateway<br/>API Gateway]
        F[onex-usercenter<br/>User Management]
        G[onex-nightwatch<br/>Async Tasks]
        H[onex-pump<br/>ETL Service]
    end
    
    subgraph "Blockchain Layer"
        I[onex-toyblc<br/>Blockchain Node]
    end
    
    A --> C
    A --> D
    B --> A
    C --> D
    E --> F
    E --> A
    G --> A
    H --> F
    D --> I
    
    style A fill:#ffcdd2
    style E fill:#c8e6c9
    style I fill:#fff9c4
```

### Component Responsibilities

| Component | Primary Function | Technology Stack |
|-----------|------------------|------------------|
| **onex-apiserver** | Kubernetes-style API server for resource management | Go, Etcd, gRPC |
| **onex-gateway** | Unified API gateway and backend portal | Go, Kratos, MySQL, Redis |
| **onex-usercenter** | User authentication, authorization, and management | Go, Kratos, MySQL, Kafka |
| **onex-controller-manager** | Aggregated controller for resource reconciliation | Go, client-go, MySQL |
| **onex-minerset-controller** | MinerSet resource lifecycle management | Go, Kubernetes controllers |
| **onex-miner-controller** | Individual miner (Pod) lifecycle management | Go, Kubernetes API |
| **onex-nightwatch** | Distributed asynchronous task processing | Go, Redis, Cron |
| **onex-pump** | ETL service for authorization log processing | Go, Kafka, MongoDB |
| **onex-toyblc** | Blockchain node initialization and management | Go, Blockchain protocols |

## Data Flow Diagrams

### User Authentication and Authorization Flow

```mermaid
sequenceDiagram
    participant U as User/Frontend
    participant GW as onex-gateway
    participant UC as onex-usercenter
    participant K as Kafka
    participant R as Redis
    participant M as MySQL
    
    U->>UC: 1. Login Request (username/password)
    UC->>M: 2. Validate Credentials
    M-->>UC: 3. User Data
    UC->>R: 4. Store Session
    UC-->>U: 5. Return JWT Token
    
    U->>GW: 6. API Request (with JWT)
    GW->>UC: 7. Validate Token & Authorize
    UC->>K: 8. Log Authorization Event
    UC-->>GW: 9. Authorization Result
    GW-->>U: 10. API Response
```

### MinerSet Creation and Management Flow

```mermaid
sequenceDiagram
    participant U as User
    participant GW as onex-gateway
    participant API as onex-apiserver
    participant MSC as onex-minerset-controller
    participant MC as onex-miner-controller
    participant K8S as Kubernetes API
    participant E as Etcd
    
    U->>GW: 1. Create MinerSet Request
    GW->>API: 2. Create MinerSet Resource
    API->>E: 3. Store MinerSet in Etcd
    
    MSC->>API: 4. Watch MinerSet Events
    API-->>MSC: 5. MinerSet Created Event
    MSC->>API: 6. Create Miner Resources
    
    MC->>API: 7. Watch Miner Events
    API-->>MC: 8. Miner Created Event
    MC->>K8S: 9. Create Pod Resources
    K8S-->>MC: 10. Pod Status Updates
    MC->>API: 11. Update Miner Status
```

### Data Synchronization Flow

```mermaid
sequenceDiagram
    participant API as onex-apiserver
    participant CM as onex-controller-manager
    participant M as MySQL
    participant P as onex-pump
    participant K as Kafka
    participant MG as MongoDB
    
    API->>CM: 1. Resource Change Events
    CM->>M: 2. Sync Resources to MySQL
    
    Note over K,P: Authorization Log Processing
    K->>P: 3. Authorization Log Events
    P->>P: 4. Process & Transform Data
    P->>MG: 5. Store Processed Logs
```

## Database Schema

### Core Resource Tables

```mermaid
erDiagram
    API_CHAIN {
        bigint id PK
        varchar namespace
        varchar name
        varchar display_name
        varchar miner_type
        varchar image
        int min_mine_interval_seconds
        datetime created_at
        datetime updated_at
    }
    
    API_MINERSET {
        bigint id PK
        varchar namespace
        varchar name
        int replicas
        varchar display_name
        varchar delete_policy
        int min_ready_seconds
        int fully_labeled_replicas
        int ready_replicas
        datetime created_at
        datetime updated_at
    }
    
    API_MINER {
        bigint id PK
        varchar namespace
        varchar name
        varchar display_name
        varchar phase
        varchar miner_type
        varchar chain_name
        int cpu
        int memory
        datetime created_at
        datetime updated_at
    }
    
    API_USER {
        bigint id PK
        varchar username
        varchar password
        varchar email
        varchar phone
        int status
        datetime created_at
        datetime updated_at
    }
    
    API_SECRET {
        bigint id PK
        varchar username
        varchar secret_id
        varchar secret_key
        int status
        varchar description
        bigint expires
        datetime created_at
        datetime updated_at
    }
    
    API_CHAIN ||--o{ API_MINER : "chain_name"
    API_MINERSET ||--o{ API_MINER : "manages"
    API_USER ||--o{ API_SECRET : "owns"
```

### Storage Architecture

```mermaid
graph TB
    subgraph "Primary Storage"
        ETCD[Etcd<br/>- API Resources<br/>- Configuration<br/>- Events]
        MYSQL[MySQL<br/>- User Data<br/>- Business Logic<br/>- Cached Resources]
    end
    
    subgraph "Cache & Message"
        REDIS[Redis<br/>- Session Cache<br/>- Distributed Locks<br/>- Rate Limiting]
        KAFKA[Kafka<br/>- Authorization Logs<br/>- Event Streaming<br/>- Audit Trail]
    end
    
    subgraph "Analytics"
        MONGO[MongoDB<br/>- Processed Logs<br/>- Analytics Data<br/>- Reporting]
    end
    
    ETCD --> MYSQL
    MYSQL --> REDIS
    KAFKA --> MONGO
    
    style ETCD fill:#e3f2fd
    style MYSQL fill:#e8f5e8
    style REDIS fill:#fff3e0
    style KAFKA fill:#f3e5f5
    style MONGO fill:#e0f2f1
```

## API Architecture

### RESTful API Design

```mermaid
graph TB
    subgraph "API Gateway Layer"
        GW[onex-gateway<br/>Port: 38080/38443]
        UC[onex-usercenter<br/>Port: 38443/39090]
    end

    subgraph "Core API Layer"
        API[onex-apiserver<br/>Port: 38443]
    end

    subgraph "API Endpoints"
        subgraph "Gateway APIs"
            GW_REST["/v1/minersets<br/>/v1/miners<br/>/v1/chains"]
            GW_GRPC[gRPC Services<br/>MinerSet Service<br/>Miner Service]
        end

        subgraph "User APIs"
            UC_REST["/v1/users<br/>/v1/secrets<br/>/v1/auth"]
            UC_GRPC[gRPC Services<br/>User Service<br/>Auth Service]
        end

        subgraph "Core APIs"
            API_REST["/api/v1/minersets<br/>/api/v1/miners<br/>/apis/apps.onex.io/v1beta1"]
        end
    end

    GW --> GW_REST
    GW --> GW_GRPC
    UC --> UC_REST
    UC --> UC_GRPC
    API --> API_REST

    style GW fill:#e1f5fe
    style UC fill:#f3e5f5
    style API fill:#e8f5e8
```

### Protocol Support Matrix

| Service | HTTP/HTTPS | gRPC | WebSocket | Protocol Buffers |
|---------|------------|------|-----------|------------------|
| onex-gateway | ✅ | ✅ | ❌ | ✅ |
| onex-usercenter | ✅ | ✅ | ❌ | ✅ |
| onex-apiserver | ✅ | ❌ | ✅ | ❌ |
| onex-nightwatch | ❌ | ❌ | ❌ | ❌ |
| onex-pump | ❌ | ❌ | ❌ | ❌ |

## Technology Stack

### Programming Languages and Frameworks

```mermaid
mindmap
  root((OneX Tech Stack))
    Programming Languages
      Go
        Primary Language
        Microservices
        Controllers
      Shell
        Automation Scripts
        Deployment Scripts
      Makefile
        Build System
        CI/CD Pipeline

    Web Frameworks
      Kratos
        Microservice Framework
        HTTP/gRPC Server
      Gin
        HTTP Router
        Middleware Support
      gRPC
        Service Communication
        Protocol Buffers

    Databases
      MySQL
        Primary Database
        User Data
        Business Logic
      Redis
        Caching
        Session Storage
        Distributed Locks
      Etcd
        Configuration
        Service Discovery
        API Resources
      Kafka
        Message Queue
        Event Streaming
      MongoDB
        Analytics
        Log Storage

    Cloud Native
      Kubernetes
        Container Orchestration
        Resource Management
      Docker
        Containerization
        Image Building
      Helm
        Package Management
        Deployment
```

### Middleware and Libraries

| Category | Technology | Purpose |
|----------|------------|---------|
| **Web Framework** | Kratos, Gin, gRPC-Gateway | HTTP/gRPC service development |
| **Database ORM** | GORM | Database abstraction and migration |
| **Caching** | go-redis, go-cache | Redis client and in-memory caching |
| **Message Queue** | segmentio/kafka-go | Kafka client for event streaming |
| **Authentication** | golang-jwt | JWT token generation and validation |
| **Authorization** | casbin | RBAC and policy enforcement |
| **Validation** | validator | Request parameter validation |
| **Logging** | klog, zap, logrus | Structured logging |
| **Monitoring** | Prometheus, Jaeger | Metrics and distributed tracing |
| **CLI** | cobra, pflag, viper | Command-line interface development |
| **Testing** | testify, GoConvey | Unit testing and mocking |

### Development Tools

```mermaid
graph LR
    subgraph "Code Generation"
        A[protoc<br/>Protocol Buffers]
        B[wire<br/>Dependency Injection]
        C[client-go<br/>Kubernetes Clients]
        D[swagger<br/>API Documentation]
    end

    subgraph "Quality Assurance"
        E[golangci-lint<br/>Static Analysis]
        F[gofmt<br/>Code Formatting]
        G[gosec<br/>Security Scanning]
        H[govulncheck<br/>Vulnerability Check]
    end

    subgraph "Build & Deploy"
        I[Docker<br/>Containerization]
        J[Helm<br/>Package Management]
        K[Kustomize<br/>Configuration Management]
        L[Kind<br/>Local Kubernetes]
    end

    style A fill:#e3f2fd
    style E fill:#e8f5e8
    style I fill:#fff3e0
```

## Deployment Architecture

### Kubernetes Deployment Model

```mermaid
graph TB
    subgraph "Infrastructure Cluster"
        subgraph "Control Plane"
            API_POD[onex-apiserver<br/>Deployment]
            GW_POD[onex-gateway<br/>Deployment]
            UC_POD[onex-usercenter<br/>Deployment]
            CM_POD[onex-controller-manager<br/>Deployment]
        end

        subgraph "Data Plane"
            MSC_POD[onex-minerset-controller<br/>Deployment]
            MC_POD[onex-miner-controller<br/>Deployment]
            NW_POD[onex-nightwatch<br/>Deployment]
            PUMP_POD[onex-pump<br/>Deployment]
        end

        subgraph "Storage"
            ETCD_STS[etcd<br/>StatefulSet]
            MYSQL_STS[mysql<br/>StatefulSet]
            REDIS_STS[redis<br/>StatefulSet]
            KAFKA_STS[kafka<br/>StatefulSet]
            MONGO_STS[mongodb<br/>StatefulSet]
        end
    end

    subgraph "Blockchain Cluster"
        subgraph "Mining Pods"
            MINER1[Miner Pod 1<br/>onex-toyblc]
            MINER2[Miner Pod 2<br/>onex-toyblc]
            MINER3[Miner Pod N<br/>onex-toyblc]
        end
    end

    MC_POD --> MINER1
    MC_POD --> MINER2
    MC_POD --> MINER3

    style API_POD fill:#ffcdd2
    style GW_POD fill:#c8e6c9
    style MINER1 fill:#fff9c4
    style ETCD_STS fill:#e1f5fe
```

### Service Mesh and Networking

```mermaid
graph TB
    subgraph "Ingress Layer"
        INGRESS[Traefik Ingress<br/>Load Balancer]
    end

    subgraph "Service Layer"
        GW_SVC[onex-gateway<br/>Service]
        UC_SVC[onex-usercenter<br/>Service]
        API_SVC[onex-apiserver<br/>Service]
    end

    subgraph "Pod Network"
        GW_POD[Gateway Pods]
        UC_POD[UserCenter Pods]
        API_POD[APIServer Pods]
    end

    INGRESS --> GW_SVC
    INGRESS --> UC_SVC
    INGRESS --> API_SVC

    GW_SVC --> GW_POD
    UC_SVC --> UC_POD
    API_SVC --> API_POD

    style INGRESS fill:#e8eaf6
    style GW_SVC fill:#e0f2f1
    style GW_POD fill:#f3e5f5
```

### Container Architecture

```mermaid
graph TB
    subgraph "Base Images"
        GOLANG[golang:1.20<br/>Build Stage]
        ALPINE[alpine:latest<br/>Runtime Stage]
    end

    subgraph "Application Images"
        GW_IMG[onex-gateway<br/>Multi-stage Build]
        UC_IMG[onex-usercenter<br/>Multi-stage Build]
        API_IMG[onex-apiserver<br/>Multi-stage Build]
        TBC_IMG[onex-toyblc<br/>Blockchain Node]
    end

    subgraph "Registry"
        REGISTRY[Container Registry<br/>ccr.ccs.tencentyun.com/superproj]
    end

    GOLANG --> GW_IMG
    GOLANG --> UC_IMG
    GOLANG --> API_IMG
    GOLANG --> TBC_IMG

    ALPINE --> GW_IMG
    ALPINE --> UC_IMG
    ALPINE --> API_IMG
    ALPINE --> TBC_IMG

    GW_IMG --> REGISTRY
    UC_IMG --> REGISTRY
    API_IMG --> REGISTRY
    TBC_IMG --> REGISTRY

    style GOLANG fill:#e3f2fd
    style ALPINE fill:#e8f5e8
    style REGISTRY fill:#fff3e0
```

## Security Architecture

### Authentication and Authorization Flow

```mermaid
graph TB
    subgraph "Authentication Layer"
        A[Basic Auth<br/>Username/Password]
        B[JWT Token<br/>Bearer Token]
        C[API Key<br/>Secret Key Pair]
        D[TLS Certificates<br/>mTLS]
    end

    subgraph "Authorization Layer"
        E[RBAC<br/>Role-Based Access Control]
        F[Casbin<br/>Policy Engine]
        G[Resource Permissions<br/>Namespace Isolation]
    end

    subgraph "Security Enforcement"
        H[API Gateway<br/>Rate Limiting]
        I[Network Policies<br/>Pod Security]
        J[Admission Controllers<br/>Resource Validation]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> F
    F --> G

    G --> H
    G --> I
    G --> J

    style A fill:#ffebee
    style E fill:#e8f5e8
    style H fill:#e3f2fd
```

### Security Components

| Component | Security Feature | Implementation |
|-----------|------------------|----------------|
| **API Gateway** | Rate Limiting, Request Validation | Kratos middleware, Custom validators |
| **User Center** | Authentication, Session Management | JWT tokens, Redis sessions |
| **API Server** | Authorization, Admission Control | RBAC, Custom admission controllers |
| **Network** | TLS Encryption, mTLS | Certificate management, Ingress TLS |
| **Storage** | Data Encryption, Access Control | Database encryption, IAM policies |

## Monitoring and Observability

### Observability Stack

```mermaid
graph TB
    subgraph "Metrics Collection"
        PROM[Prometheus<br/>Metrics Server]
        GRAFANA[Grafana<br/>Visualization]
        ALERT[AlertManager<br/>Alerting]
    end

    subgraph "Distributed Tracing"
        JAEGER[Jaeger<br/>Trace Collection]
        OTEL[OpenTelemetry<br/>Instrumentation]
    end

    subgraph "Logging"
        ELK[ELK Stack<br/>Elasticsearch, Logstash, Kibana]
        LOKI[Loki + Grafana<br/>Log Aggregation]
        FILEBEAT[Filebeat<br/>Log Shipping]
    end

    subgraph "Application Services"
        APPS[OneX Services<br/>Gateway, UserCenter, APIServer]
    end

    APPS --> PROM
    APPS --> JAEGER
    APPS --> ELK

    PROM --> GRAFANA
    PROM --> ALERT
    JAEGER --> OTEL
    ELK --> FILEBEAT

    style PROM fill:#e8f5e8
    style JAEGER fill:#e3f2fd
    style ELK fill:#fff3e0
```

### Metrics and KPIs

```mermaid
graph LR
    subgraph "Business Metrics"
        A[Active Users<br/>Registration Rate]
        B[MinerSet Creation<br/>Success Rate]
        C[Mining Efficiency<br/>Block Generation]
    end

    subgraph "Technical Metrics"
        D[API Response Time<br/>Throughput]
        E[Resource Utilization<br/>CPU, Memory]
        F[Error Rates<br/>5xx Responses]
    end

    subgraph "Infrastructure Metrics"
        G[Pod Health<br/>Restart Count]
        H[Storage Usage<br/>Database Performance]
        I[Network Latency<br/>Service Mesh]
    end

    style A fill:#e1f5fe
    style D fill:#e8f5e8
    style G fill:#fff3e0
```

### Performance Monitoring

| Metric Category | Key Indicators | Monitoring Tool |
|-----------------|----------------|-----------------|
| **Application Performance** | Response time, Throughput, Error rate | Prometheus + Grafana |
| **Infrastructure Health** | CPU, Memory, Disk, Network | Node Exporter |
| **Business KPIs** | User activity, Mining efficiency | Custom metrics |
| **Security Events** | Failed logins, Unauthorized access | Security logs |

## Blockchain Architecture

### Private Blockchain Network

```mermaid
graph TB
    subgraph "Blockchain Network"
        subgraph "Genesis Chain"
            GENESIS[Genesis Node<br/>Bootstrap Account]
        end

        subgraph "Mining Network"
            MINER1[Miner Node 1<br/>onex-toyblc]
            MINER2[Miner Node 2<br/>onex-toyblc]
            MINER3[Miner Node 3<br/>onex-toyblc]
            MINERN[Miner Node N<br/>onex-toyblc]
        end

        subgraph "Network Communication"
            P2P[P2P Network<br/>Node Discovery]
            CONSENSUS[Consensus Algorithm<br/>Block Validation]
        end
    end

    GENESIS --> MINER1
    GENESIS --> MINER2
    GENESIS --> MINER3
    GENESIS --> MINERN

    MINER1 <--> P2P
    MINER2 <--> P2P
    MINER3 <--> P2P
    MINERN <--> P2P

    P2P --> CONSENSUS

    style GENESIS fill:#fff9c4
    style MINER1 fill:#e8f5e8
    style P2P fill:#e3f2fd
```

### Blockchain Resource Management

```mermaid
stateDiagram-v2
    [*] --> ChainCreated: Create Chain
    ChainCreated --> MinerSetCreated: Create MinerSet
    MinerSetCreated --> MinersCreated: Generate Miners
    MinersCreated --> PodsRunning: Deploy Pods
    PodsRunning --> BlockchainActive: Start Mining
    BlockchainActive --> BlockchainActive: Mine Blocks
    BlockchainActive --> MinersScaling: Scale Up/Down
    MinersScaling --> MinersCreated: Add Miners
    MinersScaling --> MinersDeleted: Remove Miners
    MinersDeleted --> PodsRunning: Update Pods
    BlockchainActive --> ChainDeleted: Delete Chain
    ChainDeleted --> [*]
```

---

*This technical documentation provides a comprehensive overview of the OneX cloud-native platform architecture, covering all major components, data flows, and technical implementations. The platform demonstrates modern microservices patterns, Kubernetes-native design, and enterprise-grade observability.*
