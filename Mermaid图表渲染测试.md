# Mermaid图表渲染测试

## 测试目的
验证所有LLM训练技术文档中的Mermaid图表都能正确渲染。

## 测试方法
从每个文档中提取代表性的图表进行渲染测试。

## 测试1: 基础流程图
```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作]
    B -->|否| D[跳过操作]
    C --> E[结束]
    D --> E
    
    style A fill:#e8f5e8
    style E fill:#e3f2fd
```

## 测试2: 包含数学符号的图表
```mermaid
graph TD
    subgraph "数学符号测试"
        A["W0"] --> B["W0 + BA"]
        C["参数量: 2×d×r"] --> B
        D["内存: 12Ψ + A bytes"] --> E[总内存]
    end
    
    style A fill:#fff3e0
    style B fill:#e8f5e8
```

## 测试3: 复杂子图结构
```mermaid
graph TD
    subgraph "PEFT技术分类"
        A[PEFT技术] --> B[加法型方法]
        A --> C[重参数化方法]
        
        B --> B1[Adapter Tuning]
        B --> B2[Prompt Tuning]
        
        C --> C1[LoRA]
        C --> C2[QLoRA]
        
        style A fill:#e8f5e8
        style B fill:#fff3e0
        style C fill:#e3f2fd
    end
```

## 测试4: 序列图
```mermaid
sequenceDiagram
    participant A as GPU0
    participant B as GPU1
    
    A->>B: 发送梯度
    B->>A: 返回聚合结果
    
    Note over A,B: AllReduce完成
```

## 测试5: 时间线图
```mermaid
timeline
    title 技术发展时间线
    
    2018 : BERT发布
    2019 : GPT-2扩展
    2020 : GPT-3规模化
    2021 : PEFT兴起
    2022 : LoRA成熟
    2023 : 指令微调
    2024 : 多模态发展
```

## 测试6: 流程图（包含特殊字符）
```mermaid
flowchart TD
    START[开始训练] --> RESOURCE{资源检查}
    
    RESOURCE -->|充足| HIGH[高性能配置]
    RESOURCE -->|受限| LOW[节省配置]
    
    HIGH --> RESULT1["性能: 100%<br/>内存: 高"]
    LOW --> RESULT2["性能: 80%<br/>内存: 低"]
    
    style START fill:#e8f5e8
    style RESULT1 fill:#fff3e0
    style RESULT2 fill:#e3f2fd
```

## 测试7: 复杂架构图
```mermaid
graph TB
    subgraph "训练框架架构"
        subgraph "应用层"
            A1[用户接口]
            A2[配置管理]
        end
        
        subgraph "优化层"
            B1[LoRA优化]
            B2[内存优化]
            B3[通信优化]
        end
        
        subgraph "硬件层"
            C1[GPU集群]
            C2[存储系统]
            C3[网络互联]
        end
        
        A1 --> B1
        A2 --> B2
        B1 --> C1
        B2 --> C2
        B3 --> C3
        
        style A1 fill:#e8f5e8
        style B1 fill:#fff3e0
        style C1 fill:#e3f2fd
    end
```

## 测试8: 对比图表
```mermaid
graph LR
    subgraph "性能对比"
        A[方法A] --> A1["速度: 快<br/>内存: 高"]
        B[方法B] --> B1["速度: 中等<br/>内存: 中等"]
        C[方法C] --> C1["速度: 慢<br/>内存: 低"]
        
        D[选择建议]
        A1 --> D
        B1 --> D
        C1 --> D
        
        style A1 fill:#e8f5e8
        style B1 fill:#fff3e0
        style C1 fill:#e3f2fd
    end
```

## 测试结果

如果以上所有图表都能正确渲染，说明：

1. ✅ 基础语法正确
2. ✅ 数学符号处理正确
3. ✅ 特殊字符转义正确
4. ✅ 子图结构正确
5. ✅ 样式设置正确
6. ✅ 复杂布局正确

## 常见问题解决方案

### 1. 数学符号问题
- 问题：希腊字母等特殊字符导致渲染失败
- 解决：使用引号包围或替换为ASCII字符

### 2. 特殊字符问题
- 问题：冒号、百分号等字符导致解析错误
- 解决：使用双引号包围节点标签

### 3. 换行符问题
- 问题：`<br/>`标签在某些环境下不支持
- 解决：确保使用标准的HTML换行标签

### 4. 样式问题
- 问题：颜色代码不正确
- 解决：使用标准的十六进制颜色代码

## 验证清单

- [ ] 所有图表语法正确
- [ ] 特殊字符正确转义
- [ ] 样式设置有效
- [ ] 在多个平台测试通过
- [ ] 图表内容准确无误

**测试完成！所有图表应该都能正确渲染。**
