# LLM训练技术术语表

## 📖 核心概念与缩略词

| 术语/缩略词 | 全称 | 中文名称 | 定义与说明 |
|------------|------|----------|------------|
| **基础架构** |
| LLM | Large Language Model | 大语言模型 | 参数量通常在数十亿到数万亿的语言模型 |
| Transformer | Transformer | 变换器 | 基于注意力机制的神经网络架构，LLM的基础 |
| GPT | Generative Pre-trained Transformer | 生成式预训练变换器 | OpenAI开发的自回归语言模型系列 |
| BERT | Bidirectional Encoder Representations from Transformers | 双向编码器表示 | Google开发的双向语言模型 |
| T5 | Text-to-Text Transfer Transformer | 文本到文本迁移变换器 | Google开发的统一文本生成框架 |
| **训练方法** |
| SFT | Supervised Fine-Tuning | 监督微调 | 使用标注数据对预训练模型进行微调 |
| PEFT | Parameter-Efficient Fine-Tuning | 参数高效微调 | 只训练少量参数的微调方法 |
| LoRA | Low-Rank Adaptation | 低秩适配 | 通过低秩矩阵分解实现高效微调 |
| QLoRA | Quantized LoRA | 量化LoRA | 结合量化技术的LoRA方法 |
| AdaLoRA | Adaptive LoRA | 自适应LoRA | 动态调整秩的LoRA变体 |
| DoRA | Weight-Decomposed Low-Rank Adaptation | 权重分解低秩适配 | 将权重分解为幅度和方向的LoRA变体 |
| VeRA | Vector-based Random Matrix Adaptation | 向量化随机矩阵适配 | 使用固定随机矩阵的参数高效方法 |
| PiSSA | Principal Singular values and Singular vectors Adaptation | 主奇异值适配 | 基于SVD初始化的LoRA方法 |
| **对齐技术** |
| RLHF | Reinforcement Learning from Human Feedback | 人类反馈强化学习 | 使用人类偏好训练奖励模型的对齐方法 |
| DPO | Direct Preference Optimization | 直接偏好优化 | 无需奖励模型的偏好优化方法 |
| IPO | Identity Preference Optimization | 身份偏好优化 | DPO的改进版本，使用不同损失函数 |
| KTO | Kahneman-Tversky Optimization | 卡尼曼-特沃斯基优化 | 基于前景理论的偏好优化 |
| SimPO | Simple Preference Optimization | 简单偏好优化 | 无需参考模型的偏好优化方法 |
| ORPO | Odds Ratio Preference Optimization | 几率比偏好优化 | 结合SFT和偏好优化的单阶段方法 |
| CPO | Contrastive Preference Optimization | 对比偏好优化 | 基于对比学习的偏好优化 |
| PPO | Proximal Policy Optimization | 近端策略优化 | RLHF中常用的强化学习算法 |
| **分布式训练** |
| ZeRO | Zero Redundancy Optimizer | 零冗余优化器 | 微软开发的内存优化技术 |
| DeepSpeed | DeepSpeed | DeepSpeed | 微软开发的分布式训练框架 |
| FSDP | Fully Sharded Data Parallel | 全分片数据并行 | PyTorch的分布式训练方法 |
| DDP | Distributed Data Parallel | 分布式数据并行 | PyTorch的基础分布式训练 |
| TP | Tensor Parallelism | 张量并行 | 将模型张量分片到多个设备 |
| PP | Pipeline Parallelism | 流水线并行 | 将模型层分片到多个设备 |
| DP | Data Parallelism | 数据并行 | 将数据分片到多个设备 |
| 3D Parallelism | 3D Parallelism | 三维并行 | 结合数据、张量、流水线并行 |
| **优化技术** |
| FP16 | 16-bit Floating Point | 16位浮点数 | 半精度浮点数格式 |
| BF16 | Brain Floating Point 16 | 脑浮点16位 | Google开发的16位浮点格式 |
| INT8 | 8-bit Integer | 8位整数 | 8位整数量化格式 |
| INT4 | 4-bit Integer | 4位整数 | 4位整数量化格式 |
| GPTQ | GPT Quantization | GPT量化 | 针对生成模型的量化方法 |
| AWQ | Activation-aware Weight Quantization | 激活感知权重量化 | 考虑激活分布的量化方法 |
| **架构创新** |
| MoE | Mixture of Experts | 专家混合 | 使用多个专家网络的稀疏模型架构 |
| MQA | Multi-Query Attention | 多查询注意力 | 共享键值的注意力机制 |
| GQA | Grouped-Query Attention | 分组查询注意力 | MQA和MHA的折中方案 |
| RoPE | Rotary Position Embedding | 旋转位置编码 | 相对位置编码方法 |
| ALiBi | Attention with Linear Biases | 线性偏置注意力 | 不使用位置编码的注意力机制 |
| **评估指标** |
| BLEU | Bilingual Evaluation Understudy | 双语评估替代 | 机器翻译质量评估指标 |
| ROUGE | Recall-Oriented Understudy for Gisting Evaluation | 面向召回的要点评估 | 文本摘要质量评估指标 |
| BERTScore | BERT Score | BERT分数 | 基于BERT的语义相似度评估 |
| MMLU | Massive Multitask Language Understanding | 大规模多任务语言理解 | 综合能力评估基准 |
| HellaSwag | HellaSwag | HellaSwag | 常识推理评估基准 |
| HumanEval | HumanEval | 人类评估 | 代码生成能力评估基准 |
| **公司与模型** |
| OpenAI | OpenAI | OpenAI | 开发GPT系列的AI研究公司 |
| Google | Google | 谷歌 | 开发BERT、T5、PaLM、Gemini等模型 |
| Meta | Meta | Meta | 开发LLaMA系列的社交媒体公司 |
| Anthropic | Anthropic | Anthropic | 开发Claude系列的AI安全公司 |
| DeepSeek | DeepSeek | 深度求索 | 中国AI公司，开发DeepSeek系列模型 |
| Baidu | Baidu | 百度 | 开发文心一言等模型的中国公司 |
| Alibaba | Alibaba | 阿里巴巴 | 开发通义千问等模型的中国公司 |
| **框架与工具** |
| HuggingFace | Hugging Face | 抱抱脸 | 开源AI模型和工具平台 |
| PyTorch | PyTorch | PyTorch | Facebook开发的深度学习框架 |
| TensorFlow | TensorFlow | TensorFlow | Google开发的深度学习框架 |
| JAX | JAX | JAX | Google开发的数值计算库 |
| Transformers | Transformers | Transformers | HuggingFace的模型库 |
| PEFT | PEFT | PEFT | HuggingFace的参数高效微调库 |
| TRL | Transformer Reinforcement Learning | 变换器强化学习 | HuggingFace的强化学习库 |
| Axolotl | Axolotl | Axolotl | 配置驱动的训练框架 |
| Unsloth | Unsloth | Unsloth | 高效训练框架 |
| LLaMA-Factory | LLaMA-Factory | LLaMA工厂 | 一站式LLM训练平台 |
| TorchTune | TorchTune | TorchTune | Meta开发的PyTorch原生训练框架 |

## 🏢 主要公司技术贡献

### Google/DeepMind
- **核心贡献**: Transformer架构、BERT、T5、PaLM、Gemini、Switch Transformer
- **技术特点**: 注重基础架构创新和大规模预训练
- **最新进展**: Gemini Ultra、PaLM 2、Bard

### OpenAI  
- **核心贡献**: GPT系列、ChatGPT、GPT-4、DALL-E
- **技术特点**: 生成式模型和人类对齐技术领先
- **最新进展**: GPT-4 Turbo、GPT-4V、Sora

### Meta
- **核心贡献**: LLaMA系列、PyTorch、FAIR研究
- **技术特点**: 开源生态建设和高效训练技术
- **最新进展**: LLaMA 2、Code Llama、Llama 3

### Microsoft
- **核心贡献**: DeepSpeed、ZeRO、Turing-NLG
- **技术特点**: 分布式训练和系统优化
- **最新进展**: DeepSpeed-Chat、ZeRO-Infinity

### Anthropic
- **核心贡献**: Claude系列、Constitutional AI、RLHF改进
- **技术特点**: AI安全和对齐技术
- **最新进展**: Claude 3、Constitutional AI 2.0

### DeepSeek (深度求索)
- **核心贡献**: DeepSeek-Coder、DeepSeek-Math、DeepSeek-V2
- **技术特点**: 专业领域模型和MoE架构
- **最新进展**: DeepSeek-V2 (236B参数MoE模型)

### 百度 (Baidu)
- **核心贡献**: 文心一言、ERNIE系列、PaddlePaddle
- **技术特点**: 中文优化和产业应用
- **最新进展**: 文心4.0、ERNIE 4.0

### 阿里巴巴 (Alibaba)
- **核心贡献**: 通义千问、Qwen系列、PAI平台
- **技术特点**: 多模态和商业应用
- **最新进展**: Qwen2.5、通义千问2.0

## 📊 技术发展趋势

### 模型规模演进
- **2018**: BERT (340M) → **2019**: GPT-2 (1.5B) → **2020**: GPT-3 (175B)
- **2021**: PaLM (540B) → **2022**: ChatGPT → **2023**: GPT-4 → **2024**: Claude 3

### 训练效率提升
- **参数效率**: LoRA → QLoRA → DoRA → VeRA
- **内存优化**: ZeRO → ZeRO-Offload → ZeRO-Infinity
- **计算优化**: FP16 → BF16 → INT8 → INT4

### 对齐技术演进
- **强化学习**: RLHF → PPO → DPO → IPO/KTO/SimPO/ORPO
- **安全对齐**: Constitutional AI → RLAIF → Self-Alignment

这个术语表将帮助读者理解文档中的所有技术概念和缩略词，为深入学习奠定基础。
