# AI/ML算法权威实战手册：理论、实践与工程的完整体系

> **目标**: 打造业界最全面的AI/ML实战指南，涵盖从数学基础到工程实践的所有关键知识点，让读者无需查阅其他资料即可掌握AI/ML的完整技能体系。

## 📚 完整目录

### 第一部分：理论基础
1. [AI三大学派的深度解析](#第一章-ai三大学派)
2. [数学基础的完整体系](#第二章-数学基础)
   - 2.1 [微积分在AI中的核心应用](#微积分核心应用)
   - 2.2 [线性代数的深度解析](#线性代数深度解析)
   - 2.3 [概率论与统计学基础](#概率统计基础)
   - 2.4 [最优化理论与算法](#最优化理论)
   - 2.5 [信息论与熵理论](#信息论基础)
   - 2.6 [图论与网络分析](#图论基础)

### 第二部分：机器学习核心
3. [监督学习完整体系](#第三章-监督学习)
4. [无监督学习深度剖析](#第四章-无监督学习)
5. [半监督与强化学习](#第五章-半监督强化学习)
6. [集成学习与模型融合](#第六章-集成学习)

### 第三部分：深度学习与前沿技术
7. [深度学习架构全解析](#第七章-深度学习)
8. [计算机视觉技术栈](#第八章-计算机视觉)
9. [自然语言处理技术](#第九章-自然语言处理)
10. [生成式AI与大模型](#第十章-生成式ai)

### 第四部分：评估与优化
11. [模型评估的工业标准](#第十一章-模型评估)
12. [超参数优化与AutoML](#第十二章-超参数优化)
13. [模型解释性与可信AI](#第十三章-模型解释性)

### 第五部分：工程实践
14. [算法选择的决策框架](#第十四章-算法选择)
15. [大厂实战案例分析](#第十五章-实战案例)
16. [MLOps与生产部署](#第十六章-mlops)

### 第六部分：附录与资源
17. [完整代码库与工具链](#附录a-代码库)
18. [数学公式速查手册](#附录b-公式手册)
19. [算法复杂度对比表](#附录c-复杂度表)
20. [行业应用场景图谱](#附录d-应用图谱)

---

# 第一章：AI三大学派的深度解析

## 1.1 学派演进的历史脉络

AI发展历程中形成了三大主要学派，每个学派都有其独特的哲学基础、技术路线和应用领域。理解这些学派的本质差异对于选择合适的AI方法至关重要。

```mermaid
timeline
    title AI三大学派发展时间线
    1950s-1960s : 符号主义兴起
                : 图灵测试提出
                : 专家系统概念
    1960s-1980s : 连接主义萌芽
                : 感知机模型
                : 反向传播算法
    1980s-1990s : 行为主义发展
                : 强化学习理论
                : 遗传算法成熟
    2000s-2010s : 深度学习复兴
                : 大数据驱动
                : GPU加速计算
    2010s-现在  : 三大学派融合
                : Transformer革命
                : 大语言模型时代
```

## 1.2 符号主义学派 (Symbolism) - 逻辑推理的艺术

### 1.2.1 核心哲学思想
符号主义认为**智能 = 符号操作 + 逻辑推理**。这一学派相信人类智能的本质是对符号的操作，可以通过形式化的逻辑系统来模拟。

### 1.2.2 技术架构体系

```mermaid
graph TD
    A[知识表示] --> B[推理引擎]
    B --> C[解释机制]
    C --> D[知识获取]
    D --> A

    A1[语义网络] --> A
    A2[框架表示] --> A
    A3[产生式规则] --> A
    A4[描述逻辑] --> A

    B1[前向推理] --> B
    B2[后向推理] --> B
    B3[混合推理] --> B

    C1[推理路径] --> C
    C2[置信度] --> C
    C3[解释文本] --> C
```

### 1.2.3 核心技术详解

#### 知识表示方法
1. **语义网络 (Semantic Networks)**
```python
class SemanticNetwork:
    def __init__(self):
        self.nodes = {}  # 概念节点
        self.edges = {}  # 关系边

    def add_concept(self, concept, properties):
        """添加概念节点"""
        self.nodes[concept] = {
            'type': 'concept',
            'properties': properties,
            'relations': []
        }

    def add_relation(self, subject, predicate, object):
        """添加关系三元组"""
        relation_id = f"{subject}-{predicate}-{object}"
        self.edges[relation_id] = {
            'subject': subject,
            'predicate': predicate,
            'object': object,
            'confidence': 1.0
        }

        # 更新节点的关系列表
        if subject in self.nodes:
            self.nodes[subject]['relations'].append(relation_id)

    def query_relations(self, concept):
        """查询概念的所有关系"""
        if concept not in self.nodes:
            return []

        relations = []
        for rel_id in self.nodes[concept]['relations']:
            relations.append(self.edges[rel_id])
        return relations

# 使用示例：医疗诊断知识网络
medical_net = SemanticNetwork()
medical_net.add_concept("发烧", {"type": "症状", "severity": "中等"})
medical_net.add_concept("感冒", {"type": "疾病", "category": "呼吸系统"})
medical_net.add_relation("感冒", "导致", "发烧")
medical_net.add_relation("发烧", "治疗方法", "退烧药")
```

2. **产生式规则系统**
```python
class ProductionRuleSystem:
    def __init__(self):
        self.rules = []
        self.facts = set()
        self.working_memory = []

    def add_rule(self, conditions, actions, confidence=1.0):
        """添加产生式规则: IF conditions THEN actions"""
        rule = {
            'id': len(self.rules),
            'conditions': conditions,
            'actions': actions,
            'confidence': confidence,
            'fired_count': 0
        }
        self.rules.append(rule)

    def add_fact(self, fact):
        """添加事实到工作记忆"""
        self.facts.add(fact)
        self.working_memory.append(('ASSERT', fact))

    def forward_chaining(self):
        """前向链推理"""
        changed = True
        inference_steps = []

        while changed:
            changed = False
            for rule in self.rules:
                if self.match_conditions(rule['conditions']):
                    # 执行规则动作
                    for action in rule['actions']:
                        if action not in self.facts:
                            self.facts.add(action)
                            self.working_memory.append(('INFER', action, rule['id']))
                            inference_steps.append({
                                'rule_id': rule['id'],
                                'conditions': rule['conditions'],
                                'conclusion': action,
                                'confidence': rule['confidence']
                            })
                            changed = True
                            rule['fired_count'] += 1

        return inference_steps

    def match_conditions(self, conditions):
        """检查条件是否满足"""
        for condition in conditions:
            if condition not in self.facts:
                return False
        return True

# 医疗诊断规则系统示例
diagnosis_system = ProductionRuleSystem()

# 添加诊断规则
diagnosis_system.add_rule(
    conditions=["发烧", "咳嗽", "流鼻涕"],
    actions=["可能患感冒"],
    confidence=0.8
)

diagnosis_system.add_rule(
    conditions=["发烧", "咳嗽", "胸痛"],
    actions=["可能患肺炎"],
    confidence=0.9
)

# 添加症状事实
diagnosis_system.add_fact("发烧")
diagnosis_system.add_fact("咳嗽")
diagnosis_system.add_fact("流鼻涕")

# 执行推理
results = diagnosis_system.forward_chaining()
print("诊断结果:", diagnosis_system.facts)
```

### 1.2.4 现代应用与案例

#### 知识图谱在搜索引擎中的应用
Google的知识图谱包含超过5000亿个事实，涵盖70亿个实体：

```python
class KnowledgeGraph:
    def __init__(self):
        self.entities = {}
        self.relations = {}
        self.schema = {}

    def add_entity(self, entity_id, entity_type, properties):
        """添加实体"""
        self.entities[entity_id] = {
            'type': entity_type,
            'properties': properties,
            'relations': []
        }

    def add_relation(self, head, relation, tail, confidence=1.0):
        """添加关系三元组"""
        rel_id = f"{head}_{relation}_{tail}"
        self.relations[rel_id] = {
            'head': head,
            'relation': relation,
            'tail': tail,
            'confidence': confidence
        }

        # 更新实体的关系列表
        if head in self.entities:
            self.entities[head]['relations'].append(rel_id)

    def sparql_query(self, query):
        """SPARQL查询接口"""
        # 简化的SPARQL查询实现
        results = []
        # 实际实现会解析SPARQL语法并执行查询
        return results

    def entity_linking(self, text):
        """实体链接：将文本中的提及链接到知识图谱实体"""
        mentions = self.extract_mentions(text)
        linked_entities = []

        for mention in mentions:
            candidates = self.find_candidate_entities(mention)
            best_entity = self.rank_candidates(mention, candidates)
            linked_entities.append((mention, best_entity))

        return linked_entities
```

## 1.3 连接主义学派 (Connectionism) - 神经网络的力量

### 1.3.1 生物学启发与数学建模

连接主义的核心思想源于对人脑神经网络的模拟。人脑包含约1000亿个神经元，每个神经元平均连接7000个其他神经元。

#### 神经元数学模型
```python
import numpy as np
import matplotlib.pyplot as plt

class Neuron:
    def __init__(self, n_inputs, activation_function='sigmoid'):
        # 权重初始化 - Xavier初始化
        self.weights = np.random.randn(n_inputs) * np.sqrt(2.0 / n_inputs)
        self.bias = 0.0
        self.activation_function = activation_function

        # 用于反向传播的缓存
        self.last_input = None
        self.last_output = None

    def activate(self, x):
        """激活函数"""
        if self.activation_function == 'sigmoid':
            return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
        elif self.activation_function == 'tanh':
            return np.tanh(x)
        elif self.activation_function == 'relu':
            return np.maximum(0, x)
        elif self.activation_function == 'leaky_relu':
            return np.where(x > 0, x, 0.01 * x)

    def forward(self, inputs):
        """前向传播"""
        self.last_input = inputs
        z = np.dot(inputs, self.weights) + self.bias
        self.last_output = self.activate(z)
        return self.last_output

    def backward(self, gradient):
        """反向传播"""
        # 计算激活函数的导数
        if self.activation_function == 'sigmoid':
            activation_derivative = self.last_output * (1 - self.last_output)
        elif self.activation_function == 'tanh':
            activation_derivative = 1 - self.last_output**2
        elif self.activation_function == 'relu':
            activation_derivative = (self.last_output > 0).astype(float)

        # 计算梯度
        delta = gradient * activation_derivative
        weight_gradient = np.outer(self.last_input, delta)
        bias_gradient = delta
        input_gradient = np.dot(delta, self.weights.T)

        return weight_gradient, bias_gradient, input_gradient
```

### 1.3.2 深度学习架构演进

```mermaid
graph TD
    A[感知机 1957] --> B[多层感知机 1986]
    B --> C[卷积神经网络 1989]
    C --> D[循环神经网络 1990]
    D --> E[LSTM 1997]
    E --> F[ResNet 2015]
    F --> G[Transformer 2017]
    G --> H[BERT 2018]
    H --> I[GPT系列 2018-2023]
    I --> J[多模态大模型 2023+]

    style A fill:#ffcccc
    style G fill:#ccffcc
    style I fill:#ccccff
```

## 1.4 行为主义学派 (Behaviorism) - 环境交互学习

### 1.4.1 强化学习的数学框架

行为主义学派的核心是马尔可夫决策过程(MDP)：

**MDP定义**: 五元组 (S, A, P, R, γ)
- S: 状态空间
- A: 动作空间
- P: 状态转移概率 P(s'|s,a)
- R: 奖励函数 R(s,a,s')
- γ: 折扣因子

```python
class MDPEnvironment:
    def __init__(self, states, actions, transition_probs, rewards, gamma=0.9):
        self.states = states
        self.actions = actions
        self.P = transition_probs  # P[s][a] = [(prob, next_state, reward)]
        self.R = rewards
        self.gamma = gamma
        self.current_state = None

    def reset(self):
        """重置环境到初始状态"""
        self.current_state = np.random.choice(self.states)
        return self.current_state

    def step(self, action):
        """执行动作，返回下一状态、奖励、是否结束"""
        if self.current_state is None:
            raise ValueError("Environment not reset")

        # 根据转移概率选择下一状态
        transitions = self.P[self.current_state][action]
        probs, next_states, rewards = zip(*transitions)

        next_state_idx = np.random.choice(len(next_states), p=probs)
        next_state = next_states[next_state_idx]
        reward = rewards[next_state_idx]

        self.current_state = next_state
        done = self.is_terminal(next_state)

        return next_state, reward, done

    def is_terminal(self, state):
        """判断是否为终止状态"""
        return state in self.terminal_states

class QLearningAgent:
    def __init__(self, states, actions, learning_rate=0.1, epsilon=0.1, gamma=0.9):
        self.states = states
        self.actions = actions
        self.lr = learning_rate
        self.epsilon = epsilon
        self.gamma = gamma

        # 初始化Q表
        self.Q = {}
        for state in states:
            self.Q[state] = {}
            for action in actions:
                self.Q[state][action] = 0.0

    def choose_action(self, state):
        """ε-贪婪策略选择动作"""
        if np.random.random() < self.epsilon:
            return np.random.choice(self.actions)
        else:
            return max(self.actions, key=lambda a: self.Q[state][a])

    def update(self, state, action, reward, next_state):
        """Q-learning更新规则"""
        if next_state is not None:
            max_next_q = max(self.Q[next_state].values())
        else:
            max_next_q = 0.0

        # Q(s,a) ← Q(s,a) + α[r + γ max Q(s',a') - Q(s,a)]
        current_q = self.Q[state][action]
        new_q = current_q + self.lr * (reward + self.gamma * max_next_q - current_q)
        self.Q[state][action] = new_q
```

### 1.5 三大学派的现代融合

现代AI系统往往融合了三大学派的优势：

```mermaid
graph LR
    A[符号主义] --> D[神经符号AI]
    B[连接主义] --> D
    B --> E[深度强化学习]
    C[行为主义] --> E
    A --> F[可解释AI]
    B --> F

    D --> G[知识增强神经网络]
    E --> H[AlphaGo/ChatGPT]
    F --> I[可信AI系统]
```

#### 融合案例：AlphaGo的三学派结合
1. **连接主义**: 深度神经网络评估棋局
2. **行为主义**: 强化学习优化策略
3. **符号主义**: 蒙特卡洛树搜索提供逻辑推理

# 第二章：数学基础的完整体系

> **核心理念**: 数学是AI的语言，掌握这些数学工具不仅能理解算法原理，更能指导实际问题的建模和求解。

## 2.1 微积分核心应用

### 2.1.1 梯度与优化的几何直觉

梯度是多元函数在某点处变化率最大的方向，这一概念是所有优化算法的基础。

```mermaid
graph TD
    A[函数f(x,y)] --> B[偏导数∂f/∂x, ∂f/∂y]
    B --> C[梯度向量∇f]
    C --> D[梯度下降方向]
    D --> E[参数更新]
    E --> F[损失函数最小化]

    G[链式法则] --> H[复合函数求导]
    H --> I[反向传播算法]
    I --> J[深度网络训练]
```

#### 核心数学公式与实现

```python
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

class GradientCalculator:
    """梯度计算与可视化工具"""

    def __init__(self):
        self.epsilon = 1e-7  # 数值微分的步长

    def numerical_gradient(self, f, x):
        """数值方法计算梯度"""
        grad = np.zeros_like(x)
        for i in range(len(x)):
            x_plus = x.copy()
            x_minus = x.copy()
            x_plus[i] += self.epsilon
            x_minus[i] -= self.epsilon

            grad[i] = (f(x_plus) - f(x_minus)) / (2 * self.epsilon)
        return grad

    def analytical_gradient(self, f_grad, x):
        """解析方法计算梯度"""
        return f_grad(x)

    def visualize_gradient_descent(self, f, f_grad, x_init, learning_rate=0.01, iterations=100):
        """可视化梯度下降过程"""
        # 记录优化路径
        path = [x_init.copy()]
        x = x_init.copy()

        for i in range(iterations):
            grad = f_grad(x)
            x = x - learning_rate * grad
            path.append(x.copy())

            if np.linalg.norm(grad) < 1e-6:
                break

        return np.array(path)

# 示例：二次函数的梯度下降
def quadratic_function(x):
    """二次函数 f(x,y) = x² + 2y² + xy"""
    return x[0]**2 + 2*x[1]**2 + x[0]*x[1]

def quadratic_gradient(x):
    """二次函数的梯度"""
    return np.array([2*x[0] + x[1], 4*x[1] + x[0]])

# 可视化梯度下降
calc = GradientCalculator()
x_init = np.array([3.0, 2.0])
path = calc.visualize_gradient_descent(quadratic_function, quadratic_gradient, x_init)

print(f"初始点: {x_init}")
print(f"最终点: {path[-1]}")
print(f"函数值变化: {quadratic_function(x_init):.6f} → {quadratic_function(path[-1]):.6f}")
```

### 2.1.2 链式法则与反向传播

链式法则是深度学习的数学基础，它使得我们能够高效计算复杂网络的梯度。

#### 数学推导
对于复合函数 z = f(g(x))，链式法则告诉我们：
$$\frac{dz}{dx} = \frac{dz}{dg} \cdot \frac{dg}{dx}$$

在神经网络中，这扩展为：
$$\frac{\partial L}{\partial w_{ij}^{(l)}} = \frac{\partial L}{\partial a_j^{(l+1)}} \cdot \frac{\partial a_j^{(l+1)}}{\partial z_j^{(l+1)}} \cdot \frac{\partial z_j^{(l+1)}}{\partial w_{ij}^{(l)}}$$

```python
class BackpropagationDemo:
    """反向传播算法的详细实现"""

    def __init__(self, layers):
        """
        layers: 每层神经元数量的列表，如[784, 128, 64, 10]
        """
        self.layers = layers
        self.weights = []
        self.biases = []

        # Xavier初始化
        for i in range(len(layers)-1):
            w = np.random.randn(layers[i], layers[i+1]) * np.sqrt(2.0 / layers[i])
            b = np.zeros((1, layers[i+1]))
            self.weights.append(w)
            self.biases.append(b)

    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

    def sigmoid_derivative(self, x):
        """Sigmoid函数的导数"""
        s = self.sigmoid(x)
        return s * (1 - s)

    def forward_pass(self, X):
        """前向传播"""
        self.activations = [X]  # 存储每层的激活值
        self.z_values = []      # 存储每层的加权输入

        activation = X
        for i, (w, b) in enumerate(zip(self.weights, self.biases)):
            z = np.dot(activation, w) + b
            self.z_values.append(z)
            activation = self.sigmoid(z)
            self.activations.append(activation)

        return activation

    def backward_pass(self, X, y, learning_rate=0.01):
        """反向传播"""
        m = X.shape[0]  # 样本数量

        # 计算输出层误差
        delta = self.activations[-1] - y

        # 反向传播误差
        deltas = [delta]
        for i in range(len(self.weights)-2, -1, -1):
            delta = np.dot(delta, self.weights[i+1].T) * self.sigmoid_derivative(self.z_values[i])
            deltas.append(delta)

        deltas.reverse()

        # 更新权重和偏置
        for i in range(len(self.weights)):
            dw = np.dot(self.activations[i].T, deltas[i]) / m
            db = np.sum(deltas[i], axis=0, keepdims=True) / m

            self.weights[i] -= learning_rate * dw
            self.biases[i] -= learning_rate * db

    def train(self, X, y, epochs=1000, learning_rate=0.01):
        """训练网络"""
        losses = []

        for epoch in range(epochs):
            # 前向传播
            output = self.forward_pass(X)

            # 计算损失
            loss = np.mean((output - y)**2)
            losses.append(loss)

            # 反向传播
            self.backward_pass(X, y, learning_rate)

            if epoch % 100 == 0:
                print(f"Epoch {epoch}, Loss: {loss:.6f}")

        return losses

# 使用示例：XOR问题
X = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])
y = np.array([[0], [1], [1], [0]])

# 创建网络：2输入 -> 4隐藏 -> 1输出
network = BackpropagationDemo([2, 4, 1])
losses = network.train(X, y, epochs=5000, learning_rate=1.0)

# 测试结果
predictions = network.forward_pass(X)
print("\nXOR问题测试结果:")
for i in range(len(X)):
    print(f"输入: {X[i]}, 期望: {y[i][0]}, 预测: {predictions[i][0]:.4f}")
```

### 2.1.3 泰勒展开与函数近似

泰勒展开在优化算法中用于函数的局部线性化，是牛顿法等二阶优化方法的基础。

#### 一阶泰勒展开（梯度下降的理论基础）
$$f(x + \Delta x) \approx f(x) + \nabla f(x)^T \Delta x$$

#### 二阶泰勒展开（牛顿法的理论基础）
$$f(x + \Delta x) \approx f(x) + \nabla f(x)^T \Delta x + \frac{1}{2}\Delta x^T H(x) \Delta x$$

其中 H(x) 是Hessian矩阵。

```python
class OptimizationMethods:
    """各种优化方法的实现与比较"""

    def __init__(self):
        self.history = {}

    def gradient_descent(self, f, grad_f, x0, lr=0.01, max_iter=1000, tol=1e-6):
        """梯度下降法"""
        x = x0.copy()
        path = [x.copy()]

        for i in range(max_iter):
            grad = grad_f(x)
            if np.linalg.norm(grad) < tol:
                break
            x = x - lr * grad
            path.append(x.copy())

        return x, np.array(path)

    def newton_method(self, f, grad_f, hess_f, x0, max_iter=100, tol=1e-6):
        """牛顿法"""
        x = x0.copy()
        path = [x.copy()]

        for i in range(max_iter):
            grad = grad_f(x)
            if np.linalg.norm(grad) < tol:
                break

            hess = hess_f(x)
            try:
                # 牛顿步长：x_{k+1} = x_k - H^{-1} * grad
                delta_x = np.linalg.solve(hess, grad)
                x = x - delta_x
                path.append(x.copy())
            except np.linalg.LinAlgError:
                print("Hessian矩阵奇异，切换到梯度下降")
                x = x - 0.01 * grad
                path.append(x.copy())

        return x, np.array(path)

    def adam_optimizer(self, f, grad_f, x0, lr=0.001, beta1=0.9, beta2=0.999,
                      epsilon=1e-8, max_iter=1000, tol=1e-6):
        """Adam优化器"""
        x = x0.copy()
        m = np.zeros_like(x)  # 一阶矩估计
        v = np.zeros_like(x)  # 二阶矩估计
        path = [x.copy()]

        for t in range(1, max_iter + 1):
            grad = grad_f(x)
            if np.linalg.norm(grad) < tol:
                break

            # 更新偏置修正的一阶和二阶矩估计
            m = beta1 * m + (1 - beta1) * grad
            v = beta2 * v + (1 - beta2) * grad**2

            # 偏置修正
            m_hat = m / (1 - beta1**t)
            v_hat = v / (1 - beta2**t)

            # 参数更新
            x = x - lr * m_hat / (np.sqrt(v_hat) + epsilon)
            path.append(x.copy())

        return x, np.array(path)

# 比较不同优化方法
def rosenbrock_function(x):
    """Rosenbrock函数：经典的优化测试函数"""
    return 100 * (x[1] - x[0]**2)**2 + (1 - x[0])**2

def rosenbrock_gradient(x):
    """Rosenbrock函数的梯度"""
    return np.array([
        -400 * x[0] * (x[1] - x[0]**2) - 2 * (1 - x[0]),
        200 * (x[1] - x[0]**2)
    ])

def rosenbrock_hessian(x):
    """Rosenbrock函数的Hessian矩阵"""
    return np.array([
        [-400 * (x[1] - 3*x[0]**2) + 2, -400 * x[0]],
        [-400 * x[0], 200]
    ])

# 测试不同优化方法
optimizer = OptimizationMethods()
x0 = np.array([-1.0, 1.0])

print("优化方法比较 (Rosenbrock函数):")
print(f"初始点: {x0}, 函数值: {rosenbrock_function(x0):.6f}")
print(f"全局最优点: [1, 1], 函数值: 0")

# 梯度下降
x_gd, path_gd = optimizer.gradient_descent(rosenbrock_function, rosenbrock_gradient, x0, lr=0.001)
print(f"\n梯度下降结果: {x_gd}, 函数值: {rosenbrock_function(x_gd):.6f}, 迭代次数: {len(path_gd)}")

# 牛顿法
x_newton, path_newton = optimizer.newton_method(rosenbrock_function, rosenbrock_gradient, rosenbrock_hessian, x0)
print(f"牛顿法结果: {x_newton}, 函数值: {rosenbrock_function(x_newton):.6f}, 迭代次数: {len(path_newton)}")

# Adam优化器
x_adam, path_adam = optimizer.adam_optimizer(rosenbrock_function, rosenbrock_gradient, x0)
print(f"Adam结果: {x_adam}, 函数值: {rosenbrock_function(x_adam):.6f}, 迭代次数: {len(path_adam)}")
```

## 2.2 线性代数深度解析

### 2.2.1 向量空间与特征空间

在机器学习中，数据通常表示为高维向量空间中的点。理解向量空间的性质对于特征工程和降维至关重要。

#### 向量空间的基本性质
```python
class VectorSpace:
    """向量空间操作与分析"""

    def __init__(self, dimension):
        self.dimension = dimension

    def inner_product(self, u, v):
        """内积：<u,v> = u^T v"""
        return np.dot(u, v)

    def norm(self, v, p=2):
        """向量范数"""
        if p == 1:
            return np.sum(np.abs(v))  # L1范数
        elif p == 2:
            return np.sqrt(np.sum(v**2))  # L2范数
        elif p == np.inf:
            return np.max(np.abs(v))  # 无穷范数
        else:
            return np.sum(np.abs(v)**p)**(1/p)  # Lp范数

    def angle_between_vectors(self, u, v):
        """计算两向量夹角"""
        cos_theta = self.inner_product(u, v) / (self.norm(u) * self.norm(v))
        return np.arccos(np.clip(cos_theta, -1, 1))

    def project_onto_subspace(self, v, basis_vectors):
        """将向量投影到子空间"""
        # 使用Gram-Schmidt正交化
        orthonormal_basis = self.gram_schmidt(basis_vectors)

        # 投影：proj = Σ <v, u_i> u_i
        projection = np.zeros_like(v)
        for u in orthonormal_basis:
            projection += self.inner_product(v, u) * u

        return projection

    def gram_schmidt(self, vectors):
        """Gram-Schmidt正交化过程"""
        orthonormal = []

        for v in vectors:
            # 减去在之前向量上的投影
            u = v.copy()
            for prev_u in orthonormal:
                u -= self.inner_product(v, prev_u) * prev_u

            # 归一化
            norm_u = self.norm(u)
            if norm_u > 1e-10:  # 避免零向量
                u = u / norm_u
                orthonormal.append(u)

        return orthonormal

# 示例：文本向量空间分析
def text_vector_analysis():
    """文本向量空间分析示例"""
    # 假设我们有3个文档的TF-IDF向量
    doc1 = np.array([0.5, 0.3, 0.8, 0.1, 0.0])  # "机器学习算法"
    doc2 = np.array([0.4, 0.2, 0.7, 0.2, 0.1])  # "深度学习网络"
    doc3 = np.array([0.1, 0.0, 0.2, 0.8, 0.9])  # "自然语言处理"

    vs = VectorSpace(5)

    # 计算文档相似度
    sim_12 = vs.inner_product(doc1, doc2) / (vs.norm(doc1) * vs.norm(doc2))
    sim_13 = vs.inner_product(doc1, doc3) / (vs.norm(doc1) * vs.norm(doc3))
    sim_23 = vs.inner_product(doc2, doc3) / (vs.norm(doc2) * vs.norm(doc3))

    print("文档相似度分析:")
    print(f"文档1与文档2的余弦相似度: {sim_12:.4f}")
    print(f"文档1与文档3的余弦相似度: {sim_13:.4f}")
    print(f"文档2与文档3的余弦相似度: {sim_23:.4f}")

    # 构建主题子空间
    topic_basis = [doc1, doc2]  # 假设这两个文档定义了一个主题子空间
    doc3_projection = vs.project_onto_subspace(doc3, topic_basis)

    print(f"\n文档3在主题子空间的投影: {doc3_projection}")
    print(f"投影长度: {vs.norm(doc3_projection):.4f}")
    print(f"原向量长度: {vs.norm(doc3):.4f}")

text_vector_analysis()

### 2.2.2 矩阵分解与降维

矩阵分解是机器学习中的核心技术，用于降维、特征提取和数据压缩。

#### 特征值分解 (Eigendecomposition)
对于方阵 A，如果存在标量 λ 和非零向量 v 使得 Av = λv，则称 λ 为特征值，v 为特征向量。

```python
class MatrixDecomposition:
    """矩阵分解方法集合"""

    def __init__(self):
        self.tolerance = 1e-10

    def eigendecomposition(self, A):
        """特征值分解"""
        eigenvalues, eigenvectors = np.linalg.eig(A)

        # 按特征值大小排序
        idx = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]

        return eigenvalues, eigenvectors

    def pca_analysis(self, X, n_components=None):
        """主成分分析的完整实现"""
        # 1. 数据中心化
        X_centered = X - np.mean(X, axis=0)

        # 2. 计算协方差矩阵
        cov_matrix = np.cov(X_centered.T)

        # 3. 特征值分解
        eigenvalues, eigenvectors = self.eigendecomposition(cov_matrix)

        # 4. 选择主成分
        if n_components is None:
            n_components = len(eigenvalues)

        # 计算解释方差比
        explained_variance_ratio = eigenvalues / np.sum(eigenvalues)
        cumulative_variance_ratio = np.cumsum(explained_variance_ratio)

        # 选择前n个主成分
        principal_components = eigenvectors[:, :n_components]
        selected_eigenvalues = eigenvalues[:n_components]

        # 5. 数据变换
        X_transformed = np.dot(X_centered, principal_components)

        return {
            'transformed_data': X_transformed,
            'principal_components': principal_components,
            'eigenvalues': selected_eigenvalues,
            'explained_variance_ratio': explained_variance_ratio[:n_components],
            'cumulative_variance_ratio': cumulative_variance_ratio[:n_components],
            'mean': np.mean(X, axis=0)
        }

    def svd_analysis(self, A):
        """奇异值分解分析"""
        U, s, Vt = np.linalg.svd(A, full_matrices=False)

        # 计算重构误差
        def reconstruction_error(k):
            A_k = U[:, :k] @ np.diag(s[:k]) @ Vt[:k, :]
            return np.linalg.norm(A - A_k, 'fro')

        return {
            'U': U,
            'singular_values': s,
            'Vt': Vt,
            'reconstruction_error': reconstruction_error
        }

    def low_rank_approximation(self, A, rank):
        """低秩近似"""
        U, s, Vt = np.linalg.svd(A, full_matrices=False)

        # 保留前rank个奇异值
        A_approx = U[:, :rank] @ np.diag(s[:rank]) @ Vt[:rank, :]

        # 计算压缩比和误差
        original_elements = A.shape[0] * A.shape[1]
        compressed_elements = rank * (A.shape[0] + A.shape[1])
        compression_ratio = compressed_elements / original_elements

        frobenius_error = np.linalg.norm(A - A_approx, 'fro')
        relative_error = frobenius_error / np.linalg.norm(A, 'fro')

        return {
            'approximation': A_approx,
            'compression_ratio': compression_ratio,
            'absolute_error': frobenius_error,
            'relative_error': relative_error
        }

# 实际应用示例：图像压缩
def image_compression_demo():
    """使用SVD进行图像压缩的演示"""
    # 创建一个模拟图像矩阵
    np.random.seed(42)
    image = np.random.rand(100, 100)

    # 添加一些结构化信息
    x, y = np.meshgrid(np.linspace(0, 1, 100), np.linspace(0, 1, 100))
    image += 2 * np.sin(5 * np.pi * x) * np.cos(5 * np.pi * y)

    decomposer = MatrixDecomposition()

    print("SVD图像压缩分析:")
    print(f"原始图像大小: {image.shape}")

    # 不同压缩级别的分析
    ranks = [5, 10, 20, 50]
    for rank in ranks:
        result = decomposer.low_rank_approximation(image, rank)
        print(f"\n秩-{rank} 近似:")
        print(f"  压缩比: {result['compression_ratio']:.3f}")
        print(f"  相对误差: {result['relative_error']:.6f}")
        print(f"  存储节省: {(1-result['compression_ratio'])*100:.1f}%")

image_compression_demo()

### 2.2.3 矩阵运算在神经网络中的应用

神经网络的前向传播和反向传播本质上是一系列矩阵运算。

```python
class NeuralNetworkLinearAlgebra:
    """神经网络中的线性代数操作"""

    def __init__(self):
        pass

    def batch_matrix_operations(self, X, W, b):
        """批量矩阵运算"""
        # X: (batch_size, input_dim)
        # W: (input_dim, output_dim)
        # b: (output_dim,)

        # 线性变换：Y = XW + b
        Y = np.dot(X, W) + b

        return Y

    def convolution_as_matrix_multiplication(self, input_tensor, kernel, stride=1, padding=0):
        """将卷积操作表示为矩阵乘法"""
        # 这是理论演示，实际实现会更复杂
        batch_size, in_channels, in_height, in_width = input_tensor.shape
        out_channels, in_channels, kernel_height, kernel_width = kernel.shape

        # 计算输出尺寸
        out_height = (in_height + 2*padding - kernel_height) // stride + 1
        out_width = (in_width + 2*padding - kernel_width) // stride + 1

        # 创建Toeplitz矩阵（简化版本）
        # 实际实现需要考虑更多细节
        print(f"卷积操作转换为矩阵乘法:")
        print(f"输入形状: {input_tensor.shape}")
        print(f"卷积核形状: {kernel.shape}")
        print(f"输出形状: ({batch_size}, {out_channels}, {out_height}, {out_width})")

        return np.random.rand(batch_size, out_channels, out_height, out_width)

    def attention_mechanism_matrix_form(self, Q, K, V):
        """注意力机制的矩阵形式"""
        # Q: (seq_len, d_model) - Query矩阵
        # K: (seq_len, d_model) - Key矩阵
        # V: (seq_len, d_model) - Value矩阵

        d_k = K.shape[-1]

        # 计算注意力分数
        scores = np.dot(Q, K.T) / np.sqrt(d_k)

        # Softmax归一化
        attention_weights = self.softmax(scores)

        # 加权求和
        output = np.dot(attention_weights, V)

        return output, attention_weights

    def softmax(self, x):
        """数值稳定的Softmax实现"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

    def multi_head_attention(self, Q, K, V, num_heads=8):
        """多头注意力机制"""
        seq_len, d_model = Q.shape
        d_k = d_model // num_heads

        # 分割成多个头
        Q_heads = Q.reshape(seq_len, num_heads, d_k).transpose(1, 0, 2)
        K_heads = K.reshape(seq_len, num_heads, d_k).transpose(1, 0, 2)
        V_heads = V.reshape(seq_len, num_heads, d_k).transpose(1, 0, 2)

        # 对每个头计算注意力
        head_outputs = []
        attention_weights_all = []

        for i in range(num_heads):
            head_output, head_weights = self.attention_mechanism_matrix_form(
                Q_heads[i], K_heads[i], V_heads[i]
            )
            head_outputs.append(head_output)
            attention_weights_all.append(head_weights)

        # 拼接所有头的输出
        concatenated = np.concatenate(head_outputs, axis=-1)

        return concatenated, attention_weights_all

# 演示注意力机制
def attention_demo():
    """注意力机制的线性代数演示"""
    seq_len, d_model = 10, 64

    # 创建随机的Q, K, V矩阵
    np.random.seed(42)
    Q = np.random.randn(seq_len, d_model)
    K = np.random.randn(seq_len, d_model)
    V = np.random.randn(seq_len, d_model)

    nn_la = NeuralNetworkLinearAlgebra()

    # 单头注意力
    output, weights = nn_la.attention_mechanism_matrix_form(Q, K, V)
    print("单头注意力分析:")
    print(f"输入序列长度: {seq_len}")
    print(f"模型维度: {d_model}")
    print(f"注意力权重矩阵形状: {weights.shape}")
    print(f"输出形状: {output.shape}")

    # 多头注意力
    multi_output, multi_weights = nn_la.multi_head_attention(Q, K, V, num_heads=8)
    print(f"\n多头注意力输出形状: {multi_output.shape}")
    print(f"注意力头数: {len(multi_weights)}")

attention_demo()

## 2.3 概率统计基础

### 2.3.1 概率论在机器学习中的核心作用

概率论为机器学习提供了处理不确定性的数学框架。从贝叶斯推理到概率图模型，概率论无处不在。

```mermaid
graph TD
    A[概率论基础] --> B[贝叶斯定理]
    A --> C[概率分布]
    A --> D[随机变量]

    B --> E[贝叶斯推理]
    B --> F[朴素贝叶斯分类]

    C --> G[高斯分布]
    C --> H[伯努利分布]
    C --> I[多项式分布]

    D --> J[期望与方差]
    D --> K[协方差矩阵]

    E --> L[后验概率]
    F --> M[文本分类]
    G --> N[高斯混合模型]
    H --> O[逻辑回归]
```

#### 贝叶斯定理的深度应用

```python
class BayesianInference:
    """贝叶斯推理的完整实现"""

    def __init__(self):
        self.prior = {}
        self.likelihood = {}
        self.evidence = 0

    def naive_bayes_classifier(self, X_train, y_train, X_test):
        """朴素贝叶斯分类器的详细实现"""
        classes = np.unique(y_train)
        n_samples, n_features = X_train.shape

        # 计算先验概率 P(C)
        class_priors = {}
        for c in classes:
            class_priors[c] = np.sum(y_train == c) / len(y_train)

        # 计算似然概率 P(X|C) - 假设特征服从高斯分布
        feature_stats = {}
        for c in classes:
            class_data = X_train[y_train == c]
            feature_stats[c] = {
                'mean': np.mean(class_data, axis=0),
                'std': np.std(class_data, axis=0) + 1e-9  # 避免除零
            }

        # 对测试数据进行预测
        predictions = []
        prediction_probs = []

        for x in X_test:
            class_scores = {}

            for c in classes:
                # 计算 P(C|X) ∝ P(X|C) * P(C)
                # 使用对数概率避免数值下溢
                log_prior = np.log(class_priors[c])

                # 计算高斯似然的对数
                mean = feature_stats[c]['mean']
                std = feature_stats[c]['std']
                log_likelihood = -0.5 * np.sum(np.log(2 * np.pi * std**2))
                log_likelihood -= 0.5 * np.sum((x - mean)**2 / std**2)

                class_scores[c] = log_prior + log_likelihood

            # 选择得分最高的类别
            predicted_class = max(class_scores, key=class_scores.get)
            predictions.append(predicted_class)

            # 计算概率（softmax归一化）
            scores = np.array(list(class_scores.values()))
            probs = np.exp(scores - np.max(scores))
            probs = probs / np.sum(probs)
            prediction_probs.append(dict(zip(classes, probs)))

        return predictions, prediction_probs

    def bayesian_linear_regression(self, X, y, alpha=1.0, beta=1.0):
        """贝叶斯线性回归"""
        # 先验：w ~ N(0, α^(-1)I)
        # 似然：y|X,w ~ N(Xw, β^(-1)I)
        # 后验：w|X,y ~ N(μ_N, Σ_N)

        # 计算后验参数
        S_N_inv = alpha * np.eye(X.shape[1]) + beta * X.T @ X
        S_N = np.linalg.inv(S_N_inv)
        mu_N = beta * S_N @ X.T @ y

        return {
            'posterior_mean': mu_N,
            'posterior_covariance': S_N,
            'precision_matrix': S_N_inv
        }

    def predictive_distribution(self, X_new, posterior_params, beta=1.0):
        """预测分布"""
        mu_N = posterior_params['posterior_mean']
        S_N = posterior_params['posterior_covariance']

        # 预测均值
        y_pred_mean = X_new @ mu_N

        # 预测方差
        y_pred_var = 1/beta + np.sum((X_new @ S_N) * X_new, axis=1)

        return y_pred_mean, y_pred_var

# 贝叶斯推理演示
def bayesian_demo():
    """贝叶斯方法演示"""
    # 生成合成数据
    np.random.seed(42)
    n_samples = 100
    X = np.random.randn(n_samples, 2)
    true_weights = np.array([1.5, -2.0])
    y = X @ true_weights + 0.3 * np.random.randn(n_samples)

    # 添加偏置项
    X_with_bias = np.column_stack([np.ones(n_samples), X])
    true_weights_with_bias = np.array([0.0, 1.5, -2.0])

    bayes = BayesianInference()

    # 贝叶斯线性回归
    posterior = bayes.bayesian_linear_regression(X_with_bias, y, alpha=1.0, beta=25.0)

    print("贝叶斯线性回归结果:")
    print(f"真实权重: {true_weights_with_bias}")
    print(f"后验均值: {posterior['posterior_mean']}")
    print(f"后验标准差: {np.sqrt(np.diag(posterior['posterior_covariance']))}")

    # 预测新数据点
    X_new = np.array([[1, 0.5, -0.3], [1, -0.2, 0.8]])
    pred_mean, pred_var = bayes.predictive_distribution(X_new, posterior, beta=25.0)

    print(f"\n预测结果:")
    for i, (mean, var) in enumerate(zip(pred_mean, pred_var)):
        print(f"样本 {i+1}: 均值={mean:.3f}, 标准差={np.sqrt(var):.3f}")

bayesian_demo()

### 2.3.2 概率分布族与参数估计

理解不同概率分布的性质对于选择合适的模型至关重要。

```python
class ProbabilityDistributions:
    """概率分布分析工具"""

    def __init__(self):
        self.distributions = {}

    def gaussian_distribution(self, x, mu=0, sigma=1):
        """高斯分布"""
        return (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma)**2)

    def bernoulli_distribution(self, x, p):
        """伯努利分布"""
        return p**x * (1-p)**(1-x)

    def multinomial_distribution(self, x, n, p):
        """多项式分布"""
        from math import factorial
        coeff = factorial(n) / np.prod([factorial(xi) for xi in x])
        prob = np.prod([pi**xi for xi, pi in zip(x, p)])
        return coeff * prob

    def maximum_likelihood_estimation(self, data, distribution_type):
        """最大似然估计"""
        if distribution_type == 'gaussian':
            mu_mle = np.mean(data)
            sigma_mle = np.std(data, ddof=0)  # 使用MLE的无偏估计
            return {'mu': mu_mle, 'sigma': sigma_mle}

        elif distribution_type == 'bernoulli':
            p_mle = np.mean(data)
            return {'p': p_mle}

        elif distribution_type == 'multinomial':
            p_mle = np.mean(data, axis=0)
            p_mle = p_mle / np.sum(p_mle)  # 归一化
            return {'p': p_mle}

    def expectation_maximization_gmm(self, X, n_components=2, max_iter=100, tol=1e-6):
        """高斯混合模型的EM算法"""
        n_samples, n_features = X.shape

        # 初始化参数
        weights = np.ones(n_components) / n_components
        means = X[np.random.choice(n_samples, n_components, replace=False)]
        covariances = [np.eye(n_features) for _ in range(n_components)]

        log_likelihood_history = []

        for iteration in range(max_iter):
            # E步：计算后验概率
            responsibilities = np.zeros((n_samples, n_components))

            for k in range(n_components):
                # 计算每个高斯分量的概率密度
                diff = X - means[k]
                inv_cov = np.linalg.inv(covariances[k])
                det_cov = np.linalg.det(covariances[k])

                # 多元高斯概率密度函数
                normalization = 1 / np.sqrt((2*np.pi)**n_features * det_cov)
                exponent = -0.5 * np.sum((diff @ inv_cov) * diff, axis=1)
                responsibilities[:, k] = weights[k] * normalization * np.exp(exponent)

            # 归一化责任度
            responsibilities = responsibilities / np.sum(responsibilities, axis=1, keepdims=True)

            # M步：更新参数
            N_k = np.sum(responsibilities, axis=0)

            # 更新权重
            weights = N_k / n_samples

            # 更新均值
            for k in range(n_components):
                means[k] = np.sum(responsibilities[:, k:k+1] * X, axis=0) / N_k[k]

            # 更新协方差矩阵
            for k in range(n_components):
                diff = X - means[k]
                weighted_diff = responsibilities[:, k:k+1] * diff
                covariances[k] = (weighted_diff.T @ diff) / N_k[k]
                # 添加正则化项避免奇异矩阵
                covariances[k] += 1e-6 * np.eye(n_features)

            # 计算对数似然
            log_likelihood = 0
            for i in range(n_samples):
                sample_likelihood = 0
                for k in range(n_components):
                    diff = X[i] - means[k]
                    inv_cov = np.linalg.inv(covariances[k])
                    det_cov = np.linalg.det(covariances[k])

                    normalization = 1 / np.sqrt((2*np.pi)**n_features * det_cov)
                    exponent = -0.5 * (diff @ inv_cov @ diff.T)
                    sample_likelihood += weights[k] * normalization * np.exp(exponent)

                log_likelihood += np.log(sample_likelihood)

            log_likelihood_history.append(log_likelihood)

            # 检查收敛
            if iteration > 0 and abs(log_likelihood_history[-1] - log_likelihood_history[-2]) < tol:
                print(f"EM算法在第{iteration+1}次迭代后收敛")
                break

        return {
            'weights': weights,
            'means': means,
            'covariances': covariances,
            'responsibilities': responsibilities,
            'log_likelihood_history': log_likelihood_history
        }

# 概率分布演示
def probability_distributions_demo():
    """概率分布和参数估计演示"""
    np.random.seed(42)

    # 生成混合高斯数据
    n_samples = 300

    # 第一个高斯分量
    X1 = np.random.multivariate_normal([2, 2], [[1, 0.5], [0.5, 1]], n_samples//2)
    # 第二个高斯分量
    X2 = np.random.multivariate_normal([-1, -1], [[1, -0.3], [-0.3, 1]], n_samples//2)

    X = np.vstack([X1, X2])

    # 使用EM算法拟合高斯混合模型
    prob_dist = ProbabilityDistributions()
    gmm_result = prob_dist.expectation_maximization_gmm(X, n_components=2, max_iter=50)

    print("高斯混合模型EM算法结果:")
    print(f"混合权重: {gmm_result['weights']}")
    print(f"均值1: {gmm_result['means'][0]}")
    print(f"均值2: {gmm_result['means'][1]}")
    print(f"最终对数似然: {gmm_result['log_likelihood_history'][-1]:.2f}")

    # 单变量高斯分布的MLE估计
    gaussian_data = np.random.normal(3, 2, 1000)
    gaussian_params = prob_dist.maximum_likelihood_estimation(gaussian_data, 'gaussian')
    print(f"\n高斯分布MLE估计:")
    print(f"真实参数: μ=3, σ=2")
    print(f"估计参数: μ={gaussian_params['mu']:.3f}, σ={gaussian_params['sigma']:.3f}")

probability_distributions_demo()

### 2.3.3 统计学习理论基础

统计学习理论为机器学习提供了理论保证，帮助我们理解泛化能力和样本复杂度。

```python
class StatisticalLearningTheory:
    """统计学习理论分析工具"""

    def __init__(self):
        pass

    def pac_learning_bound(self, m, delta, h):
        """PAC学习界限"""
        # Hoeffding不等式：P(|R(h) - R_emp(h)| > ε) ≤ 2exp(-2mε²)
        # 设置右边等于δ，解出ε
        epsilon = np.sqrt(np.log(2/delta) / (2*m))
        return epsilon

    def rademacher_complexity_bound(self, m, R_m, delta):
        """Rademacher复杂度界限"""
        # 泛化界限：R(h) ≤ R_emp(h) + 2R_m + sqrt(log(1/δ)/(2m))
        confidence_term = np.sqrt(np.log(1/delta) / (2*m))
        generalization_bound = 2*R_m + confidence_term
        return generalization_bound

    def vc_dimension_analysis(self, hypothesis_class):
        """VC维分析"""
        # 这里提供一些经典假设类的VC维
        vc_dimensions = {
            'linear_classifier_2d': 3,  # 2D线性分类器的VC维是3
            'linear_classifier_d': lambda d: d + 1,  # d维线性分类器的VC维是d+1
            'polynomial_degree_k': lambda d, k: sum([self.binomial_coefficient(d+i-1, i) for i in range(k+1)]),
            'neural_network': 'depends_on_architecture'  # 神经网络的VC维很复杂
        }

        return vc_dimensions.get(hypothesis_class, "Unknown")

    def binomial_coefficient(self, n, k):
        """二项式系数"""
        if k > n or k < 0:
            return 0
        if k == 0 or k == n:
            return 1

        result = 1
        for i in range(min(k, n-k)):
            result = result * (n - i) // (i + 1)
        return result

    def bias_variance_decomposition(self, true_function, predictions_ensemble, x_test):
        """偏差-方差分解"""
        # predictions_ensemble: 多个模型在测试点的预测结果
        # true_function: 真实函数在测试点的值

        # 计算期望预测（多个模型的平均）
        expected_prediction = np.mean(predictions_ensemble, axis=0)

        # 偏差²：期望预测与真实值的差异
        bias_squared = (expected_prediction - true_function)**2

        # 方差：单个预测与期望预测的差异
        variance = np.mean((predictions_ensemble - expected_prediction)**2, axis=0)

        # 噪声：通常假设为常数
        noise = 0  # 在这个简化版本中假设无噪声

        # 总误差 = 偏差² + 方差 + 噪声
        total_error = bias_squared + variance + noise

        return {
            'bias_squared': bias_squared,
            'variance': variance,
            'noise': noise,
            'total_error': total_error,
            'expected_prediction': expected_prediction
        }

    def learning_curve_analysis(self, train_sizes, train_scores, val_scores):
        """学习曲线分析"""
        analysis = {}

        # 检查过拟合
        final_gap = train_scores[-1] - val_scores[-1]
        if final_gap > 0.1:
            analysis['overfitting'] = 'High'
        elif final_gap > 0.05:
            analysis['overfitting'] = 'Moderate'
        else:
            analysis['overfitting'] = 'Low'

        # 检查欠拟合
        if val_scores[-1] < 0.7:  # 假设满分是1.0
            analysis['underfitting'] = 'Possible'
        else:
            analysis['underfitting'] = 'Unlikely'

        # 收敛性分析
        val_improvement = val_scores[-1] - val_scores[len(val_scores)//2]
        if val_improvement < 0.01:
            analysis['convergence'] = 'Converged'
        else:
            analysis['convergence'] = 'Still improving'

        return analysis

# 统计学习理论演示
def statistical_learning_demo():
    """统计学习理论演示"""
    slt = StatisticalLearningTheory()

    # PAC学习界限分析
    sample_sizes = [100, 500, 1000, 5000, 10000]
    delta = 0.05  # 95%置信度

    print("PAC学习界限分析 (δ=0.05):")
    for m in sample_sizes:
        epsilon = slt.pac_learning_bound(m, delta, None)
        print(f"样本数 {m:5d}: 泛化误差界限 ε ≤ {epsilon:.4f}")

    # 偏差-方差分解演示
    print("\n偏差-方差分解演示:")

    # 模拟真实函数
    x_test = np.linspace(0, 1, 100)
    true_function = np.sin(2 * np.pi * x_test)

    # 模拟多个模型的预测（添加不同程度的偏差和方差）
    n_models = 50
    predictions = []

    for i in range(n_models):
        # 添加偏差（系统性误差）
        bias_error = 0.1 * x_test
        # 添加方差（随机误差）
        variance_error = 0.2 * np.random.randn(len(x_test))

        prediction = true_function + bias_error + variance_error
        predictions.append(prediction)

    predictions_ensemble = np.array(predictions)

    # 计算偏差-方差分解
    decomposition = slt.bias_variance_decomposition(true_function, predictions_ensemble, x_test)

    print(f"平均偏差²: {np.mean(decomposition['bias_squared']):.4f}")
    print(f"平均方差: {np.mean(decomposition['variance']):.4f}")
    print(f"平均总误差: {np.mean(decomposition['total_error']):.4f}")

    # VC维分析
    print(f"\nVC维分析:")
    print(f"2D线性分类器的VC维: {slt.vc_dimension_analysis('linear_classifier_2d')}")
    print(f"d维线性分类器的VC维: d+1")

statistical_learning_demo()

## 2.4 最优化理论

### 2.4.1 凸优化基础

凸优化是机器学习中最重要的数学工具之一，许多机器学习问题都可以转化为凸优化问题。

```python
class ConvexOptimization:
    """凸优化理论与算法"""

    def __init__(self):
        self.tolerance = 1e-8

    def is_convex_function(self, f, domain_points, epsilon=1e-6):
        """检查函数是否为凸函数（数值方法）"""
        # 对于凸函数，任意两点间的线性组合应该满足：
        # f(λx + (1-λ)y) ≤ λf(x) + (1-λ)f(y)

        n_tests = 100
        for _ in range(n_tests):
            # 随机选择两个点
            x = domain_points[np.random.randint(len(domain_points))]
            y = domain_points[np.random.randint(len(domain_points))]

            # 随机选择λ
            lambda_val = np.random.random()

            # 计算线性组合点
            z = lambda_val * x + (1 - lambda_val) * y

            # 检查凸性条件
            lhs = f(z)
            rhs = lambda_val * f(x) + (1 - lambda_val) * f(y)

            if lhs > rhs + epsilon:
                return False

        return True

    def gradient_descent_with_line_search(self, f, grad_f, x0, max_iter=1000):
        """带线搜索的梯度下降"""
        x = x0.copy()
        path = [x.copy()]

        for i in range(max_iter):
            grad = grad_f(x)

            if np.linalg.norm(grad) < self.tolerance:
                break

            # Armijo线搜索
            alpha = self.armijo_line_search(f, grad_f, x, -grad)

            x = x - alpha * grad
            path.append(x.copy())

        return x, np.array(path)

    def armijo_line_search(self, f, grad_f, x, direction, c1=1e-4, rho=0.5, max_iter=50):
        """Armijo线搜索"""
        alpha = 1.0
        fx = f(x)
        grad_x = grad_f(x)
        expected_decrease = c1 * np.dot(grad_x, direction)

        for _ in range(max_iter):
            if f(x + alpha * direction) <= fx + alpha * expected_decrease:
                return alpha
            alpha *= rho

        return alpha

    def proximal_gradient_method(self, f, grad_f, prox_g, x0, max_iter=1000, step_size=0.01):
        """近端梯度方法（用于非光滑优化）"""
        x = x0.copy()
        path = [x.copy()]

        for i in range(max_iter):
            # 梯度步
            y = x - step_size * grad_f(x)

            # 近端算子步
            x_new = prox_g(y, step_size)

            if np.linalg.norm(x_new - x) < self.tolerance:
                break

            x = x_new
            path.append(x.copy())

        return x, np.array(path)

    def lasso_proximal_operator(self, x, lambda_reg):
        """Lasso回归的近端算子（软阈值函数）"""
        return np.sign(x) * np.maximum(np.abs(x) - lambda_reg, 0)

# 凸优化演示
def convex_optimization_demo():
    """凸优化方法演示"""

    # 定义一个凸函数：f(x) = x₁² + 2x₂² + x₁x₂
    def convex_function(x):
        return x[0]**2 + 2*x[1]**2 + x[0]*x[1]

    def convex_gradient(x):
        return np.array([2*x[0] + x[1], 4*x[1] + x[0]])

    optimizer = ConvexOptimization()

    # 测试函数凸性
    test_points = [np.random.randn(2) for _ in range(100)]
    is_convex = optimizer.is_convex_function(convex_function, test_points)
    print(f"函数凸性检测: {'凸函数' if is_convex else '非凸函数'}")

    # 梯度下降优化
    x0 = np.array([3.0, 2.0])
    x_opt, path = optimizer.gradient_descent_with_line_search(
        convex_function, convex_gradient, x0
    )

    print(f"\n梯度下降结果:")
    print(f"初始点: {x0}")
    print(f"最优点: {x_opt}")
    print(f"函数值: {convex_function(x0):.6f} → {convex_function(x_opt):.6f}")
    print(f"迭代次数: {len(path)}")

    # Lasso回归演示（使用近端梯度方法）
    print(f"\nLasso回归近端算子演示:")
    x_test = np.array([2.0, -1.5, 0.3, -0.8])
    lambda_vals = [0.1, 0.5, 1.0, 2.0]

    for lam in lambda_vals:
        x_prox = optimizer.lasso_proximal_operator(x_test, lam)
        print(f"λ={lam}: {x_test} → {x_prox}")

convex_optimization_demo()

## 2.5 信息论基础

### 2.5.1 熵、互信息与KL散度

信息论为机器学习提供了量化信息和不确定性的数学工具。

```python
class InformationTheory:
    """信息论分析工具"""

    def __init__(self):
        self.epsilon = 1e-12  # 避免log(0)

    def entropy(self, probabilities):
        """计算熵 H(X) = -Σ p(x) log p(x)"""
        p = np.array(probabilities)
        p = p[p > 0]  # 移除零概率项
        return -np.sum(p * np.log2(p + self.epsilon))

    def conditional_entropy(self, joint_prob):
        """条件熵 H(Y|X) = H(X,Y) - H(X)"""
        # joint_prob: 联合概率分布矩阵
        joint_prob = np.array(joint_prob)

        # 计算边缘概率
        p_x = np.sum(joint_prob, axis=1)
        p_y = np.sum(joint_prob, axis=0)

        # 计算联合熵
        joint_entropy = self.entropy(joint_prob.flatten())

        # 计算边缘熵
        entropy_x = self.entropy(p_x)

        # 条件熵 = 联合熵 - 边缘熵
        return joint_entropy - entropy_x

    def mutual_information(self, joint_prob):
        """互信息 I(X;Y) = H(X) + H(Y) - H(X,Y)"""
        joint_prob = np.array(joint_prob)

        # 计算边缘概率
        p_x = np.sum(joint_prob, axis=1)
        p_y = np.sum(joint_prob, axis=0)

        # 计算各种熵
        entropy_x = self.entropy(p_x)
        entropy_y = self.entropy(p_y)
        joint_entropy = self.entropy(joint_prob.flatten())

        return entropy_x + entropy_y - joint_entropy

    def kl_divergence(self, p, q):
        """KL散度 D_KL(P||Q) = Σ p(x) log(p(x)/q(x))"""
        p = np.array(p) + self.epsilon
        q = np.array(q) + self.epsilon

        return np.sum(p * np.log(p / q))

    def js_divergence(self, p, q):
        """JS散度（对称的KL散度）"""
        p = np.array(p)
        q = np.array(q)
        m = 0.5 * (p + q)

        return 0.5 * self.kl_divergence(p, m) + 0.5 * self.kl_divergence(q, m)

    def information_gain(self, parent_labels, split_labels):
        """信息增益（决策树中使用）"""
        # 计算分割前的熵
        parent_entropy = self.entropy(np.bincount(parent_labels) / len(parent_labels))

        # 计算分割后的加权熵
        unique_splits = np.unique(split_labels)
        weighted_entropy = 0

        for split_val in unique_splits:
            mask = split_labels == split_val
            subset_labels = parent_labels[mask]
            weight = len(subset_labels) / len(parent_labels)

            if len(subset_labels) > 0:
                subset_entropy = self.entropy(np.bincount(subset_labels) / len(subset_labels))
                weighted_entropy += weight * subset_entropy

        return parent_entropy - weighted_entropy

    def cross_entropy_loss(self, y_true, y_pred):
        """交叉熵损失函数"""
        y_pred = np.clip(y_pred, self.epsilon, 1 - self.epsilon)
        return -np.mean(y_true * np.log(y_pred))

# 信息论演示
def information_theory_demo():
    """信息论概念演示"""
    info_theory = InformationTheory()

    # 熵的计算
    print("熵的计算示例:")

    # 均匀分布（最大熵）
    uniform_dist = [0.25, 0.25, 0.25, 0.25]
    uniform_entropy = info_theory.entropy(uniform_dist)
    print(f"均匀分布熵: {uniform_entropy:.4f} bits")

    # 偏斜分布（较小熵）
    skewed_dist = [0.8, 0.1, 0.05, 0.05]
    skewed_entropy = info_theory.entropy(skewed_dist)
    print(f"偏斜分布熵: {skewed_entropy:.4f} bits")

    # 互信息计算
    print(f"\n互信息计算:")
    # 联合概率分布矩阵
    joint_prob = np.array([
        [0.1, 0.2, 0.1],
        [0.1, 0.3, 0.2]
    ])

    mi = info_theory.mutual_information(joint_prob)
    print(f"互信息 I(X;Y): {mi:.4f} bits")

    # KL散度计算
    print(f"\nKL散度计算:")
    p = [0.5, 0.3, 0.2]
    q = [0.4, 0.4, 0.2]

    kl_pq = info_theory.kl_divergence(p, q)
    kl_qp = info_theory.kl_divergence(q, p)
    js_div = info_theory.js_divergence(p, q)

    print(f"KL(P||Q): {kl_pq:.4f}")
    print(f"KL(Q||P): {kl_qp:.4f}")
    print(f"JS散度: {js_div:.4f}")

    # 决策树信息增益
    print(f"\n决策树信息增益:")
    parent_labels = np.array([0, 0, 1, 1, 1, 0, 1, 0, 1, 1])
    split_labels = np.array([0, 0, 0, 1, 1, 1, 1, 0, 1, 1])

    ig = info_theory.information_gain(parent_labels, split_labels)
    print(f"信息增益: {ig:.4f} bits")

information_theory_demo()

## 2.6 图论基础

### 2.6.1 图神经网络的数学基础

图论为处理非欧几里得数据提供了数学框架，是图神经网络的理论基础。

```python
class GraphTheory:
    """图论分析工具"""

    def __init__(self):
        pass

    def adjacency_matrix_analysis(self, adj_matrix):
        """邻接矩阵分析"""
        A = np.array(adj_matrix)
        n_nodes = A.shape[0]

        # 计算度矩阵
        degrees = np.sum(A, axis=1)
        D = np.diag(degrees)

        # 计算拉普拉斯矩阵
        L = D - A

        # 归一化拉普拉斯矩阵
        D_sqrt_inv = np.diag(1.0 / np.sqrt(degrees + 1e-12))
        L_norm = D_sqrt_inv @ L @ D_sqrt_inv

        # 计算特征值和特征向量
        eigenvalues, eigenvectors = np.linalg.eigh(L_norm)

        return {
            'adjacency_matrix': A,
            'degree_matrix': D,
            'laplacian_matrix': L,
            'normalized_laplacian': L_norm,
            'eigenvalues': eigenvalues,
            'eigenvectors': eigenvectors,
            'degrees': degrees
        }

    def graph_convolution_operation(self, X, A, W):
        """图卷积操作"""
        # X: 节点特征矩阵 (n_nodes, n_features)
        # A: 邻接矩阵 (n_nodes, n_nodes)
        # W: 权重矩阵 (n_features, n_output_features)

        # 添加自环
        A_tilde = A + np.eye(A.shape[0])

        # 计算度矩阵
        D_tilde = np.diag(np.sum(A_tilde, axis=1))

        # 对称归一化
        D_tilde_sqrt_inv = np.diag(1.0 / np.sqrt(np.diag(D_tilde) + 1e-12))
        A_norm = D_tilde_sqrt_inv @ A_tilde @ D_tilde_sqrt_inv

        # 图卷积：H = σ(A_norm * X * W)
        H = A_norm @ X @ W

        return H

    def pagerank_algorithm(self, adj_matrix, damping_factor=0.85, max_iter=100, tol=1e-6):
        """PageRank算法"""
        A = np.array(adj_matrix, dtype=float)
        n = A.shape[0]

        # 转换为转移概率矩阵
        # 处理悬挂节点（出度为0的节点）
        out_degrees = np.sum(A, axis=1)
        out_degrees[out_degrees == 0] = 1  # 避免除零

        # 转移概率矩阵
        P = A / out_degrees[:, np.newaxis]

        # 初始化PageRank值
        pr = np.ones(n) / n

        for i in range(max_iter):
            pr_new = (1 - damping_factor) / n + damping_factor * (P.T @ pr)

            if np.linalg.norm(pr_new - pr) < tol:
                break

            pr = pr_new

        return pr

    def shortest_path_algorithms(self, adj_matrix):
        """最短路径算法"""
        A = np.array(adj_matrix)
        n = A.shape[0]

        # Floyd-Warshall算法
        dist = A.copy().astype(float)
        dist[dist == 0] = np.inf  # 无连接设为无穷大
        np.fill_diagonal(dist, 0)  # 自己到自己距离为0

        for k in range(n):
            for i in range(n):
                for j in range(n):
                    if dist[i][k] + dist[k][j] < dist[i][j]:
                        dist[i][j] = dist[i][k] + dist[k][j]

        return dist

    def graph_clustering_spectral(self, adj_matrix, n_clusters=2):
        """谱聚类算法"""
        graph_analysis = self.adjacency_matrix_analysis(adj_matrix)

        # 使用归一化拉普拉斯矩阵的特征向量
        eigenvalues = graph_analysis['eigenvalues']
        eigenvectors = graph_analysis['eigenvectors']

        # 选择前n_clusters个最小特征值对应的特征向量
        embedding = eigenvectors[:, :n_clusters]

        # 使用K-means聚类
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(embedding)

        return cluster_labels, embedding

# 图论演示
def graph_theory_demo():
    """图论概念演示"""

    # 创建一个示例图的邻接矩阵
    adj_matrix = np.array([
        [0, 1, 1, 0, 0],
        [1, 0, 1, 1, 0],
        [1, 1, 0, 1, 1],
        [0, 1, 1, 0, 1],
        [0, 0, 1, 1, 0]
    ])

    graph = GraphTheory()

    # 图分析
    analysis = graph.adjacency_matrix_analysis(adj_matrix)
    print("图结构分析:")
    print(f"节点数: {adj_matrix.shape[0]}")
    print(f"边数: {np.sum(adj_matrix) // 2}")
    print(f"节点度数: {analysis['degrees']}")
    print(f"拉普拉斯特征值: {analysis['eigenvalues']}")

    # PageRank计算
    pr_scores = graph.pagerank_algorithm(adj_matrix)
    print(f"\nPageRank分数:")
    for i, score in enumerate(pr_scores):
        print(f"节点 {i}: {score:.4f}")

    # 图卷积演示
    print(f"\n图卷积操作:")
    n_nodes, n_features = 5, 3
    X = np.random.randn(n_nodes, n_features)  # 节点特征
    W = np.random.randn(n_features, 2)        # 权重矩阵

    H = graph.graph_convolution_operation(X, adj_matrix, W)
    print(f"输入特征形状: {X.shape}")
    print(f"输出特征形状: {H.shape}")

    # 谱聚类
    cluster_labels, embedding = graph.graph_clustering_spectral(adj_matrix, n_clusters=2)
    print(f"\n谱聚类结果:")
    print(f"聚类标签: {cluster_labels}")

graph_theory_demo()

---

# 数学基础总结

通过以上详细的数学基础介绍，我们涵盖了AI/ML中最重要的数学工具：

## 🔧 核心数学工具箱

| 数学分支 | 核心概念 | AI/ML应用 | 关键算法 |
|---------|---------|----------|----------|
| **微积分** | 梯度、链式法则、泰勒展开 | 优化算法、反向传播 | 梯度下降、牛顿法、Adam |
| **线性代数** | 矩阵分解、特征值、SVD | 降维、神经网络、推荐系统 | PCA、SVD、矩阵分解 |
| **概率统计** | 贝叶斯定理、分布、MLE | 不确定性建模、分类 | 朴素贝叶斯、EM算法、GMM |
| **最优化** | 凸优化、约束优化、对偶 | 模型训练、参数估计 | 梯度下降、拉格朗日乘数法 |
| **信息论** | 熵、互信息、KL散度 | 特征选择、模型评估 | 决策树、VAE、GAN |
| **图论** | 拉普拉斯矩阵、谱分析 | 图神经网络、社交网络 | GCN、GraphSAGE、谱聚类 |

## 🎯 实际应用指导

1. **选择优化算法时**：
   - 凸问题 → 梯度下降、牛顿法
   - 非凸问题 → Adam、RMSprop
   - 约束问题 → 拉格朗日方法、KKT条件

2. **处理高维数据时**：
   - 线性降维 → PCA、ICA
   - 非线性降维 → t-SNE、UMAP
   - 特征选择 → 互信息、方差分析

3. **建模不确定性时**：
   - 参数不确定性 → 贝叶斯方法
   - 模型不确定性 → 集成学习
   - 数据不确定性 → 鲁棒优化

这些数学基础不仅是理解AI/ML算法的关键，更是在实际项目中做出正确技术决策的基础。掌握这些工具，您就能够：
- 理解算法的工作原理和适用条件
- 诊断模型问题并选择合适的解决方案
- 设计新的算法来解决特定问题
- 在工程实践中做出有理论支撑的决策

## 2.7 动态规划与最优化方法扩展

### 2.7.1 动态规划在机器学习中的应用

动态规划是解决具有重叠子问题和最优子结构性质问题的重要方法，在序列建模、强化学习和结构化预测中广泛应用。

```python
class DynamicProgramming:
    """动态规划算法集合"""

    def __init__(self):
        self.memo = {}

    def viterbi_algorithm(self, observations, states, start_prob, trans_prob, emit_prob):
        """
        维特比算法：寻找隐马尔可夫模型中最可能的状态序列

        参数说明：
        - observations: 观测序列，如['sunny', 'rainy', 'cloudy']
        - states: 隐状态集合，如['happy', 'sad']
        - start_prob: 初始状态概率，如{'happy': 0.6, 'sad': 0.4}
        - trans_prob: 状态转移概率矩阵
        - emit_prob: 发射概率矩阵

        算法思想：
        1. 初始化：计算第一个观测的各状态概率
        2. 递推：对每个时刻，计算到达各状态的最大概率路径
        3. 终止：找到最后时刻概率最大的状态
        4. 回溯：从终止状态回溯找到完整路径
        """
        n_obs = len(observations)
        n_states = len(states)

        # 初始化概率表和路径表
        # prob[t][s] 表示在时刻t处于状态s的最大概率
        prob = [{} for _ in range(n_obs)]
        # path[t][s] 表示在时刻t到达状态s的最优前驱状态
        path = [{} for _ in range(n_obs)]

        # 第一步：初始化
        for state in states:
            prob[0][state] = start_prob[state] * emit_prob[state][observations[0]]
            path[0][state] = None

        # 第二步：递推计算
        for t in range(1, n_obs):
            for curr_state in states:
                # 找到到达当前状态的最优前驱状态
                max_prob = 0
                best_prev_state = None

                for prev_state in states:
                    # 计算从prev_state转移到curr_state的概率
                    transition_prob = (prob[t-1][prev_state] *
                                     trans_prob[prev_state][curr_state] *
                                     emit_prob[curr_state][observations[t]])

                    if transition_prob > max_prob:
                        max_prob = transition_prob
                        best_prev_state = prev_state

                prob[t][curr_state] = max_prob
                path[t][curr_state] = best_prev_state

        # 第三步：找到最终最优状态
        max_final_prob = 0
        best_final_state = None
        for state in states:
            if prob[n_obs-1][state] > max_final_prob:
                max_final_prob = prob[n_obs-1][state]
                best_final_state = state

        # 第四步：回溯构建最优路径
        optimal_path = []
        current_state = best_final_state

        for t in range(n_obs-1, -1, -1):
            optimal_path.append(current_state)
            current_state = path[t][current_state]

        optimal_path.reverse()

        return optimal_path, max_final_prob

    def edit_distance_dp(self, str1, str2):
        """
        编辑距离动态规划算法（用于文本相似度计算）

        算法思想：
        dp[i][j] 表示将str1[0:i]转换为str2[0:j]的最小编辑距离

        状态转移方程：
        - 如果str1[i-1] == str2[j-1]: dp[i][j] = dp[i-1][j-1]
        - 否则: dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
          分别对应删除、插入、替换操作
        """
        m, n = len(str1), len(str2)

        # 初始化DP表
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        # 边界条件：空字符串到任意字符串的距离
        for i in range(m + 1):
            dp[i][0] = i  # 删除i个字符
        for j in range(n + 1):
            dp[0][j] = j  # 插入j个字符

        # 填充DP表
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i-1] == str2[j-1]:
                    dp[i][j] = dp[i-1][j-1]  # 字符相同，无需操作
                else:
                    dp[i][j] = 1 + min(
                        dp[i-1][j],    # 删除str1[i-1]
                        dp[i][j-1],    # 插入str2[j-1]
                        dp[i-1][j-1]   # 替换str1[i-1]为str2[j-1]
                    )

        return dp[m][n]

    def longest_common_subsequence(self, seq1, seq2):
        """
        最长公共子序列（LCS）算法

        应用场景：
        - 文本相似度计算
        - 生物信息学中的序列比对
        - 版本控制系统中的差异比较
        """
        m, n = len(seq1), len(seq2)

        # dp[i][j]表示seq1[0:i]和seq2[0:j]的LCS长度
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if seq1[i-1] == seq2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        # 回溯构建LCS
        lcs = []
        i, j = m, n
        while i > 0 and j > 0:
            if seq1[i-1] == seq2[j-1]:
                lcs.append(seq1[i-1])
                i -= 1
                j -= 1
            elif dp[i-1][j] > dp[i][j-1]:
                i -= 1
            else:
                j -= 1

        lcs.reverse()
        return dp[m][n], lcs

# 动态规划应用演示
def dynamic_programming_demo():
    """动态规划在机器学习中的应用演示"""
    dp = DynamicProgramming()

    print("=== 动态规划算法演示 ===")

    # 1. 维特比算法演示（天气预测）
    print("\n1. 维特比算法 - 隐马尔可夫模型状态推断")
    print("场景：根据观察到的活动推断天气状态")

    # 定义HMM参数
    observations = ['walk', 'shop', 'clean']  # 观测序列：散步、购物、清洁
    states = ['sunny', 'rainy']  # 隐状态：晴天、雨天

    # 初始状态概率
    start_prob = {'sunny': 0.6, 'rainy': 0.4}

    # 状态转移概率
    trans_prob = {
        'sunny': {'sunny': 0.7, 'rainy': 0.3},
        'rainy': {'sunny': 0.4, 'rainy': 0.6}
    }

    # 发射概率（状态->观测）
    emit_prob = {
        'sunny': {'walk': 0.6, 'shop': 0.3, 'clean': 0.1},
        'rainy': {'walk': 0.1, 'shop': 0.4, 'clean': 0.5}
    }

    optimal_path, probability = dp.viterbi_algorithm(
        observations, states, start_prob, trans_prob, emit_prob
    )

    print(f"观测序列: {observations}")
    print(f"最可能的天气序列: {optimal_path}")
    print(f"路径概率: {probability:.6f}")

    # 2. 编辑距离演示（文本相似度）
    print(f"\n2. 编辑距离算法 - 文本相似度计算")
    print("应用：拼写检查、模糊匹配、文本去重")

    text_pairs = [
        ("kitten", "sitting"),
        ("machine", "learning"),
        ("algorithm", "logarithm"),
        ("python", "java")
    ]

    for str1, str2 in text_pairs:
        distance = dp.edit_distance_dp(str1, str2)
        similarity = 1 - distance / max(len(str1), len(str2))
        print(f"'{str1}' vs '{str2}': 编辑距离={distance}, 相似度={similarity:.3f}")

    # 3. 最长公共子序列演示
    print(f"\n3. 最长公共子序列 - 序列比对")
    print("应用：代码抄袭检测、生物序列分析")

    seq1 = "ABCDGH"
    seq2 = "AEDFHR"
    lcs_length, lcs = dp.longest_common_subsequence(seq1, seq2)

    print(f"序列1: {seq1}")
    print(f"序列2: {seq2}")
    print(f"LCS长度: {lcs_length}")
    print(f"LCS序列: {''.join(lcs)}")

dynamic_programming_demo()

### 2.7.2 高级优化算法

除了基础的梯度下降，现代机器学习还依赖许多高级优化算法。

```python
class AdvancedOptimization:
    """高级优化算法集合"""

    def __init__(self):
        self.history = {}

    def simulated_annealing(self, objective_func, initial_solution, neighbor_func,
                           initial_temp=1000, cooling_rate=0.95, min_temp=1e-8, max_iter=10000):
        """
        模拟退火算法

        算法思想：
        模拟金属退火过程，在高温时允许接受较差解（跳出局部最优），
        随着温度降低，逐渐只接受更好的解。

        参数说明：
        - objective_func: 目标函数（最小化）
        - initial_solution: 初始解
        - neighbor_func: 邻域函数，生成当前解的邻居
        - initial_temp: 初始温度
        - cooling_rate: 冷却率
        """
        current_solution = initial_solution.copy()
        current_cost = objective_func(current_solution)

        best_solution = current_solution.copy()
        best_cost = current_cost

        temperature = initial_temp
        costs_history = [current_cost]

        for iteration in range(max_iter):
            if temperature < min_temp:
                break

            # 生成邻居解
            neighbor_solution = neighbor_func(current_solution)
            neighbor_cost = objective_func(neighbor_solution)

            # 计算接受概率
            if neighbor_cost < current_cost:
                # 更好的解，直接接受
                accept_prob = 1.0
            else:
                # 较差的解，按概率接受
                accept_prob = np.exp(-(neighbor_cost - current_cost) / temperature)

            # 决定是否接受新解
            if np.random.random() < accept_prob:
                current_solution = neighbor_solution
                current_cost = neighbor_cost

                # 更新最优解
                if current_cost < best_cost:
                    best_solution = current_solution.copy()
                    best_cost = current_cost

            # 降温
            temperature *= cooling_rate
            costs_history.append(current_cost)

        return {
            'best_solution': best_solution,
            'best_cost': best_cost,
            'costs_history': costs_history,
            'final_temperature': temperature
        }

    def genetic_algorithm(self, fitness_func, gene_length, population_size=100,
                         generations=500, mutation_rate=0.01, crossover_rate=0.8):
        """
        遗传算法

        算法思想：
        模拟生物进化过程，通过选择、交叉、变异操作，
        让种群逐代进化，寻找最优解。

        核心操作：
        1. 选择：根据适应度选择父代个体
        2. 交叉：父代个体交换基因产生子代
        3. 变异：随机改变个体的某些基因
        4. 替换：用子代替换部分父代个体
        """
        # 初始化种群
        population = [np.random.randint(0, 2, gene_length) for _ in range(population_size)]

        best_fitness_history = []
        avg_fitness_history = []

        for generation in range(generations):
            # 计算适应度
            fitness_scores = [fitness_func(individual) for individual in population]

            # 记录统计信息
            best_fitness = max(fitness_scores)
            avg_fitness = np.mean(fitness_scores)
            best_fitness_history.append(best_fitness)
            avg_fitness_history.append(avg_fitness)

            # 选择操作（轮盘赌选择）
            fitness_sum = sum(fitness_scores)
            if fitness_sum == 0:
                selection_probs = [1/population_size] * population_size
            else:
                selection_probs = [f/fitness_sum for f in fitness_scores]

            new_population = []

            for _ in range(population_size // 2):
                # 选择两个父代
                parent1_idx = np.random.choice(population_size, p=selection_probs)
                parent2_idx = np.random.choice(population_size, p=selection_probs)

                parent1 = population[parent1_idx].copy()
                parent2 = population[parent2_idx].copy()

                # 交叉操作
                if np.random.random() < crossover_rate:
                    crossover_point = np.random.randint(1, gene_length)
                    child1 = np.concatenate([parent1[:crossover_point], parent2[crossover_point:]])
                    child2 = np.concatenate([parent2[:crossover_point], parent1[crossover_point:]])
                else:
                    child1, child2 = parent1, parent2

                # 变异操作
                for child in [child1, child2]:
                    for i in range(gene_length):
                        if np.random.random() < mutation_rate:
                            child[i] = 1 - child[i]  # 翻转位

                new_population.extend([child1, child2])

            population = new_population

        # 找到最优个体
        final_fitness = [fitness_func(individual) for individual in population]
        best_idx = np.argmax(final_fitness)

        return {
            'best_individual': population[best_idx],
            'best_fitness': final_fitness[best_idx],
            'best_fitness_history': best_fitness_history,
            'avg_fitness_history': avg_fitness_history
        }

    def particle_swarm_optimization(self, objective_func, bounds, n_particles=30,
                                   max_iter=1000, w=0.5, c1=1.5, c2=1.5):
        """
        粒子群优化算法

        算法思想：
        模拟鸟群觅食行为，每个粒子代表一个候选解，
        粒子根据自身经验和群体经验调整飞行方向。

        更新公式：
        v[i] = w*v[i] + c1*r1*(pbest[i] - x[i]) + c2*r2*(gbest - x[i])
        x[i] = x[i] + v[i]

        其中：
        - v[i]: 粒子i的速度
        - x[i]: 粒子i的位置
        - pbest[i]: 粒子i的历史最优位置
        - gbest: 全局最优位置
        """
        n_dims = len(bounds)

        # 初始化粒子位置和速度
        particles = np.random.uniform(
            [b[0] for b in bounds],
            [b[1] for b in bounds],
            (n_particles, n_dims)
        )

        velocities = np.random.uniform(-1, 1, (n_particles, n_dims))

        # 初始化个体最优和全局最优
        pbest_positions = particles.copy()
        pbest_values = [objective_func(p) for p in particles]

        gbest_idx = np.argmin(pbest_values)
        gbest_position = pbest_positions[gbest_idx].copy()
        gbest_value = pbest_values[gbest_idx]

        gbest_history = [gbest_value]

        for iteration in range(max_iter):
            for i in range(n_particles):
                # 更新速度
                r1, r2 = np.random.random(n_dims), np.random.random(n_dims)

                velocities[i] = (w * velocities[i] +
                               c1 * r1 * (pbest_positions[i] - particles[i]) +
                               c2 * r2 * (gbest_position - particles[i]))

                # 更新位置
                particles[i] += velocities[i]

                # 边界处理
                for j in range(n_dims):
                    particles[i][j] = np.clip(particles[i][j], bounds[j][0], bounds[j][1])

                # 评估新位置
                current_value = objective_func(particles[i])

                # 更新个体最优
                if current_value < pbest_values[i]:
                    pbest_positions[i] = particles[i].copy()
                    pbest_values[i] = current_value

                    # 更新全局最优
                    if current_value < gbest_value:
                        gbest_position = particles[i].copy()
                        gbest_value = current_value

            gbest_history.append(gbest_value)

        return {
            'best_position': gbest_position,
            'best_value': gbest_value,
            'convergence_history': gbest_history
        }

# 高级优化算法演示
def advanced_optimization_demo():
    """高级优化算法演示"""
    optimizer = AdvancedOptimization()

    print("=== 高级优化算法演示 ===")

    # 1. 模拟退火算法 - 旅行商问题（简化版）
    print("\n1. 模拟退火算法 - 组合优化问题")

    def tsp_objective(route):
        """旅行商问题目标函数（简化版）"""
        # 假设城市坐标
        cities = [(0,0), (1,2), (3,1), (5,3), (4,4), (2,5)]
        total_distance = 0

        for i in range(len(route)):
            city1 = cities[route[i]]
            city2 = cities[route[(i+1) % len(route)]]
            distance = np.sqrt((city1[0] - city2[0])**2 + (city1[1] - city2[1])**2)
            total_distance += distance

        return total_distance

    def tsp_neighbor(route):
        """生成邻居解：随机交换两个城市"""
        new_route = route.copy()
        i, j = np.random.choice(len(route), 2, replace=False)
        new_route[i], new_route[j] = new_route[j], new_route[i]
        return new_route

    initial_route = list(range(6))  # 6个城市
    sa_result = optimizer.simulated_annealing(
        tsp_objective, initial_route, tsp_neighbor,
        initial_temp=100, cooling_rate=0.95, max_iter=5000
    )

    print(f"初始路线: {initial_route}, 距离: {tsp_objective(initial_route):.3f}")
    print(f"最优路线: {sa_result['best_solution']}, 距离: {sa_result['best_cost']:.3f}")

    # 2. 遗传算法 - 背包问题
    print(f"\n2. 遗传算法 - 0-1背包问题")

    # 物品：(重量, 价值)
    items = [(10, 60), (20, 100), (30, 120), (40, 160), (50, 200)]
    capacity = 100

    def knapsack_fitness(individual):
        """背包问题适应度函数"""
        total_weight = sum(items[i][0] for i in range(len(individual)) if individual[i])
        total_value = sum(items[i][1] for i in range(len(individual)) if individual[i])

        # 超重则适应度为0
        if total_weight > capacity:
            return 0
        return total_value

    ga_result = optimizer.genetic_algorithm(
        knapsack_fitness, len(items), population_size=50,
        generations=100, mutation_rate=0.1
    )

    selected_items = [i for i in range(len(items)) if ga_result['best_individual'][i]]
    total_weight = sum(items[i][0] for i in selected_items)
    total_value = ga_result['best_fitness']

    print(f"选中物品索引: {selected_items}")
    print(f"总重量: {total_weight}, 总价值: {total_value}")

    # 3. 粒子群优化 - 函数优化
    print(f"\n3. 粒子群优化 - 连续函数优化")

    def rastrigin_function(x):
        """Rastrigin函数：多峰函数，测试全局优化能力"""
        A = 10
        n = len(x)
        return A * n + sum(xi**2 - A * np.cos(2 * np.pi * xi) for xi in x)

    bounds = [(-5.12, 5.12)] * 2  # 2维优化问题
    pso_result = optimizer.particle_swarm_optimization(
        rastrigin_function, bounds, n_particles=30, max_iter=500
    )

    print(f"最优位置: {pso_result['best_position']}")
    print(f"最优值: {pso_result['best_value']:.6f}")
    print(f"理论最优值: 0 (在原点处)")

advanced_optimization_demo()
```

# 第三章：监督学习完整体系

> **核心理念**: 监督学习是机器学习的基石，通过标注数据学习输入到输出的映射关系。掌握监督学习的理论和实践是成为AI专家的必经之路。

## 3.1 监督学习理论框架

### 3.1.1 监督学习的数学定义

监督学习的目标是从训练数据 D = {(x₁,y₁), (x₂,y₂), ..., (xₙ,yₙ)} 中学习一个函数 f: X → Y，使得对新的输入 x，能够准确预测输出 y。

```mermaid
graph TD
    A[训练数据 D] --> B[学习算法 A]
    B --> C[假设函数 h]
    C --> D[预测 ŷ]
    E[新输入 x] --> C
    F[真实标签 y] --> G[损失函数 L]
    D --> G
    G --> H[经验风险 R_emp]
    H --> I[结构风险 R_struct]
    I --> J[泛化能力]
```

### 3.1.2 经验风险最小化与结构风险最小化

```python
class SupervisedLearningFramework:
    """监督学习理论框架"""

    def __init__(self):
        self.models = {}
        self.risk_analysis = {}

    def empirical_risk_minimization(self, X, y, model, loss_function):
        """经验风险最小化"""
        n_samples = len(y)
        predictions = model.predict(X)

        # 计算经验风险
        empirical_risk = np.mean([loss_function(y[i], predictions[i]) for i in range(n_samples)])

        return empirical_risk

    def structural_risk_minimization(self, empirical_risk, complexity_penalty, lambda_reg):
        """结构风险最小化"""
        # R_struct = R_emp + λ * Ω(h)
        structural_risk = empirical_risk + lambda_reg * complexity_penalty
        return structural_risk

    def bias_variance_tradeoff_analysis(self, X, y, model_class, n_bootstrap=100):
        """偏差-方差权衡分析"""
        n_samples = len(y)
        predictions_ensemble = []

        # Bootstrap采样训练多个模型
        for i in range(n_bootstrap):
            # Bootstrap采样
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot, y_boot = X[indices], y[indices]

            # 训练模型
            model = model_class()
            model.fit(X_boot, y_boot)

            # 预测
            predictions = model.predict(X)
            predictions_ensemble.append(predictions)

        predictions_ensemble = np.array(predictions_ensemble)

        # 计算偏差和方差
        mean_prediction = np.mean(predictions_ensemble, axis=0)
        bias_squared = np.mean((mean_prediction - y)**2)
        variance = np.mean(np.var(predictions_ensemble, axis=0))

        return {
            'bias_squared': bias_squared,
            'variance': variance,
            'total_error': bias_squared + variance,
            'predictions_ensemble': predictions_ensemble
        }

# 理论框架演示
def supervised_learning_theory_demo():
    """监督学习理论演示"""
    from sklearn.linear_model import LinearRegression, Ridge
    from sklearn.preprocessing import PolynomialFeatures
    from sklearn.pipeline import Pipeline

    # 生成合成数据
    np.random.seed(42)
    n_samples = 100
    X = np.linspace(0, 1, n_samples).reshape(-1, 1)
    true_function = lambda x: 1.5 * x + 0.3 * np.sin(15 * x)
    y = true_function(X.ravel()) + 0.1 * np.random.randn(n_samples)

    framework = SupervisedLearningFramework()

    # 比较不同复杂度模型的偏差-方差权衡
    models = {
        'Linear': lambda: LinearRegression(),
        'Polynomial_3': lambda: Pipeline([
            ('poly', PolynomialFeatures(3)),
            ('linear', LinearRegression())
        ]),
        'Polynomial_10': lambda: Pipeline([
            ('poly', PolynomialFeatures(10)),
            ('linear', LinearRegression())
        ])
    }

    print("偏差-方差权衡分析:")
    for name, model_class in models.items():
        analysis = framework.bias_variance_tradeoff_analysis(X, y, model_class)
        print(f"\n{name}:")
        print(f"  偏差²: {analysis['bias_squared']:.6f}")
        print(f"  方差: {analysis['variance']:.6f}")
        print(f"  总误差: {analysis['total_error']:.6f}")

supervised_learning_theory_demo()

## 3.2 线性模型深度解析

### 3.2.1 线性回归的完整理论

线性回归是最基础但也是最重要的监督学习算法，其数学优雅性和可解释性使其在实际应用中广泛使用。

#### 数学推导与几何直觉

```python
class LinearRegressionComplete:
    """线性回归的完整实现与分析"""

    def __init__(self, fit_intercept=True, regularization=None, alpha=1.0):
        self.fit_intercept = fit_intercept
        self.regularization = regularization  # None, 'l1', 'l2', 'elastic_net'
        self.alpha = alpha
        self.weights = None
        self.intercept = None
        self.training_history = {}

    def add_intercept(self, X):
        """添加截距项"""
        if self.fit_intercept:
            return np.column_stack([np.ones(X.shape[0]), X])
        return X

    def analytical_solution(self, X, y):
        """解析解：正规方程"""
        X_with_intercept = self.add_intercept(X)

        if self.regularization == 'l2':
            # Ridge回归：w = (X^T X + αI)^(-1) X^T y
            I = np.eye(X_with_intercept.shape[1])
            if self.fit_intercept:
                I[0, 0] = 0  # 不正则化截距项

            weights = np.linalg.inv(X_with_intercept.T @ X_with_intercept + self.alpha * I) @ X_with_intercept.T @ y
        else:
            # 普通最小二乘：w = (X^T X)^(-1) X^T y
            weights = np.linalg.inv(X_with_intercept.T @ X_with_intercept) @ X_with_intercept.T @ y

        if self.fit_intercept:
            self.intercept = weights[0]
            self.weights = weights[1:]
        else:
            self.intercept = 0
            self.weights = weights

        return self

    def gradient_descent_solution(self, X, y, learning_rate=0.01, max_iter=1000, tol=1e-6):
        """梯度下降解"""
        X_with_intercept = self.add_intercept(X)
        n_samples, n_features = X_with_intercept.shape

        # 初始化权重
        weights = np.random.randn(n_features) * 0.01

        # 记录训练历史
        costs = []

        for i in range(max_iter):
            # 前向传播
            predictions = X_with_intercept @ weights

            # 计算损失
            if self.regularization == 'l2':
                cost = np.mean((predictions - y)**2) + self.alpha * np.sum(weights[1:]**2)
            elif self.regularization == 'l1':
                cost = np.mean((predictions - y)**2) + self.alpha * np.sum(np.abs(weights[1:]))
            else:
                cost = np.mean((predictions - y)**2)

            costs.append(cost)

            # 计算梯度
            residuals = predictions - y
            gradients = (2/n_samples) * X_with_intercept.T @ residuals

            # 添加正则化梯度
            if self.regularization == 'l2':
                reg_gradient = np.zeros_like(weights)
                reg_gradient[1:] = 2 * self.alpha * weights[1:]
                gradients += reg_gradient
            elif self.regularization == 'l1':
                reg_gradient = np.zeros_like(weights)
                reg_gradient[1:] = self.alpha * np.sign(weights[1:])
                gradients += reg_gradient

            # 更新权重
            weights -= learning_rate * gradients

            # 检查收敛
            if i > 0 and abs(costs[-1] - costs[-2]) < tol:
                break

        # 保存结果
        if self.fit_intercept:
            self.intercept = weights[0]
            self.weights = weights[1:]
        else:
            self.intercept = 0
            self.weights = weights

        self.training_history['costs'] = costs
        self.training_history['iterations'] = i + 1

        return self

    def predict(self, X):
        """预测"""
        return X @ self.weights + self.intercept

    def confidence_intervals(self, X, y, confidence_level=0.95):
        """计算预测的置信区间"""
        X_with_intercept = self.add_intercept(X)
        n_samples, n_features = X_with_intercept.shape

        # 计算残差标准误差
        predictions = self.predict(X)
        residuals = y - predictions
        mse = np.mean(residuals**2)
        residual_std = np.sqrt(mse)

        # 计算协方差矩阵
        try:
            cov_matrix = mse * np.linalg.inv(X_with_intercept.T @ X_with_intercept)
        except np.linalg.LinAlgError:
            # 处理奇异矩阵
            cov_matrix = mse * np.linalg.pinv(X_with_intercept.T @ X_with_intercept)

        # 计算预测标准误差
        prediction_std = np.sqrt(np.diag(X_with_intercept @ cov_matrix @ X_with_intercept.T))

        # 计算置信区间
        from scipy import stats
        alpha = 1 - confidence_level
        t_value = stats.t.ppf(1 - alpha/2, n_samples - n_features)

        margin_of_error = t_value * prediction_std
        lower_bound = predictions - margin_of_error
        upper_bound = predictions + margin_of_error

        return {
            'predictions': predictions,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'prediction_std': prediction_std
        }

    def feature_importance_analysis(self, feature_names=None):
        """特征重要性分析"""
        if self.weights is None:
            raise ValueError("模型尚未训练")

        importance_scores = np.abs(self.weights)

        if feature_names is None:
            feature_names = [f'Feature_{i}' for i in range(len(self.weights))]

        # 按重要性排序
        sorted_indices = np.argsort(importance_scores)[::-1]

        return {
            'feature_names': [feature_names[i] for i in sorted_indices],
            'importance_scores': importance_scores[sorted_indices],
            'weights': self.weights[sorted_indices]
        }

# 线性回归完整演示
def linear_regression_complete_demo():
    """线性回归完整功能演示"""
    # 生成合成数据
    np.random.seed(42)
    n_samples, n_features = 100, 5
    X = np.random.randn(n_samples, n_features)
    true_weights = np.array([1.5, -2.0, 0.5, 3.0, -1.0])
    y = X @ true_weights + 0.1 * np.random.randn(n_samples)

    # 添加特征名称
    feature_names = ['房屋面积', '房间数量', '建造年份', '地段评分', '交通便利性']

    print("线性回归完整分析:")
    print(f"真实权重: {true_weights}")

    # 1. 解析解
    model_analytical = LinearRegressionComplete()
    model_analytical.analytical_solution(X, y)
    print(f"\n解析解权重: {model_analytical.weights}")

    # 2. 梯度下降解
    model_gd = LinearRegressionComplete()
    model_gd.gradient_descent_solution(X, y, learning_rate=0.01, max_iter=1000)
    print(f"梯度下降权重: {model_gd.weights}")
    print(f"收敛迭代次数: {model_gd.training_history['iterations']}")

    # 3. Ridge回归
    model_ridge = LinearRegressionComplete(regularization='l2', alpha=0.1)
    model_ridge.analytical_solution(X, y)
    print(f"Ridge回归权重: {model_ridge.weights}")

    # 4. 置信区间分析
    confidence_results = model_analytical.confidence_intervals(X, y, confidence_level=0.95)
    print(f"\n预测置信区间分析:")
    print(f"平均预测标准误差: {np.mean(confidence_results['prediction_std']):.4f}")

    # 5. 特征重要性分析
    importance = model_analytical.feature_importance_analysis(feature_names)
    print(f"\n特征重要性排序:")
    for i, (name, score, weight) in enumerate(zip(importance['feature_names'],
                                                 importance['importance_scores'],
                                                 importance['weights'])):
        print(f"  {i+1}. {name}: 重要性={score:.4f}, 权重={weight:.4f}")

linear_regression_complete_demo()

### 3.2.2 逻辑回归的深度剖析

逻辑回归是分类问题的基石，其优雅的概率解释和高效的优化使其在工业界广泛应用。

#### 从线性回归到逻辑回归的数学桥梁

```python
class LogisticRegressionComplete:
    """逻辑回归的完整实现与分析"""

    def __init__(self, regularization=None, C=1.0, max_iter=1000, tol=1e-6):
        self.regularization = regularization  # None, 'l1', 'l2'
        self.C = C  # 正则化强度的倒数
        self.max_iter = max_iter
        self.tol = tol
        self.weights = None
        self.intercept = None
        self.training_history = {}

    def sigmoid(self, z):
        """Sigmoid激活函数（数值稳定版本）"""
        # 避免数值溢出
        z = np.clip(z, -500, 500)
        return 1 / (1 + np.exp(-z))

    def log_likelihood(self, X, y, weights):
        """对数似然函数"""
        z = X @ weights
        # 数值稳定的对数似然计算
        log_likelihood = np.sum(y * z - np.log(1 + np.exp(z)))

        # 添加正则化项
        if self.regularization == 'l2':
            log_likelihood -= (1 / (2 * self.C)) * np.sum(weights[1:]**2)
        elif self.regularization == 'l1':
            log_likelihood -= (1 / self.C) * np.sum(np.abs(weights[1:]))

        return log_likelihood

    def gradient_and_hessian(self, X, y, weights):
        """计算梯度和Hessian矩阵"""
        z = X @ weights
        p = self.sigmoid(z)

        # 梯度
        gradient = X.T @ (p - y)

        # 添加正则化梯度
        if self.regularization == 'l2':
            reg_gradient = np.zeros_like(weights)
            reg_gradient[1:] = weights[1:] / self.C
            gradient += reg_gradient
        elif self.regularization == 'l1':
            reg_gradient = np.zeros_like(weights)
            reg_gradient[1:] = np.sign(weights[1:]) / self.C
            gradient += reg_gradient

        # Hessian矩阵
        W = np.diag(p * (1 - p))
        hessian = X.T @ W @ X

        # 添加正则化Hessian
        if self.regularization == 'l2':
            reg_hessian = np.eye(len(weights)) / self.C
            reg_hessian[0, 0] = 0  # 不正则化截距项
            hessian += reg_hessian

        return gradient, hessian

    def newton_raphson_optimization(self, X, y):
        """牛顿-拉夫逊优化"""
        # 添加截距项
        X_with_intercept = np.column_stack([np.ones(X.shape[0]), X])
        n_features = X_with_intercept.shape[1]

        # 初始化权重
        weights = np.zeros(n_features)

        # 记录训练历史
        log_likelihoods = []

        for i in range(self.max_iter):
            # 计算对数似然
            ll = self.log_likelihood(X_with_intercept, y, weights)
            log_likelihoods.append(ll)

            # 计算梯度和Hessian
            gradient, hessian = self.gradient_and_hessian(X_with_intercept, y, weights)

            # 牛顿步长
            try:
                delta_weights = np.linalg.solve(hessian, gradient)
                weights -= delta_weights
            except np.linalg.LinAlgError:
                # 如果Hessian奇异，使用梯度下降
                weights -= 0.01 * gradient

            # 检查收敛
            if i > 0 and abs(log_likelihoods[-1] - log_likelihoods[-2]) < self.tol:
                break

        # 保存结果
        self.intercept = weights[0]
        self.weights = weights[1:]
        self.training_history['log_likelihoods'] = log_likelihoods
        self.training_history['iterations'] = i + 1

        return self

    def predict_proba(self, X):
        """预测概率"""
        z = X @ self.weights + self.intercept
        prob_positive = self.sigmoid(z)
        return np.column_stack([1 - prob_positive, prob_positive])

    def predict(self, X, threshold=0.5):
        """预测类别"""
        probabilities = self.predict_proba(X)[:, 1]
        return (probabilities >= threshold).astype(int)

    def decision_boundary_analysis(self, X, y, feature_indices=[0, 1]):
        """决策边界分析（适用于2D可视化）"""
        if len(feature_indices) != 2:
            raise ValueError("决策边界分析需要选择2个特征")

        # 提取选定特征
        X_2d = X[:, feature_indices]

        # 创建网格
        h = 0.02
        x_min, x_max = X_2d[:, 0].min() - 1, X_2d[:, 0].max() + 1
        y_min, y_max = X_2d[:, 1].min() - 1, X_2d[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                            np.arange(y_min, y_max, h))

        # 预测网格点
        grid_points = np.c_[xx.ravel(), yy.ravel()]

        # 为了预测，需要重新训练只使用这两个特征的模型
        temp_model = LogisticRegressionComplete(
            regularization=self.regularization, C=self.C
        )
        temp_model.newton_raphson_optimization(X_2d, y)

        Z = temp_model.predict_proba(grid_points)[:, 1]
        Z = Z.reshape(xx.shape)

        return xx, yy, Z, X_2d

    def feature_importance_analysis(self, feature_names=None):
        """特征重要性分析"""
        if self.weights is None:
            raise ValueError("模型尚未训练")

        # 使用权重的绝对值作为重要性指标
        importance_scores = np.abs(self.weights)

        if feature_names is None:
            feature_names = [f'Feature_{i}' for i in range(len(self.weights))]

        # 按重要性排序
        sorted_indices = np.argsort(importance_scores)[::-1]

        return {
            'feature_names': [feature_names[i] for i in sorted_indices],
            'importance_scores': importance_scores[sorted_indices],
            'weights': self.weights[sorted_indices],
            'odds_ratios': np.exp(self.weights[sorted_indices])  # 优势比
        }

# 逻辑回归演示
def logistic_regression_complete_demo():
    """逻辑回归完整功能演示"""
    from sklearn.datasets import make_classification
    from sklearn.preprocessing import StandardScaler

    # 生成分类数据
    X, y = make_classification(n_samples=1000, n_features=5, n_informative=3,
                              n_redundant=1, n_clusters_per_class=1, random_state=42)

    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    feature_names = ['年龄', '收入', '教育水平', '工作经验', '信用评分']

    print("逻辑回归完整分析:")

    # 1. 标准逻辑回归
    model = LogisticRegressionComplete()
    model.newton_raphson_optimization(X_scaled, y)

    print(f"收敛迭代次数: {model.training_history['iterations']}")
    print(f"最终对数似然: {model.training_history['log_likelihoods'][-1]:.4f}")

    # 2. 预测性能
    probabilities = model.predict_proba(X_scaled)
    predictions = model.predict(X_scaled)
    accuracy = np.mean(predictions == y)
    print(f"训练准确率: {accuracy:.4f}")

    # 3. 特征重要性分析
    importance = model.feature_importance_analysis(feature_names)
    print(f"\n特征重要性分析:")
    for i, (name, score, weight, odds_ratio) in enumerate(zip(
        importance['feature_names'], importance['importance_scores'],
        importance['weights'], importance['odds_ratios'])):
        print(f"  {i+1}. {name}:")
        print(f"     重要性: {score:.4f}")
        print(f"     权重: {weight:.4f}")
        print(f"     优势比: {odds_ratio:.4f}")

    # 4. 正则化比较
    print(f"\n正则化效果比较:")
    regularizations = [None, 'l1', 'l2']
    C_values = [1.0, 0.1, 0.01]

    for reg in regularizations:
        for C in C_values:
            model_reg = LogisticRegressionComplete(regularization=reg, C=C)
            model_reg.newton_raphson_optimization(X_scaled, y)
            pred_reg = model_reg.predict(X_scaled)
            acc_reg = np.mean(pred_reg == y)
            weight_norm = np.linalg.norm(model_reg.weights)

            reg_name = reg if reg else 'None'
            print(f"  {reg_name}, C={C}: 准确率={acc_reg:.4f}, 权重范数={weight_norm:.4f}")

logistic_regression_complete_demo()

## 3.3 支持向量机深度解析

### 3.3.1 从几何直觉到数学优化

支持向量机是最优雅的机器学习算法之一，它将分类问题转化为寻找最大间隔超平面的优化问题。

```python
class SupportVectorMachineComplete:
    """支持向量机的完整实现"""

    def __init__(self, C=1.0, kernel='linear', gamma='scale', degree=3, coef0=0.0):
        self.C = C  # 正则化参数
        self.kernel = kernel  # 核函数类型
        self.gamma = gamma
        self.degree = degree
        self.coef0 = coef0

        # 训练后的参数
        self.support_vectors = None
        self.support_vector_labels = None
        self.alpha = None
        self.intercept = None
        self.training_history = {}

    def kernel_function(self, X1, X2):
        """核函数计算"""
        if self.kernel == 'linear':
            return X1 @ X2.T

        elif self.kernel == 'polynomial':
            return (self.gamma * (X1 @ X2.T) + self.coef0) ** self.degree

        elif self.kernel == 'rbf':
            # RBF核：K(x,y) = exp(-γ||x-y||²)
            if self.gamma == 'scale':
                gamma_val = 1.0 / X1.shape[1]
            else:
                gamma_val = self.gamma

            # 计算欧几里得距离的平方
            X1_norm = np.sum(X1**2, axis=1, keepdims=True)
            X2_norm = np.sum(X2**2, axis=1, keepdims=True)
            distances_sq = X1_norm + X2_norm.T - 2 * (X1 @ X2.T)

            return np.exp(-gamma_val * distances_sq)

        elif self.kernel == 'sigmoid':
            return np.tanh(self.gamma * (X1 @ X2.T) + self.coef0)

        else:
            raise ValueError(f"不支持的核函数: {self.kernel}")

    def smo_algorithm(self, X, y, max_iter=1000, tol=1e-3):
        """简化的SMO算法实现"""
        n_samples = X.shape[0]

        # 初始化拉格朗日乘子
        alpha = np.zeros(n_samples)
        intercept = 0.0

        # 计算核矩阵
        K = self.kernel_function(X, X)

        # SMO主循环
        for iteration in range(max_iter):
            alpha_prev = alpha.copy()

            for i in range(n_samples):
                # 计算预测值
                prediction_i = np.sum(alpha * y * K[i, :]) + intercept
                error_i = prediction_i - y[i]

                # 检查KKT条件
                if (y[i] * error_i < -tol and alpha[i] < self.C) or \
                   (y[i] * error_i > tol and alpha[i] > 0):

                    # 随机选择第二个变量
                    j = np.random.choice([idx for idx in range(n_samples) if idx != i])

                    prediction_j = np.sum(alpha * y * K[j, :]) + intercept
                    error_j = prediction_j - y[j]

                    # 保存旧的alpha值
                    alpha_i_old, alpha_j_old = alpha[i], alpha[j]

                    # 计算边界
                    if y[i] != y[j]:
                        L = max(0, alpha[j] - alpha[i])
                        H = min(self.C, self.C + alpha[j] - alpha[i])
                    else:
                        L = max(0, alpha[i] + alpha[j] - self.C)
                        H = min(self.C, alpha[i] + alpha[j])

                    if L == H:
                        continue

                    # 计算eta
                    eta = 2 * K[i, j] - K[i, i] - K[j, j]
                    if eta >= 0:
                        continue

                    # 更新alpha[j]
                    alpha[j] = alpha[j] - (y[j] * (error_i - error_j)) / eta
                    alpha[j] = max(L, min(H, alpha[j]))

                    if abs(alpha[j] - alpha_j_old) < 1e-5:
                        continue

                    # 更新alpha[i]
                    alpha[i] = alpha[i] + y[i] * y[j] * (alpha_j_old - alpha[j])

                    # 更新截距
                    b1 = intercept - error_i - y[i] * (alpha[i] - alpha_i_old) * K[i, i] - \
                         y[j] * (alpha[j] - alpha_j_old) * K[i, j]
                    b2 = intercept - error_j - y[i] * (alpha[i] - alpha_i_old) * K[i, j] - \
                         y[j] * (alpha[j] - alpha_j_old) * K[j, j]

                    if 0 < alpha[i] < self.C:
                        intercept = b1
                    elif 0 < alpha[j] < self.C:
                        intercept = b2
                    else:
                        intercept = (b1 + b2) / 2

            # 检查收敛
            if np.linalg.norm(alpha - alpha_prev) < tol:
                break

        # 找到支持向量
        support_vector_indices = alpha > 1e-5
        self.support_vectors = X[support_vector_indices]
        self.support_vector_labels = y[support_vector_indices]
        self.alpha = alpha[support_vector_indices]
        self.intercept = intercept

        self.training_history['iterations'] = iteration + 1
        self.training_history['n_support_vectors'] = len(self.alpha)

        return self

    def predict(self, X):
        """预测"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        # 计算决策函数值
        K = self.kernel_function(X, self.support_vectors)
        decision_values = np.sum(self.alpha * self.support_vector_labels * K.T, axis=0) + self.intercept

        return np.sign(decision_values).astype(int)

    def decision_function(self, X):
        """决策函数值"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        K = self.kernel_function(X, self.support_vectors)
        return np.sum(self.alpha * self.support_vector_labels * K.T, axis=0) + self.intercept

    def margin_analysis(self):
        """间隔分析"""
        if self.support_vectors is None:
            raise ValueError("模型尚未训练")

        # 计算几何间隔
        # 对于线性SVM，间隔 = 2/||w||
        if self.kernel == 'linear':
            # 重构权重向量
            w = np.sum((self.alpha * self.support_vector_labels)[:, np.newaxis] *
                      self.support_vectors, axis=0)
            margin = 2.0 / np.linalg.norm(w)
        else:
            # 对于非线性SVM，使用支持向量到决策边界的距离
            sv_distances = np.abs(self.decision_function(self.support_vectors))
            margin = 2.0 * np.min(sv_distances)

        return {
            'margin': margin,
            'n_support_vectors': len(self.alpha),
            'support_vector_ratio': len(self.alpha) / len(self.support_vectors)
        }

# SVM演示
def svm_complete_demo():
    """SVM完整功能演示"""
    from sklearn.datasets import make_classification
    from sklearn.preprocessing import StandardScaler

    # 生成分类数据
    X, y = make_classification(n_samples=200, n_features=2, n_redundant=0,
                              n_informative=2, n_clusters_per_class=1, random_state=42)

    # 转换标签为{-1, 1}
    y = 2 * y - 1

    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    print("支持向量机完整分析:")

    # 测试不同核函数
    kernels = ['linear', 'polynomial', 'rbf']

    for kernel in kernels:
        print(f"\n{kernel.upper()}核函数:")

        # 训练SVM
        svm = SupportVectorMachineComplete(C=1.0, kernel=kernel, gamma=0.1)
        svm.smo_algorithm(X_scaled, y, max_iter=500)

        # 预测和评估
        predictions = svm.predict(X_scaled)
        accuracy = np.mean(predictions == y)

        print(f"  训练准确率: {accuracy:.4f}")
        print(f"  收敛迭代次数: {svm.training_history['iterations']}")
        print(f"  支持向量数量: {svm.training_history['n_support_vectors']}")

        # 间隔分析
        margin_info = svm.margin_analysis()
        print(f"  几何间隔: {margin_info['margin']:.4f}")
        print(f"  支持向量比例: {margin_info['support_vector_ratio']:.4f}")

    # C参数影响分析
    print(f"\n正则化参数C的影响:")
    C_values = [0.1, 1.0, 10.0, 100.0]

    for C in C_values:
        svm_c = SupportVectorMachineComplete(C=C, kernel='rbf', gamma=0.1)
        svm_c.smo_algorithm(X_scaled, y, max_iter=300)

        predictions_c = svm_c.predict(X_scaled)
        accuracy_c = np.mean(predictions_c == y)
        margin_c = svm_c.margin_analysis()

        print(f"  C={C:5.1f}: 准确率={accuracy_c:.4f}, "
              f"支持向量={margin_c['n_support_vectors']:3d}, "
              f"间隔={margin_c['margin']:.4f}")

svm_complete_demo()

## 3.4 决策树与集成学习

### 3.4.1 决策树的完整理论与实现

决策树是最直观的机器学习算法，其可解释性和处理非线性关系的能力使其在实际应用中非常重要。

```python
class DecisionTreeComplete:
    """决策树的完整实现"""

    def __init__(self, criterion='gini', max_depth=None, min_samples_split=2,
                 min_samples_leaf=1, max_features=None):
        self.criterion = criterion  # 'gini', 'entropy', 'mse'
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.max_features = max_features

        # 树结构
        self.tree = None
        self.feature_importances_ = None
        self.n_features = None
        self.n_classes = None

    class TreeNode:
        """树节点类"""
        def __init__(self):
            self.feature_index = None      # 分割特征索引
            self.threshold = None          # 分割阈值
            self.left = None              # 左子树
            self.right = None             # 右子树
            self.value = None             # 叶节点的预测值
            self.samples = 0              # 节点样本数
            self.impurity = 0.0           # 节点不纯度
            self.is_leaf = False          # 是否为叶节点

    def gini_impurity(self, y):
        """基尼不纯度"""
        if len(y) == 0:
            return 0

        _, counts = np.unique(y, return_counts=True)
        probabilities = counts / len(y)
        return 1 - np.sum(probabilities**2)

    def entropy(self, y):
        """信息熵"""
        if len(y) == 0:
            return 0

        _, counts = np.unique(y, return_counts=True)
        probabilities = counts / len(y)
        return -np.sum(probabilities * np.log2(probabilities + 1e-10))

    def mse(self, y):
        """均方误差（回归树）"""
        if len(y) == 0:
            return 0
        return np.var(y)

    def calculate_impurity(self, y):
        """计算不纯度"""
        if self.criterion == 'gini':
            return self.gini_impurity(y)
        elif self.criterion == 'entropy':
            return self.entropy(y)
        elif self.criterion == 'mse':
            return self.mse(y)
        else:
            raise ValueError(f"不支持的准则: {self.criterion}")

    def information_gain(self, y_parent, y_left, y_right):
        """信息增益"""
        n_parent = len(y_parent)
        n_left, n_right = len(y_left), len(y_right)

        if n_left == 0 or n_right == 0:
            return 0

        # 加权平均的子节点不纯度
        weighted_impurity = (n_left / n_parent) * self.calculate_impurity(y_left) + \
                           (n_right / n_parent) * self.calculate_impurity(y_right)

        # 信息增益 = 父节点不纯度 - 加权子节点不纯度
        return self.calculate_impurity(y_parent) - weighted_impurity

    def find_best_split(self, X, y):
        """寻找最佳分割"""
        n_samples, n_features = X.shape

        if n_samples < self.min_samples_split:
            return None, None, 0

        # 确定要考虑的特征数量
        if self.max_features is None:
            max_features = n_features
        elif isinstance(self.max_features, int):
            max_features = min(self.max_features, n_features)
        elif self.max_features == 'sqrt':
            max_features = int(np.sqrt(n_features))
        elif self.max_features == 'log2':
            max_features = int(np.log2(n_features))
        else:
            max_features = n_features

        # 随机选择特征子集
        feature_indices = np.random.choice(n_features, max_features, replace=False)

        best_gain = 0
        best_feature = None
        best_threshold = None

        for feature_idx in feature_indices:
            # 获取该特征的唯一值作为候选阈值
            feature_values = np.unique(X[:, feature_idx])

            for i in range(len(feature_values) - 1):
                threshold = (feature_values[i] + feature_values[i + 1]) / 2

                # 分割数据
                left_mask = X[:, feature_idx] <= threshold
                right_mask = ~left_mask

                if np.sum(left_mask) < self.min_samples_leaf or \
                   np.sum(right_mask) < self.min_samples_leaf:
                    continue

                # 计算信息增益
                gain = self.information_gain(y, y[left_mask], y[right_mask])

                if gain > best_gain:
                    best_gain = gain
                    best_feature = feature_idx
                    best_threshold = threshold

        return best_feature, best_threshold, best_gain

    def build_tree(self, X, y, depth=0):
        """递归构建决策树"""
        node = self.TreeNode()
        node.samples = len(y)
        node.impurity = self.calculate_impurity(y)

        # 检查停止条件
        if (self.max_depth is not None and depth >= self.max_depth) or \
           len(y) < self.min_samples_split or \
           len(np.unique(y)) == 1:
            # 创建叶节点
            node.is_leaf = True
            if self.criterion == 'mse':
                node.value = np.mean(y)  # 回归：预测均值
            else:
                node.value = np.bincount(y).argmax()  # 分类：预测众数
            return node

        # 寻找最佳分割
        best_feature, best_threshold, best_gain = self.find_best_split(X, y)

        if best_feature is None or best_gain == 0:
            # 无法找到有效分割，创建叶节点
            node.is_leaf = True
            if self.criterion == 'mse':
                node.value = np.mean(y)
            else:
                node.value = np.bincount(y).argmax()
            return node

        # 设置分割参数
        node.feature_index = best_feature
        node.threshold = best_threshold

        # 分割数据
        left_mask = X[:, best_feature] <= best_threshold
        right_mask = ~left_mask

        # 递归构建子树
        node.left = self.build_tree(X[left_mask], y[left_mask], depth + 1)
        node.right = self.build_tree(X[right_mask], y[right_mask], depth + 1)

        return node

    def fit(self, X, y):
        """训练决策树"""
        self.n_features = X.shape[1]

        if self.criterion == 'mse':
            self.n_classes = None  # 回归任务
        else:
            self.n_classes = len(np.unique(y))

        # 构建树
        self.tree = self.build_tree(X, y)

        # 计算特征重要性
        self.feature_importances_ = self.calculate_feature_importances(X, y)

        return self

    def predict_sample(self, x, node):
        """预测单个样本"""
        if node.is_leaf:
            return node.value

        if x[node.feature_index] <= node.threshold:
            return self.predict_sample(x, node.left)
        else:
            return self.predict_sample(x, node.right)

    def predict(self, X):
        """预测"""
        if self.tree is None:
            raise ValueError("模型尚未训练")

        predictions = []
        for x in X:
            pred = self.predict_sample(x, self.tree)
            predictions.append(pred)

        return np.array(predictions)

    def calculate_feature_importances(self, X, y):
        """计算特征重要性"""
        importances = np.zeros(self.n_features)

        def traverse_tree(node, n_samples):
            if node.is_leaf:
                return

            # 计算该节点的重要性贡献
            left_samples = node.left.samples
            right_samples = node.right.samples

            importance = (n_samples / len(y)) * node.impurity - \
                        (left_samples / len(y)) * node.left.impurity - \
                        (right_samples / len(y)) * node.right.impurity

            importances[node.feature_index] += importance

            # 递归遍历子树
            traverse_tree(node.left, left_samples)
            traverse_tree(node.right, right_samples)

        traverse_tree(self.tree, len(y))

        # 归一化
        if np.sum(importances) > 0:
            importances = importances / np.sum(importances)

        return importances

    def print_tree(self, node=None, depth=0, prefix="Root: "):
        """打印决策树结构"""
        if node is None:
            node = self.tree

        if node.is_leaf:
            print("  " * depth + prefix + f"Predict {node.value} (samples: {node.samples})")
        else:
            print("  " * depth + prefix + f"Feature {node.feature_index} <= {node.threshold:.3f} "
                  f"(samples: {node.samples}, impurity: {node.impurity:.3f})")

            if node.left:
                self.print_tree(node.left, depth + 1, "Left: ")
            if node.right:
                self.print_tree(node.right, depth + 1, "Right: ")

# 决策树演示
def decision_tree_complete_demo():
    """决策树完整功能演示"""
    from sklearn.datasets import make_classification, make_regression
    from sklearn.model_selection import train_test_split

    print("决策树完整分析:")

    # 1. 分类任务
    print("\n=== 分类任务 ===")
    X_clf, y_clf = make_classification(n_samples=500, n_features=10, n_informative=5,
                                      n_redundant=2, random_state=42)

    feature_names = [f'特征_{i+1}' for i in range(X_clf.shape[1])]

    # 训练决策树
    dt_clf = DecisionTreeComplete(criterion='gini', max_depth=5, min_samples_split=10)
    dt_clf.fit(X_clf, y_clf)

    # 预测和评估
    predictions_clf = dt_clf.predict(X_clf)
    accuracy = np.mean(predictions_clf == y_clf)
    print(f"分类准确率: {accuracy:.4f}")

    # 特征重要性
    print(f"\n特征重要性排序:")
    importance_indices = np.argsort(dt_clf.feature_importances_)[::-1]
    for i, idx in enumerate(importance_indices[:5]):
        print(f"  {i+1}. {feature_names[idx]}: {dt_clf.feature_importances_[idx]:.4f}")

    # 2. 回归任务
    print(f"\n=== 回归任务 ===")
    X_reg, y_reg = make_regression(n_samples=500, n_features=10, noise=0.1, random_state=42)

    dt_reg = DecisionTreeComplete(criterion='mse', max_depth=5, min_samples_split=10)
    dt_reg.fit(X_reg, y_reg)

    predictions_reg = dt_reg.predict(X_reg)
    mse = np.mean((predictions_reg - y_reg)**2)
    r2 = 1 - mse / np.var(y_reg)
    print(f"回归MSE: {mse:.4f}")
    print(f"回归R²: {r2:.4f}")

    # 3. 不同准则比较
    print(f"\n=== 分割准则比较 ===")
    criteria = ['gini', 'entropy']

    for criterion in criteria:
        dt_crit = DecisionTreeComplete(criterion=criterion, max_depth=5)
        dt_crit.fit(X_clf, y_clf)
        pred_crit = dt_crit.predict(X_clf)
        acc_crit = np.mean(pred_crit == y_clf)
        print(f"{criterion.upper()}准则准确率: {acc_crit:.4f}")

    # 4. 树结构可视化（简化版）
    print(f"\n=== 决策树结构（前3层）===")
    dt_simple = DecisionTreeComplete(criterion='gini', max_depth=3, min_samples_split=20)
    dt_simple.fit(X_clf[:100], y_clf[:100])  # 使用较小数据集便于展示
    dt_simple.print_tree()

decision_tree_complete_demo()

### 3.4.2 集成学习的理论与实践

集成学习通过组合多个弱学习器来构建强学习器，是提高模型性能的重要技术。

```python
class EnsembleLearning:
    """集成学习方法集合"""

    def __init__(self):
        self.models = []
        self.weights = []

    class RandomForest:
        """随机森林实现"""

        def __init__(self, n_estimators=100, max_depth=None, min_samples_split=2,
                     max_features='sqrt', bootstrap=True, random_state=None):
            self.n_estimators = n_estimators
            self.max_depth = max_depth
            self.min_samples_split = min_samples_split
            self.max_features = max_features
            self.bootstrap = bootstrap
            self.random_state = random_state

            self.trees = []
            self.feature_importances_ = None
            self.oob_score_ = None

        def fit(self, X, y):
            """训练随机森林"""
            n_samples, n_features = X.shape
            self.trees = []

            # 设置随机种子
            if self.random_state is not None:
                np.random.seed(self.random_state)

            # 存储OOB预测用于计算OOB分数
            oob_predictions = np.zeros((n_samples, self.n_estimators))
            oob_counts = np.zeros(n_samples)

            for i in range(self.n_estimators):
                # Bootstrap采样
                if self.bootstrap:
                    indices = np.random.choice(n_samples, n_samples, replace=True)
                    oob_indices = np.setdiff1d(np.arange(n_samples), indices)
                    X_bootstrap, y_bootstrap = X[indices], y[indices]
                else:
                    X_bootstrap, y_bootstrap = X, y
                    oob_indices = []

                # 训练决策树
                tree = DecisionTreeComplete(
                    max_depth=self.max_depth,
                    min_samples_split=self.min_samples_split,
                    max_features=self.max_features
                )
                tree.fit(X_bootstrap, y_bootstrap)
                self.trees.append(tree)

                # 计算OOB预测
                if len(oob_indices) > 0:
                    oob_pred = tree.predict(X[oob_indices])
                    oob_predictions[oob_indices, i] = oob_pred
                    oob_counts[oob_indices] += 1

            # 计算特征重要性
            self.feature_importances_ = np.mean([tree.feature_importances_ for tree in self.trees], axis=0)

            # 计算OOB分数
            if self.bootstrap:
                oob_mask = oob_counts > 0
                if np.sum(oob_mask) > 0:
                    oob_final_predictions = []
                    for i in range(n_samples):
                        if oob_counts[i] > 0:
                            # 对该样本的所有OOB预测进行投票
                            valid_predictions = oob_predictions[i, oob_predictions[i] != 0]
                            if len(valid_predictions) > 0:
                                oob_final_predictions.append(np.bincount(valid_predictions.astype(int)).argmax())
                            else:
                                oob_final_predictions.append(0)
                        else:
                            oob_final_predictions.append(0)

                    oob_final_predictions = np.array(oob_final_predictions)
                    self.oob_score_ = np.mean(oob_final_predictions[oob_mask] == y[oob_mask])

            return self

        def predict(self, X):
            """预测"""
            if not self.trees:
                raise ValueError("模型尚未训练")

            # 收集所有树的预测
            predictions = np.array([tree.predict(X) for tree in self.trees])

            # 多数投票
            final_predictions = []
            for i in range(X.shape[0]):
                votes = predictions[:, i]
                final_predictions.append(np.bincount(votes.astype(int)).argmax())

            return np.array(final_predictions)

        def predict_proba(self, X):
            """预测概率"""
            if not self.trees:
                raise ValueError("模型尚未训练")

            # 收集所有树的预测
            predictions = np.array([tree.predict(X) for tree in self.trees])

            # 计算每个类别的投票比例
            n_samples = X.shape[0]
            n_classes = len(np.unique(predictions))
            probabilities = np.zeros((n_samples, n_classes))

            for i in range(n_samples):
                votes = predictions[:, i].astype(int)
                vote_counts = np.bincount(votes, minlength=n_classes)
                probabilities[i] = vote_counts / len(self.trees)

            return probabilities

    class AdaBoost:
        """AdaBoost实现"""

        def __init__(self, n_estimators=50, learning_rate=1.0):
            self.n_estimators = n_estimators
            self.learning_rate = learning_rate
            self.estimators = []
            self.estimator_weights = []
            self.estimator_errors = []

        def fit(self, X, y):
            """训练AdaBoost"""
            n_samples = X.shape[0]

            # 初始化样本权重
            sample_weights = np.ones(n_samples) / n_samples

            for i in range(self.n_estimators):
                # 训练弱学习器（决策树桩）
                estimator = DecisionTreeComplete(max_depth=1)
                estimator.fit(X, y)

                # 预测
                predictions = estimator.predict(X)

                # 计算错误率
                incorrect = predictions != y
                estimator_error = np.average(incorrect, weights=sample_weights)

                # 如果错误率为0或>=0.5，停止训练
                if estimator_error <= 0:
                    self.estimators.append(estimator)
                    self.estimator_weights.append(1.0)
                    self.estimator_errors.append(estimator_error)
                    break

                if estimator_error >= 0.5:
                    if len(self.estimators) == 0:
                        raise ValueError("第一个弱学习器的错误率>=0.5")
                    break

                # 计算学习器权重
                alpha = self.learning_rate * 0.5 * np.log((1 - estimator_error) / estimator_error)

                # 更新样本权重
                sample_weights *= np.exp(alpha * incorrect * ((predictions == y) * -2 + 1))
                sample_weights /= np.sum(sample_weights)

                # 保存学习器
                self.estimators.append(estimator)
                self.estimator_weights.append(alpha)
                self.estimator_errors.append(estimator_error)

            return self

        def predict(self, X):
            """预测"""
            if not self.estimators:
                raise ValueError("模型尚未训练")

            # 加权投票
            predictions = np.zeros(X.shape[0])

            for estimator, weight in zip(self.estimators, self.estimator_weights):
                pred = estimator.predict(X)
                predictions += weight * (2 * pred - 1)  # 转换为{-1, 1}进行加权

            return (predictions > 0).astype(int)

# 集成学习演示
def ensemble_learning_demo():
    """集成学习演示"""
    from sklearn.datasets import make_classification
    from sklearn.model_selection import train_test_split

    # 生成数据
    X, y = make_classification(n_samples=1000, n_features=20, n_informative=10,
                              n_redundant=5, random_state=42)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

    print("集成学习方法比较:")

    # 1. 单个决策树（基线）
    single_tree = DecisionTreeComplete(max_depth=10)
    single_tree.fit(X_train, y_train)
    pred_single = single_tree.predict(X_test)
    acc_single = np.mean(pred_single == y_test)
    print(f"\n单个决策树准确率: {acc_single:.4f}")

    # 2. 随机森林
    ensemble = EnsembleLearning()
    rf = ensemble.RandomForest(n_estimators=100, max_depth=10, random_state=42)
    rf.fit(X_train, y_train)
    pred_rf = rf.predict(X_test)
    acc_rf = np.mean(pred_rf == y_test)
    print(f"随机森林准确率: {acc_rf:.4f}")
    print(f"随机森林OOB分数: {rf.oob_score_:.4f}")

    # 3. AdaBoost
    ada = ensemble.AdaBoost(n_estimators=50, learning_rate=1.0)
    ada.fit(X_train, y_train)
    pred_ada = ada.predict(X_test)
    acc_ada = np.mean(pred_ada == y_test)
    print(f"AdaBoost准确率: {acc_ada:.4f}")

    # 4. 特征重要性分析
    print(f"\n随机森林特征重要性（Top 5）:")
    importance_indices = np.argsort(rf.feature_importances_)[::-1]
    for i, idx in enumerate(importance_indices[:5]):
        print(f"  特征 {idx}: {rf.feature_importances_[idx]:.4f}")

    # 5. AdaBoost学习器权重分析
    print(f"\nAdaBoost学习器分析:")
    print(f"  总学习器数量: {len(ada.estimators)}")
    print(f"  平均学习器权重: {np.mean(ada.estimator_weights):.4f}")
    print(f"  平均学习器错误率: {np.mean(ada.estimator_errors):.4f}")

ensemble_learning_demo()

## 3.5 梯度提升算法详解

### 3.5.1 XGBoost算法原理与实现

XGBoost（eXtreme Gradient Boosting）是目前最流行的梯度提升算法，在各种机器学习竞赛中表现卓越。

```python
class XGBoostImplementation:
    """XGBoost核心算法实现"""

    def __init__(self, n_estimators=100, max_depth=6, learning_rate=0.3,
                 reg_lambda=1.0, reg_alpha=0.0, gamma=0.0, min_child_weight=1):
        """
        XGBoost参数说明：
        - n_estimators: 树的数量（迭代次数）
        - max_depth: 树的最大深度，控制模型复杂度
        - learning_rate: 学习率，控制每棵树的贡献
        - reg_lambda: L2正则化参数，防止过拟合
        - reg_alpha: L1正则化参数，增加稀疏性
        - gamma: 最小分割损失，控制树的生长
        - min_child_weight: 叶子节点最小权重和
        """
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.learning_rate = learning_rate
        self.reg_lambda = reg_lambda
        self.reg_alpha = reg_alpha
        self.gamma = gamma
        self.min_child_weight = min_child_weight

        self.trees = []
        self.base_prediction = 0
        self.feature_importances_ = None

    def sigmoid(self, x):
        """数值稳定的sigmoid函数"""
        return np.where(x >= 0,
                       1 / (1 + np.exp(-x)),
                       np.exp(x) / (1 + np.exp(x)))

    def compute_gradients_hessians(self, y_true, y_pred, objective='binary:logistic'):
        """
        计算梯度和二阶导数（Hessian）

        XGBoost的核心创新：使用二阶泰勒展开近似损失函数
        L(θ) ≈ L(θ₀) + g·Δθ + ½h·(Δθ)²

        其中：g是一阶导数（梯度），h是二阶导数（Hessian）
        """
        if objective == 'binary:logistic':
            # 逻辑回归的梯度和Hessian
            pred_prob = self.sigmoid(y_pred)
            gradients = pred_prob - y_true  # ∂L/∂ŷ
            hessians = pred_prob * (1 - pred_prob)  # ∂²L/∂ŷ²

        elif objective == 'reg:squarederror':
            # 平方损失的梯度和Hessian
            gradients = y_pred - y_true
            hessians = np.ones_like(y_true)

        else:
            raise ValueError(f"不支持的目标函数: {objective}")

        return gradients, hessians

    def calculate_leaf_weight(self, gradients, hessians):
        """
        计算叶子节点的最优权重

        XGBoost叶子权重公式：
        w* = -G / (H + λ)

        其中：G是梯度和，H是Hessian和，λ是L2正则化参数
        """
        G = np.sum(gradients)
        H = np.sum(hessians)
        return -G / (H + self.reg_lambda)

    def calculate_gain(self, gradients_left, hessians_left, gradients_right, hessians_right):
        """
        计算分割增益

        XGBoost增益公式：
        Gain = ½[G_L²/(H_L+λ) + G_R²/(H_R+λ) - (G_L+G_R)²/(H_L+H_R+λ)] - γ

        这个公式考虑了：
        1. 分割后左右子树的损失减少
        2. L2正则化的影响
        3. 复杂度惩罚γ
        """
        G_L, H_L = np.sum(gradients_left), np.sum(hessians_left)
        G_R, H_R = np.sum(gradients_right), np.sum(hessians_right)
        G_total, H_total = G_L + G_R, H_L + H_R

        gain = 0.5 * (
            G_L**2 / (H_L + self.reg_lambda) +
            G_R**2 / (H_R + self.reg_lambda) -
            G_total**2 / (H_total + self.reg_lambda)
        ) - self.gamma

        return gain

    def find_best_split_xgboost(self, X, gradients, hessians):
        """
        XGBoost的最优分割查找

        与传统决策树不同，XGBoost使用梯度和Hessian信息
        来评估分割质量，而不是基于信息增益或基尼不纯度
        """
        best_gain = 0
        best_feature = None
        best_threshold = None
        n_samples, n_features = X.shape

        for feature_idx in range(n_features):
            # 获取该特征的唯一值
            feature_values = np.unique(X[:, feature_idx])

            for i in range(len(feature_values) - 1):
                threshold = (feature_values[i] + feature_values[i + 1]) / 2

                # 分割数据
                left_mask = X[:, feature_idx] <= threshold
                right_mask = ~left_mask

                # 检查最小样本数约束
                if np.sum(left_mask) < self.min_child_weight or \
                   np.sum(right_mask) < self.min_child_weight:
                    continue

                # 计算增益
                gain = self.calculate_gain(
                    gradients[left_mask], hessians[left_mask],
                    gradients[right_mask], hessians[right_mask]
                )

                if gain > best_gain:
                    best_gain = gain
                    best_feature = feature_idx
                    best_threshold = threshold

        return best_feature, best_threshold, best_gain

    def build_tree_xgboost(self, X, gradients, hessians, depth=0):
        """
        构建XGBoost树

        XGBoost树的构建过程：
        1. 计算当前节点的最优权重
        2. 如果满足停止条件，创建叶子节点
        3. 否则寻找最优分割点
        4. 递归构建左右子树
        """
        # 创建节点
        node = {'is_leaf': False, 'weight': 0, 'feature': None, 'threshold': None,
                'left': None, 'right': None}

        # 检查停止条件
        if (depth >= self.max_depth or
            len(gradients) < self.min_child_weight or
            np.sum(hessians) < self.min_child_weight):

            node['is_leaf'] = True
            node['weight'] = self.calculate_leaf_weight(gradients, hessians)
            return node

        # 寻找最优分割
        best_feature, best_threshold, best_gain = self.find_best_split_xgboost(
            X, gradients, hessians
        )

        if best_feature is None or best_gain <= 0:
            node['is_leaf'] = True
            node['weight'] = self.calculate_leaf_weight(gradients, hessians)
            return node

        # 设置分割参数
        node['feature'] = best_feature
        node['threshold'] = best_threshold

        # 分割数据
        left_mask = X[:, best_feature] <= best_threshold
        right_mask = ~left_mask

        # 递归构建子树
        node['left'] = self.build_tree_xgboost(
            X[left_mask], gradients[left_mask], hessians[left_mask], depth + 1
        )
        node['right'] = self.build_tree_xgboost(
            X[right_mask], gradients[right_mask], hessians[right_mask], depth + 1
        )

        return node

    def predict_tree(self, tree, x):
        """单棵树预测"""
        if tree['is_leaf']:
            return tree['weight']

        if x[tree['feature']] <= tree['threshold']:
            return self.predict_tree(tree['left'], x)
        else:
            return self.predict_tree(tree['right'], x)

    def fit(self, X, y, objective='binary:logistic'):
        """
        训练XGBoost模型

        XGBoost训练过程：
        1. 初始化预测值（通常为0）
        2. 对每次迭代：
           a. 计算当前预测的梯度和Hessian
           b. 构建新树来拟合负梯度
           c. 更新预测值
        3. 重复直到达到指定迭代次数
        """
        n_samples, n_features = X.shape

        # 初始化预测值
        if objective == 'binary:logistic':
            self.base_prediction = np.log(np.mean(y) / (1 - np.mean(y)))  # log-odds
        else:
            self.base_prediction = np.mean(y)

        y_pred = np.full(n_samples, self.base_prediction)

        # 迭代构建树
        for i in range(self.n_estimators):
            # 计算梯度和Hessian
            gradients, hessians = self.compute_gradients_hessians(y, y_pred, objective)

            # 构建树
            tree = self.build_tree_xgboost(X, gradients, hessians)
            self.trees.append(tree)

            # 更新预测值
            tree_predictions = np.array([self.predict_tree(tree, x) for x in X])
            y_pred += self.learning_rate * tree_predictions

        # 计算特征重要性（简化版本）
        self.feature_importances_ = self.calculate_feature_importance(X.shape[1])

        return self

    def predict(self, X, output_margin=False):
        """预测"""
        if not self.trees:
            raise ValueError("模型尚未训练")

        predictions = np.full(X.shape[0], self.base_prediction)

        for tree in self.trees:
            tree_pred = np.array([self.predict_tree(tree, x) for x in X])
            predictions += self.learning_rate * tree_pred

        if output_margin:
            return predictions
        else:
            return self.sigmoid(predictions)  # 转换为概率

    def calculate_feature_importance(self, n_features):
        """计算特征重要性（基于分割次数）"""
        importance = np.zeros(n_features)

        def traverse_tree(node):
            if not node['is_leaf']:
                importance[node['feature']] += 1
                traverse_tree(node['left'])
                traverse_tree(node['right'])

        for tree in self.trees:
            traverse_tree(tree)

        # 归一化
        if np.sum(importance) > 0:
            importance = importance / np.sum(importance)

        return importance
```

### 3.5.2 XGBoost应用场景详解

XGBoost在多个领域都有出色表现，让我们通过具体案例来理解其应用。

```python
# XGBoost应用场景演示
def xgboost_applications_demo():
    """XGBoost在不同场景的应用演示"""

    print("=== XGBoost应用场景演示 ===")

    # 场景1：信用卡欺诈检测
    print("\n场景1：信用卡欺诈检测")
    print("特点：高度不平衡数据，需要高召回率")

    # 生成模拟的信用卡交易数据
    np.random.seed(42)
    n_samples = 10000
    n_features = 20

    # 正常交易特征（大部分）
    normal_features = np.random.normal(0, 1, (int(n_samples * 0.99), n_features))
    normal_labels = np.zeros(int(n_samples * 0.99))

    # 欺诈交易特征（少数，特征分布不同）
    fraud_features = np.random.normal(2, 1.5, (int(n_samples * 0.01), n_features))
    fraud_labels = np.ones(int(n_samples * 0.01))

    X_fraud = np.vstack([normal_features, fraud_features])
    y_fraud = np.hstack([normal_labels, fraud_labels])

    # 训练XGBoost
    xgb_fraud = XGBoostImplementation(
        n_estimators=50, max_depth=4, learning_rate=0.1,
        reg_lambda=1.0, gamma=0.1
    )
    xgb_fraud.fit(X_fraud, y_fraud, objective='binary:logistic')

    # 预测和评估
    fraud_probs = xgb_fraud.predict(X_fraud)
    fraud_preds = (fraud_probs > 0.5).astype(int)

    # 计算混淆矩阵
    tn = np.sum((y_fraud == 0) & (fraud_preds == 0))
    fp = np.sum((y_fraud == 0) & (fraud_preds == 1))
    fn = np.sum((y_fraud == 1) & (fraud_preds == 0))
    tp = np.sum((y_fraud == 1) & (fraud_preds == 1))

    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

    print(f"欺诈检测结果:")
    print(f"  精确率: {precision:.4f}")
    print(f"  召回率: {recall:.4f}")
    print(f"  F1分数: {f1_score:.4f}")
    print(f"  检测到的欺诈交易: {tp}/{tp+fn}")

xgboost_applications_demo()

---

# 第四章：无监督学习深度剖析

> **核心理念**: 无监督学习从无标签数据中发现隐藏的模式和结构，是数据科学中最具挑战性和创造性的领域。

## 4.1 聚类算法完整体系

### 4.1.1 K-Means聚类的深度解析

K-Means是最经典的聚类算法，其简洁性和有效性使其在工业界广泛应用。

```python
class KMeansComplete:
    """K-Means聚类的完整实现与分析"""

    def __init__(self, n_clusters=8, init='k-means++', max_iter=300, tol=1e-4, random_state=None):
        """
        K-Means参数说明：
        - n_clusters: 聚类数量K，这是最关键的超参数
        - init: 初始化方法，'k-means++'能更好地选择初始中心点
        - max_iter: 最大迭代次数，防止算法无限循环
        - tol: 收敛容忍度，中心点变化小于此值时停止迭代
        - random_state: 随机种子，确保结果可重现
        """
        self.n_clusters = n_clusters
        self.init = init
        self.max_iter = max_iter
        self.tol = tol
        self.random_state = random_state

        # 训练后的属性
        self.cluster_centers_ = None
        self.labels_ = None
        self.inertia_ = None  # 簇内平方和
        self.n_iter_ = None   # 实际迭代次数

    def _init_centroids(self, X):
        """
        初始化聚类中心

        K-means++初始化算法：
        1. 随机选择第一个中心点
        2. 对每个后续中心点：
           - 计算每个点到已选中心的最小距离
           - 按距离平方的概率选择下一个中心
        3. 重复直到选择了K个中心点

        这种方法比随机初始化更能避免局部最优解
        """
        n_samples, n_features = X.shape

        if self.random_state is not None:
            np.random.seed(self.random_state)

        if self.init == 'k-means++':
            centroids = np.zeros((self.n_clusters, n_features))

            # 随机选择第一个中心点
            centroids[0] = X[np.random.randint(n_samples)]

            # 选择剩余的中心点
            for c_id in range(1, self.n_clusters):
                # 计算每个点到最近中心的距离平方
                distances = np.array([min([np.sum((x - c)**2) for c in centroids[:c_id]])
                                    for x in X])

                # 按概率选择下一个中心（距离越远，被选中概率越大）
                probabilities = distances / distances.sum()
                cumulative_probs = probabilities.cumsum()
                r = np.random.rand()

                for j, p in enumerate(cumulative_probs):
                    if r < p:
                        centroids[c_id] = X[j]
                        break

        elif self.init == 'random':
            # 随机初始化
            centroids = X[np.random.choice(n_samples, self.n_clusters, replace=False)]

        else:
            raise ValueError(f"不支持的初始化方法: {self.init}")

        return centroids

    def _assign_clusters(self, X, centroids):
        """
        分配样本到最近的聚类中心

        使用欧几里得距离计算样本到各中心点的距离：
        distance(x, c) = √(Σ(x_i - c_i)²)

        为了计算效率，通常使用距离的平方（避免开方运算）
        """
        distances = np.sqrt(((X - centroids[:, np.newaxis])**2).sum(axis=2))
        return np.argmin(distances, axis=0)

    def _update_centroids(self, X, labels):
        """
        更新聚类中心

        新的中心点是该簇内所有点的均值：
        c_new = (1/|S_i|) * Σ(x ∈ S_i) x

        其中S_i是第i个簇的所有样本点
        """
        centroids = np.zeros((self.n_clusters, X.shape[1]))

        for k in range(self.n_clusters):
            cluster_points = X[labels == k]
            if len(cluster_points) > 0:
                centroids[k] = cluster_points.mean(axis=0)
            else:
                # 如果某个簇为空，保持原中心点不变
                centroids[k] = self.cluster_centers_[k] if self.cluster_centers_ is not None else np.random.randn(X.shape[1])

        return centroids

    def _calculate_inertia(self, X, labels, centroids):
        """
        计算簇内平方和（WCSS - Within-Cluster Sum of Squares）

        WCSS = Σ(i=1 to k) Σ(x ∈ S_i) ||x - c_i||²

        这是K-means算法要最小化的目标函数
        """
        inertia = 0
        for k in range(self.n_clusters):
            cluster_points = X[labels == k]
            if len(cluster_points) > 0:
                inertia += np.sum((cluster_points - centroids[k])**2)
        return inertia

    def fit(self, X):
        """
        训练K-Means模型

        K-Means算法流程：
        1. 初始化K个聚类中心
        2. 重复以下步骤直到收敛：
           a. 将每个样本分配到最近的中心点
           b. 更新每个中心点为其簇内样本的均值
           c. 检查收敛条件（中心点变化小于阈值）
        """
        n_samples, n_features = X.shape

        # 初始化聚类中心
        centroids = self._init_centroids(X)

        for i in range(self.max_iter):
            # 分配样本到聚类
            labels = self._assign_clusters(X, centroids)

            # 更新聚类中心
            new_centroids = self._update_centroids(X, labels)

            # 检查收敛
            center_shift = np.sum((new_centroids - centroids)**2)
            if center_shift < self.tol:
                break

            centroids = new_centroids

        # 保存结果
        self.cluster_centers_ = centroids
        self.labels_ = labels
        self.inertia_ = self._calculate_inertia(X, labels, centroids)
        self.n_iter_ = i + 1

        return self

    def predict(self, X):
        """预测新样本的聚类标签"""
        if self.cluster_centers_ is None:
            raise ValueError("模型尚未训练")

        return self._assign_clusters(X, self.cluster_centers_)

    def fit_predict(self, X):
        """训练模型并返回聚类标签"""
        return self.fit(X).labels_

# K-Means应用场景演示
def kmeans_applications_demo():
    """K-Means在不同场景的应用演示"""

    print("=== K-Means聚类应用场景演示 ===")

    # 场景1：客户分群分析
    print("\n场景1：电商客户分群分析")
    print("目标：根据购买行为将客户分为不同群体，制定精准营销策略")

    # 生成模拟客户数据
    np.random.seed(42)
    n_customers = 1000

    # 客户特征：年消费金额、购买频次、平均订单金额、会员年限
    # 创建3个不同的客户群体

    # 高价值客户（20%）：高消费、高频次
    high_value = np.random.multivariate_normal(
        [8000, 50, 200, 3],
        [[1000000, 500, 2000, 0.5], [500, 100, 200, 0.2],
         [2000, 200, 1000, 0.3], [0.5, 0.2, 0.3, 0.5]],
        int(n_customers * 0.2)
    )

    # 中等价值客户（50%）：中等消费、中等频次
    medium_value = np.random.multivariate_normal(
        [3000, 20, 150, 2],
        [[400000, 200, 1000, 0.3], [200, 50, 100, 0.1],
         [1000, 100, 500, 0.2], [0.3, 0.1, 0.2, 0.3]],
        int(n_customers * 0.5)
    )

    # 低价值客户（30%）：低消费、低频次
    low_value = np.random.multivariate_normal(
        [800, 5, 80, 1],
        [[100000, 50, 200, 0.1], [50, 10, 20, 0.05],
         [200, 20, 100, 0.1], [0.1, 0.05, 0.1, 0.1]],
        int(n_customers * 0.3)
    )

    # 合并数据
    customer_data = np.vstack([high_value, medium_value, low_value])
    feature_names = ['年消费金额', '购买频次', '平均订单金额', '会员年限']

    # 数据标准化（K-means对尺度敏感）
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    customer_data_scaled = scaler.fit_transform(customer_data)

    # 使用肘部法则确定最优K值
    print("使用肘部法则确定最优聚类数：")
    K_range = range(2, 11)
    inertias = []

    for k in K_range:
        kmeans = KMeansComplete(n_clusters=k, random_state=42)
        kmeans.fit(customer_data_scaled)
        inertias.append(kmeans.inertia_)
        print(f"K={k}: WCSS={kmeans.inertia_:.2f}")

    # 训练最终模型（假设选择K=3）
    kmeans_final = KMeansComplete(n_clusters=3, random_state=42)
    customer_labels = kmeans_final.fit_predict(customer_data_scaled)

    # 分析每个客户群体的特征
    print(f"\n客户分群结果分析：")
    for cluster_id in range(3):
        cluster_mask = customer_labels == cluster_id
        cluster_data = customer_data[cluster_mask]
        cluster_size = np.sum(cluster_mask)

        print(f"\n群体 {cluster_id + 1} ({cluster_size}人, {cluster_size/n_customers*100:.1f}%):")
        for i, feature in enumerate(feature_names):
            mean_val = np.mean(cluster_data[:, i])
            print(f"  {feature}: {mean_val:.1f}")

    # 场景2：图像颜色量化
    print(f"\n场景2：图像颜色量化")
    print("目标：将图像的颜色数量减少到K种主要颜色")

    # 创建一个模拟的RGB图像数据
    np.random.seed(42)
    # 模拟一个100x100的彩色图像，每个像素有RGB三个通道
    image_height, image_width = 50, 50

    # 创建几个主要颜色区域
    image = np.zeros((image_height, image_width, 3))

    # 红色区域
    image[:20, :20] = [200, 50, 50]
    # 绿色区域
    image[20:40, :20] = [50, 200, 50]
    # 蓝色区域
    image[40:, :20] = [50, 50, 200]
    # 黄色区域
    image[:20, 20:40] = [200, 200, 50]
    # 紫色区域
    image[20:40, 20:40] = [200, 50, 200]
    # 青色区域
    image[40:, 20:40] = [50, 200, 200]
    # 随机噪声区域
    image[:, 40:] = np.random.randint(0, 255, (image_height, 10, 3))

    # 将图像重塑为像素点的集合
    pixels = image.reshape(-1, 3)

    # 使用K-means进行颜色量化
    n_colors = 6  # 量化到6种颜色
    kmeans_color = KMeansComplete(n_clusters=n_colors, random_state=42)
    color_labels = kmeans_color.fit_predict(pixels)

    # 用聚类中心替换原始颜色
    quantized_pixels = kmeans_color.cluster_centers_[color_labels]
    quantized_image = quantized_pixels.reshape(image_height, image_width, 3)

    print(f"原始图像颜色数量: {len(np.unique(pixels.reshape(-1, pixels.shape[-1]), axis=0))}")
    print(f"量化后颜色数量: {n_colors}")
    print(f"主要颜色 (RGB):")
    for i, color in enumerate(kmeans_color.cluster_centers_):
        print(f"  颜色 {i+1}: ({color[0]:.0f}, {color[1]:.0f}, {color[2]:.0f})")

kmeans_applications_demo()

### 4.1.2 DBSCAN密度聚类算法

DBSCAN（Density-Based Spatial Clustering of Applications with Noise）是一种基于密度的聚类算法，能够发现任意形状的聚类并识别噪声点。

```python
class DBSCANComplete:
    """DBSCAN聚类的完整实现"""

    def __init__(self, eps=0.5, min_samples=5, metric='euclidean'):
        """
        DBSCAN参数说明：
        - eps: 邻域半径，定义了两点被认为是邻居的最大距离
        - min_samples: 核心点的最小邻居数量（包括自己）
        - metric: 距离度量方法

        DBSCAN核心概念：
        1. 核心点：邻域内至少有min_samples个点的点
        2. 边界点：不是核心点但在某个核心点的邻域内
        3. 噪声点：既不是核心点也不是边界点
        """
        self.eps = eps
        self.min_samples = min_samples
        self.metric = metric

        self.labels_ = None
        self.core_sample_indices_ = None
        self.n_clusters_ = 0

    def _get_neighbors(self, X, point_idx):
        """
        获取指定点的所有邻居

        邻居定义：距离小于等于eps的所有点
        """
        distances = np.sqrt(np.sum((X - X[point_idx])**2, axis=1))
        return np.where(distances <= self.eps)[0]

    def fit(self, X):
        """
        DBSCAN聚类算法实现

        算法流程：
        1. 对每个未访问的点：
           a. 标记为已访问
           b. 获取其邻居
           c. 如果邻居数量 >= min_samples，标记为核心点
           d. 如果是核心点，开始形成新簇
        2. 对于每个核心点的邻居：
           a. 如果未访问，递归处理
           b. 如果未分配到任何簇，加入当前簇
        """
        n_samples = X.shape[0]

        # 初始化标签（-1表示噪声点）
        labels = np.full(n_samples, -1, dtype=int)
        visited = np.zeros(n_samples, dtype=bool)

        cluster_id = 0
        core_samples = []

        for point_idx in range(n_samples):
            if visited[point_idx]:
                continue

            visited[point_idx] = True

            # 获取邻居
            neighbors = self._get_neighbors(X, point_idx)

            # 检查是否为核心点
            if len(neighbors) < self.min_samples:
                # 标记为噪声点（暂时）
                labels[point_idx] = -1
            else:
                # 核心点，开始新簇
                core_samples.append(point_idx)
                labels[point_idx] = cluster_id

                # 扩展簇
                self._expand_cluster(X, labels, visited, neighbors, cluster_id)
                cluster_id += 1

        self.labels_ = labels
        self.core_sample_indices_ = np.array(core_samples)
        self.n_clusters_ = cluster_id

        return self

    def _expand_cluster(self, X, labels, visited, neighbors, cluster_id):
        """
        扩展簇：将核心点的所有密度可达点加入同一簇

        密度可达：如果存在一条路径，路径上每个点都是核心点，
        且相邻点之间的距离都小于eps，则称终点密度可达起点
        """
        i = 0
        while i < len(neighbors):
            neighbor_idx = neighbors[i]

            if not visited[neighbor_idx]:
                visited[neighbor_idx] = True

                # 获取邻居的邻居
                neighbor_neighbors = self._get_neighbors(X, neighbor_idx)

                # 如果邻居也是核心点，将其邻居加入待处理列表
                if len(neighbor_neighbors) >= self.min_samples:
                    neighbors = np.concatenate([neighbors, neighbor_neighbors])

            # 如果邻居还未分配到任何簇，将其加入当前簇
            if labels[neighbor_idx] == -1:
                labels[neighbor_idx] = cluster_id

            i += 1

    def fit_predict(self, X):
        """训练并返回聚类标签"""
        return self.fit(X).labels_

# DBSCAN应用场景演示
def dbscan_applications_demo():
    """DBSCAN在不同场景的应用演示"""

    print("=== DBSCAN聚类应用场景演示 ===")

    # 场景1：异常检测
    print("\n场景1：网络入侵检测")
    print("目标：识别网络流量中的异常行为模式")

    # 生成模拟网络流量数据
    np.random.seed(42)
    n_normal = 800
    n_anomaly = 50

    # 正常流量：聚集在几个区域
    normal_cluster1 = np.random.multivariate_normal([2, 2], [[0.5, 0.1], [0.1, 0.5]], n_normal//3)
    normal_cluster2 = np.random.multivariate_normal([6, 6], [[0.8, 0.2], [0.2, 0.8]], n_normal//3)
    normal_cluster3 = np.random.multivariate_normal([2, 6], [[0.6, 0.0], [0.0, 0.6]], n_normal//3)

    # 异常流量：分散的离群点
    anomaly_points = np.random.uniform(0, 8, (n_anomaly, 2))

    # 合并数据
    network_data = np.vstack([normal_cluster1, normal_cluster2, normal_cluster3, anomaly_points])
    true_labels = np.hstack([
        np.zeros(n_normal//3), np.ones(n_normal//3), np.full(n_normal//3, 2),
        np.full(n_anomaly, -1)  # 异常点标记为-1
    ])

    # 使用DBSCAN进行聚类
    dbscan = DBSCANComplete(eps=0.8, min_samples=10)
    cluster_labels = dbscan.fit_predict(network_data)

    # 分析结果
    n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
    n_noise = list(cluster_labels).count(-1)

    print(f"检测到的聚类数量: {n_clusters}")
    print(f"检测到的异常点数量: {n_noise}")
    print(f"实际异常点数量: {n_anomaly}")

    # 计算异常检测准确率
    detected_anomalies = (cluster_labels == -1)
    actual_anomalies = (true_labels == -1)

    true_positives = np.sum(detected_anomalies & actual_anomalies)
    false_positives = np.sum(detected_anomalies & ~actual_anomalies)
    false_negatives = np.sum(~detected_anomalies & actual_anomalies)

    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0

    print(f"异常检测精确率: {precision:.3f}")
    print(f"异常检测召回率: {recall:.3f}")

    # 场景2：地理位置聚类
    print(f"\n场景2：商店选址分析")
    print("目标：根据人口密度数据确定商店的最佳选址区域")

    # 生成模拟城市人口分布数据
    np.random.seed(42)

    # 高密度居住区
    residential_area1 = np.random.multivariate_normal([10, 10], [[2, 0.5], [0.5, 2]], 200)
    residential_area2 = np.random.multivariate_normal([25, 15], [[3, 1], [1, 3]], 150)
    residential_area3 = np.random.multivariate_normal([15, 25], [[2.5, 0.8], [0.8, 2.5]], 180)

    # 商业区
    business_district = np.random.multivariate_normal([20, 20], [[1.5, 0.3], [0.3, 1.5]], 100)

    # 稀疏区域
    sparse_points = np.random.uniform(0, 35, (80, 2))

    # 合并所有数据点
    city_data = np.vstack([residential_area1, residential_area2, residential_area3,
                          business_district, sparse_points])

    # 使用DBSCAN识别高密度区域
    dbscan_city = DBSCANComplete(eps=2.0, min_samples=15)
    area_labels = dbscan_city.fit_predict(city_data)

    # 分析聚类结果
    n_dense_areas = len(set(area_labels)) - (1 if -1 in area_labels else 0)
    n_sparse_points = list(area_labels).count(-1)

    print(f"识别的高密度区域数量: {n_dense_areas}")
    print(f"稀疏区域点数: {n_sparse_points}")

    # 为每个高密度区域计算统计信息
    print(f"\n各区域分析:")
    for cluster_id in set(area_labels):
        if cluster_id == -1:
            continue

        cluster_points = city_data[area_labels == cluster_id]
        cluster_center = np.mean(cluster_points, axis=0)
        cluster_size = len(cluster_points)

        print(f"区域 {cluster_id + 1}:")
        print(f"  中心位置: ({cluster_center[0]:.1f}, {cluster_center[1]:.1f})")
        print(f"  覆盖人口: {cluster_size}")
        print(f"  建议商店数量: {max(1, cluster_size // 50)}")

dbscan_applications_demo()

### 4.1.3 层次聚类算法

层次聚类构建聚类的层次结构，分为凝聚式（自底向上）和分裂式（自顶向下）两种方法。

```python
class HierarchicalClustering:
    """层次聚类的完整实现"""

    def __init__(self, n_clusters=2, linkage='ward', distance_threshold=None):
        """
        层次聚类参数说明：
        - n_clusters: 最终聚类数量
        - linkage: 链接准则，决定簇间距离的计算方法
          * 'single': 单链接（最近点距离）
          * 'complete': 全链接（最远点距离）
          * 'average': 平均链接（平均距离）
          * 'ward': Ward方法（最小化方差）
        - distance_threshold: 距离阈值，用于自动确定聚类数
        """
        self.n_clusters = n_clusters
        self.linkage = linkage
        self.distance_threshold = distance_threshold

        self.labels_ = None
        self.children_ = None  # 合并历史
        self.distances_ = None  # 合并时的距离
        self.n_clusters_ = None

    def _calculate_distance_matrix(self, X):
        """计算样本间的距离矩阵"""
        n_samples = X.shape[0]
        distances = np.zeros((n_samples, n_samples))

        for i in range(n_samples):
            for j in range(i+1, n_samples):
                dist = np.sqrt(np.sum((X[i] - X[j])**2))
                distances[i, j] = distances[j, i] = dist

        return distances

    def _cluster_distance(self, cluster1_indices, cluster2_indices, distance_matrix):
        """
        计算两个簇之间的距离

        不同链接准则的距离计算：
        - single: min(d(i,j)) for i in cluster1, j in cluster2
        - complete: max(d(i,j)) for i in cluster1, j in cluster2
        - average: mean(d(i,j)) for i in cluster1, j in cluster2
        """
        distances = []
        for i in cluster1_indices:
            for j in cluster2_indices:
                distances.append(distance_matrix[i, j])

        if self.linkage == 'single':
            return min(distances)
        elif self.linkage == 'complete':
            return max(distances)
        elif self.linkage == 'average':
            return np.mean(distances)
        elif self.linkage == 'ward':
            # Ward方法需要特殊处理，这里简化为平均距离
            return np.mean(distances)
        else:
            raise ValueError(f"不支持的链接方法: {self.linkage}")

    def fit(self, X):
        """
        凝聚式层次聚类算法

        算法流程：
        1. 初始化：每个样本为一个簇
        2. 重复以下步骤直到达到目标簇数：
           a. 找到距离最近的两个簇
           b. 合并这两个簇
           c. 更新簇间距离
        """
        n_samples = X.shape[0]

        # 计算距离矩阵
        distance_matrix = self._calculate_distance_matrix(X)

        # 初始化：每个样本为一个簇
        clusters = [[i] for i in range(n_samples)]
        merge_history = []
        merge_distances = []

        # 当前簇数
        current_n_clusters = n_samples

        while current_n_clusters > 1:
            # 如果指定了距离阈值，检查是否应该停止
            if self.distance_threshold is not None:
                min_distance = float('inf')
                for i in range(len(clusters)):
                    for j in range(i+1, len(clusters)):
                        dist = self._cluster_distance(clusters[i], clusters[j], distance_matrix)
                        if dist < min_distance:
                            min_distance = dist

                if min_distance > self.distance_threshold:
                    break

            # 如果指定了聚类数，检查是否达到目标
            if (self.distance_threshold is None and
                self.n_clusters is not None and
                current_n_clusters <= self.n_clusters):
                break

            # 找到距离最近的两个簇
            min_distance = float('inf')
            merge_i, merge_j = -1, -1

            for i in range(len(clusters)):
                for j in range(i+1, len(clusters)):
                    dist = self._cluster_distance(clusters[i], clusters[j], distance_matrix)
                    if dist < min_distance:
                        min_distance = dist
                        merge_i, merge_j = i, j

            # 合并簇
            merged_cluster = clusters[merge_i] + clusters[merge_j]

            # 记录合并历史
            merge_history.append([merge_i, merge_j])
            merge_distances.append(min_distance)

            # 更新簇列表
            new_clusters = []
            for k, cluster in enumerate(clusters):
                if k != merge_i and k != merge_j:
                    new_clusters.append(cluster)
            new_clusters.append(merged_cluster)

            clusters = new_clusters
            current_n_clusters -= 1

        # 生成最终标签
        labels = np.zeros(n_samples, dtype=int)
        for cluster_id, cluster in enumerate(clusters):
            for sample_idx in cluster:
                labels[sample_idx] = cluster_id

        self.labels_ = labels
        self.children_ = merge_history
        self.distances_ = merge_distances
        self.n_clusters_ = len(clusters)

        return self

    def fit_predict(self, X):
        """训练并返回聚类标签"""
        return self.fit(X).labels_

# 层次聚类应用演示
def hierarchical_clustering_demo():
    """层次聚类应用场景演示"""

    print("=== 层次聚类应用场景演示 ===")

    # 场景1：基因表达数据聚类
    print("\n场景1：基因表达谱聚类分析")
    print("目标：根据基因表达模式对基因进行分组，发现功能相关的基因簇")

    # 生成模拟基因表达数据
    np.random.seed(42)
    n_genes = 50
    n_conditions = 10  # 不同实验条件

    # 创建几个功能相关的基因组
    # 代谢相关基因（高表达）
    metabolism_genes = np.random.normal(8, 1, (15, n_conditions))

    # 免疫相关基因（中等表达）
    immune_genes = np.random.normal(5, 1.5, (20, n_conditions))

    # 结构蛋白基因（低表达）
    structural_genes = np.random.normal(2, 0.8, (15, n_conditions))

    # 合并基因表达数据
    gene_expression = np.vstack([metabolism_genes, immune_genes, structural_genes])
    gene_names = [f"Gene_{i+1}" for i in range(n_genes)]

    # 使用不同链接方法进行层次聚类
    linkage_methods = ['single', 'complete', 'average', 'ward']

    print("不同链接方法的聚类结果比较:")
    for method in linkage_methods:
        hc = HierarchicalClustering(n_clusters=3, linkage=method)
        gene_labels = hc.fit_predict(gene_expression)

        print(f"\n{method.upper()}链接方法:")
        for cluster_id in range(3):
            cluster_genes = [gene_names[i] for i in range(n_genes) if gene_labels[i] == cluster_id]
            avg_expression = np.mean(gene_expression[gene_labels == cluster_id])
            print(f"  簇 {cluster_id + 1} ({len(cluster_genes)}个基因): 平均表达量 {avg_expression:.2f}")

    # 场景2：客户行为层次分析
    print(f"\n场景2：客户行为层次分析")
    print("目标：构建客户行为的层次结构，支持多层次的营销策略")

    # 生成客户行为数据
    np.random.seed(42)
    n_customers = 100

    # 客户特征：网站访问时长、页面浏览数、购买金额、评论数
    # VIP客户
    vip_customers = np.random.multivariate_normal(
        [120, 50, 1000, 10],
        [[400, 50, 5000, 20], [50, 100, 2000, 15],
         [5000, 2000, 100000, 500], [20, 15, 500, 25]],
        30
    )

    # 活跃客户
    active_customers = np.random.multivariate_normal(
        [60, 25, 300, 3],
        [[200, 25, 1000, 5], [25, 50, 500, 8],
         [1000, 500, 10000, 100], [5, 8, 100, 10]],
        40
    )

    # 普通客户
    regular_customers = np.random.multivariate_normal(
        [20, 8, 50, 1],
        [[50, 10, 100, 2], [10, 20, 50, 3],
         [100, 50, 1000, 20], [2, 3, 20, 5]],
        30
    )

    customer_data = np.vstack([vip_customers, active_customers, regular_customers])
    feature_names = ['访问时长(分钟)', '页面浏览数', '购买金额(元)', '评论数']

    # 数据标准化
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    customer_data_scaled = scaler.fit_transform(customer_data)

    # 使用距离阈值自动确定聚类数
    hc_auto = HierarchicalClustering(distance_threshold=2.0, linkage='ward')
    auto_labels = hc_auto.fit_predict(customer_data_scaled)

    print(f"自动确定的聚类数: {hc_auto.n_clusters_}")

    # 分析每个客户群体
    print(f"\n客户群体分析:")
    for cluster_id in range(hc_auto.n_clusters_):
        cluster_mask = auto_labels == cluster_id
        cluster_data = customer_data[cluster_mask]
        cluster_size = np.sum(cluster_mask)

        print(f"\n客户群体 {cluster_id + 1} ({cluster_size}人):")
        for i, feature in enumerate(feature_names):
            mean_val = np.mean(cluster_data[:, i])
            print(f"  {feature}: {mean_val:.1f}")

hierarchical_clustering_demo()

## 4.2 降维算法深度解析

### 4.2.1 主成分分析（PCA）的完整实现

PCA是最重要的降维技术，通过寻找数据的主要变化方向来实现降维。

```python
class PCAComplete:
    """主成分分析的完整实现与分析"""

    def __init__(self, n_components=None, whiten=False):
        """
        PCA参数说明：
        - n_components: 保留的主成分数量，None表示保留所有
        - whiten: 是否对主成分进行白化（标准化方差为1）

        PCA核心思想：
        1. 找到数据方差最大的方向（第一主成分）
        2. 在与第一主成分正交的空间中找到方差最大的方向（第二主成分）
        3. 重复此过程直到找到所有主成分
        """
        self.n_components = n_components
        self.whiten = whiten

        # 训练后的属性
        self.components_ = None          # 主成分（特征向量）
        self.explained_variance_ = None  # 解释方差（特征值）
        self.explained_variance_ratio_ = None  # 解释方差比例
        self.mean_ = None               # 数据均值
        self.n_components_ = None       # 实际保留的主成分数

    def fit(self, X):
        """
        训练PCA模型

        PCA算法步骤：
        1. 数据中心化：X_centered = X - mean(X)
        2. 计算协方差矩阵：C = (1/n) * X_centered^T * X_centered
        3. 特征值分解：C = V * Λ * V^T
        4. 按特征值大小排序特征向量
        5. 选择前k个特征向量作为主成分
        """
        n_samples, n_features = X.shape

        # 1. 数据中心化
        self.mean_ = np.mean(X, axis=0)
        X_centered = X - self.mean_

        # 2. 计算协方差矩阵
        # 注意：这里使用 (n-1) 作为分母（无偏估计）
        cov_matrix = np.cov(X_centered.T)

        # 3. 特征值分解
        eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)

        # 4. 按特征值大小降序排序
        sorted_indices = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[sorted_indices]
        eigenvectors = eigenvectors[:, sorted_indices]

        # 5. 确定保留的主成分数量
        if self.n_components is None:
            self.n_components_ = n_features
        else:
            self.n_components_ = min(self.n_components, n_features)

        # 6. 选择主成分
        self.components_ = eigenvectors[:, :self.n_components_].T
        self.explained_variance_ = eigenvalues[:self.n_components_]

        # 7. 计算解释方差比例
        total_variance = np.sum(eigenvalues)
        self.explained_variance_ratio_ = self.explained_variance_ / total_variance

        return self

    def transform(self, X):
        """
        将数据投影到主成分空间

        变换公式：Y = (X - mean) * V
        其中V是主成分矩阵
        """
        if self.components_ is None:
            raise ValueError("模型尚未训练")

        # 中心化数据
        X_centered = X - self.mean_

        # 投影到主成分空间
        X_transformed = X_centered @ self.components_.T

        # 如果需要白化
        if self.whiten:
            X_transformed = X_transformed / np.sqrt(self.explained_variance_)

        return X_transformed

    def fit_transform(self, X):
        """训练并变换数据"""
        return self.fit(X).transform(X)

    def inverse_transform(self, X_transformed):
        """
        从主成分空间重构原始数据

        重构公式：X_reconstructed = Y * V^T + mean
        """
        if self.components_ is None:
            raise ValueError("模型尚未训练")

        # 如果数据被白化过，需要先反白化
        if self.whiten:
            X_transformed = X_transformed * np.sqrt(self.explained_variance_)

        # 从主成分空间重构
        X_reconstructed = X_transformed @ self.components_ + self.mean_

        return X_reconstructed

    def explained_variance_analysis(self):
        """解释方差分析"""
        if self.explained_variance_ratio_ is None:
            raise ValueError("模型尚未训练")

        cumulative_variance = np.cumsum(self.explained_variance_ratio_)

        analysis = {
            'individual_variance_ratio': self.explained_variance_ratio_,
            'cumulative_variance_ratio': cumulative_variance,
            'n_components_90_percent': np.argmax(cumulative_variance >= 0.9) + 1,
            'n_components_95_percent': np.argmax(cumulative_variance >= 0.95) + 1,
            'n_components_99_percent': np.argmax(cumulative_variance >= 0.99) + 1
        }

        return analysis

# PCA应用场景演示
def pca_applications_demo():
    """PCA在不同场景的应用演示"""

    print("=== PCA降维应用场景演示 ===")

    # 场景1：高维数据可视化
    print("\n场景1：高维客户数据可视化")
    print("目标：将高维客户特征降维到2D进行可视化分析")

    # 生成高维客户数据
    np.random.seed(42)
    n_customers = 300
    n_features = 20  # 20维特征

    # 创建三种不同类型的客户
    # 高价值客户
    high_value = np.random.multivariate_normal(
        np.random.randn(n_features) + 2,  # 整体特征值较高
        np.eye(n_features) * 0.5,
        n_customers // 3
    )

    # 中等价值客户
    medium_value = np.random.multivariate_normal(
        np.random.randn(n_features),      # 中等特征值
        np.eye(n_features) * 0.8,
        n_customers // 3
    )

    # 低价值客户
    low_value = np.random.multivariate_normal(
        np.random.randn(n_features) - 1,  # 整体特征值较低
        np.eye(n_features) * 0.6,
        n_customers // 3
    )

    # 合并数据
    customer_data = np.vstack([high_value, medium_value, low_value])
    true_labels = np.hstack([
        np.zeros(n_customers // 3),      # 高价值客户
        np.ones(n_customers // 3),       # 中等价值客户
        np.full(n_customers // 3, 2)     # 低价值客户
    ])

    # 应用PCA降维
    pca = PCAComplete(n_components=2)
    customer_2d = pca.fit_transform(customer_data)

    # 分析降维效果
    variance_analysis = pca.explained_variance_analysis()

    print(f"原始数据维度: {customer_data.shape[1]}")
    print(f"降维后维度: {customer_2d.shape[1]}")
    print(f"前2个主成分解释的方差比例: {np.sum(variance_analysis['individual_variance_ratio']):.3f}")
    print(f"第1主成分解释方差: {variance_analysis['individual_variance_ratio'][0]:.3f}")
    print(f"第2主成分解释方差: {variance_analysis['individual_variance_ratio'][1]:.3f}")

    # 场景2：图像压缩
    print(f"\n场景2：图像数据压缩")
    print("目标：使用PCA对图像进行有损压缩")

    # 创建一个模拟的灰度图像（64x64像素）
    np.random.seed(42)
    image_size = 32  # 简化为32x32以便演示

    # 创建一个有结构的图像（包含几何形状）
    image = np.zeros((image_size, image_size))

    # 添加圆形
    center = image_size // 2
    y, x = np.ogrid[:image_size, :image_size]
    mask = (x - center)**2 + (y - center)**2 <= (image_size//4)**2
    image[mask] = 200

    # 添加矩形
    image[5:10, 5:15] = 150
    image[20:25, 15:25] = 100

    # 添加噪声
    noise = np.random.normal(0, 10, (image_size, image_size))
    image = np.clip(image + noise, 0, 255)

    # 将图像重塑为向量形式进行PCA
    image_vectors = image.reshape(image_size, -1)  # 每行作为一个样本

    # 应用PCA进行压缩
    compression_ratios = [0.1, 0.3, 0.5, 0.8]

    print(f"原始图像大小: {image_size}x{image_size} = {image_size**2} 像素")

    for ratio in compression_ratios:
        n_components = max(1, int(image_size * ratio))

        pca_compress = PCAComplete(n_components=n_components)
        compressed = pca_compress.fit_transform(image_vectors)
        reconstructed_vectors = pca_compress.inverse_transform(compressed)
        reconstructed_image = reconstructed_vectors.reshape(image_size, image_size)

        # 计算压缩效果
        compression_ratio = n_components / image_size
        mse = np.mean((image - reconstructed_image)**2)
        psnr = 20 * np.log10(255 / np.sqrt(mse)) if mse > 0 else float('inf')

        variance_kept = np.sum(pca_compress.explained_variance_ratio_)

        print(f"\n压缩比 {ratio:.1f} (保留{n_components}个主成分):")
        print(f"  保留方差比例: {variance_kept:.3f}")
        print(f"  均方误差: {mse:.2f}")
        print(f"  峰值信噪比: {psnr:.2f} dB")

    # 场景3：特征选择和数据预处理
    print(f"\n场景3：机器学习特征预处理")
    print("目标：使用PCA去除特征间的相关性，提高模型性能")

    # 生成具有相关性的特征数据
    np.random.seed(42)
    n_samples = 1000

    # 创建基础特征
    base_features = np.random.randn(n_samples, 3)

    # 创建相关特征（线性组合）
    correlated_features = np.column_stack([
        base_features[:, 0] + 0.5 * base_features[:, 1] + np.random.randn(n_samples) * 0.1,
        base_features[:, 1] + 0.3 * base_features[:, 2] + np.random.randn(n_samples) * 0.1,
        base_features[:, 2] + 0.4 * base_features[:, 0] + np.random.randn(n_samples) * 0.1,
        base_features[:, 0] * 2 + np.random.randn(n_samples) * 0.2,
        base_features[:, 1] * 1.5 + np.random.randn(n_samples) * 0.2
    ])

    # 合并所有特征
    all_features = np.column_stack([base_features, correlated_features])

    print(f"原始特征数量: {all_features.shape[1]}")

    # 计算特征间相关性
    correlation_matrix = np.corrcoef(all_features.T)
    high_correlation_pairs = np.sum(np.abs(correlation_matrix) > 0.8) - all_features.shape[1]  # 减去对角线

    print(f"高相关性特征对数量 (|r| > 0.8): {high_correlation_pairs // 2}")

    # 应用PCA去相关
    pca_decorr = PCAComplete()
    features_pca = pca_decorr.fit_transform(all_features)

    # 分析PCA后的特征
    variance_analysis = pca_decorr.explained_variance_analysis()

    print(f"PCA后特征间相关性: 0 (主成分正交)")
    print(f"保留95%方差需要的主成分数: {variance_analysis['n_components_95_percent']}")
    print(f"保留99%方差需要的主成分数: {variance_analysis['n_components_99_percent']}")

    # 展示前几个主成分的贡献
    print(f"\n前5个主成分的方差贡献:")
    for i in range(min(5, len(variance_analysis['individual_variance_ratio']))):
        ratio = variance_analysis['individual_variance_ratio'][i]
        cumulative = variance_analysis['cumulative_variance_ratio'][i]
        print(f"  PC{i+1}: {ratio:.3f} (累积: {cumulative:.3f})")

pca_applications_demo()

### 4.2.2 高斯混合模型（GMM）

GMM是一种概率聚类方法，假设数据来自多个高斯分布的混合。

```python
class GaussianMixtureModel:
    """高斯混合模型的完整实现"""

    def __init__(self, n_components=2, max_iter=100, tol=1e-6, reg_covar=1e-6):
        """
        GMM参数说明：
        - n_components: 高斯分量的数量
        - max_iter: EM算法的最大迭代次数
        - tol: 收敛容忍度
        - reg_covar: 协方差矩阵的正则化参数，防止奇异

        GMM核心思想：
        假设数据来自K个高斯分布的混合：
        p(x) = Σ(k=1 to K) π_k * N(x|μ_k, Σ_k)

        其中：π_k是混合权重，μ_k是均值，Σ_k是协方差矩阵
        """
        self.n_components = n_components
        self.max_iter = max_iter
        self.tol = tol
        self.reg_covar = reg_covar

        # 模型参数
        self.weights_ = None      # 混合权重 π_k
        self.means_ = None        # 均值 μ_k
        self.covariances_ = None  # 协方差矩阵 Σ_k
        self.converged_ = False   # 是否收敛
        self.n_iter_ = 0         # 实际迭代次数
        self.lower_bound_ = None  # 对数似然下界

    def _initialize_parameters(self, X):
        """
        初始化GMM参数

        初始化策略：
        1. 使用K-means初始化均值
        2. 协方差矩阵初始化为单位矩阵
        3. 混合权重初始化为均匀分布
        """
        n_samples, n_features = X.shape

        # 使用K-means初始化均值
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=self.n_components, random_state=42)
        labels = kmeans.fit_predict(X)

        # 初始化参数
        self.weights_ = np.ones(self.n_components) / self.n_components
        self.means_ = np.array([X[labels == k].mean(axis=0) for k in range(self.n_components)])

        # 初始化协方差矩阵
        self.covariances_ = []
        for k in range(self.n_components):
            cluster_data = X[labels == k]
            if len(cluster_data) > 1:
                cov = np.cov(cluster_data.T) + self.reg_covar * np.eye(n_features)
            else:
                cov = np.eye(n_features)
            self.covariances_.append(cov)

        self.covariances_ = np.array(self.covariances_)

    def _multivariate_gaussian_pdf(self, X, mean, covariance):
        """
        计算多元高斯分布的概率密度函数

        PDF公式：
        p(x) = (2π)^(-k/2) * |Σ|^(-1/2) * exp(-1/2 * (x-μ)^T * Σ^(-1) * (x-μ))
        """
        n_features = X.shape[1]
        diff = X - mean

        try:
            # 计算协方差矩阵的逆和行列式
            cov_inv = np.linalg.inv(covariance)
            cov_det = np.linalg.det(covariance)

            # 计算指数项
            exponent = -0.5 * np.sum((diff @ cov_inv) * diff, axis=1)

            # 计算归一化常数
            normalization = 1.0 / np.sqrt((2 * np.pi) ** n_features * cov_det)

            return normalization * np.exp(exponent)

        except np.linalg.LinAlgError:
            # 如果协方差矩阵奇异，返回很小的概率
            return np.full(X.shape[0], 1e-10)

    def _e_step(self, X):
        """
        EM算法的E步：计算后验概率（责任度）

        责任度公式：
        γ(z_nk) = π_k * N(x_n|μ_k, Σ_k) / Σ_j π_j * N(x_n|μ_j, Σ_j)
        """
        n_samples = X.shape[0]
        responsibilities = np.zeros((n_samples, self.n_components))

        # 计算每个分量的概率密度
        for k in range(self.n_components):
            responsibilities[:, k] = (self.weights_[k] *
                                    self._multivariate_gaussian_pdf(X, self.means_[k], self.covariances_[k]))

        # 归一化得到后验概率
        responsibilities_sum = responsibilities.sum(axis=1, keepdims=True)
        responsibilities_sum[responsibilities_sum == 0] = 1e-10  # 避免除零
        responsibilities /= responsibilities_sum

        return responsibilities

    def _m_step(self, X, responsibilities):
        """
        EM算法的M步：更新模型参数

        参数更新公式：
        π_k = (1/N) * Σ_n γ(z_nk)
        μ_k = Σ_n γ(z_nk) * x_n / Σ_n γ(z_nk)
        Σ_k = Σ_n γ(z_nk) * (x_n - μ_k)(x_n - μ_k)^T / Σ_n γ(z_nk)
        """
        n_samples, n_features = X.shape

        # 计算有效样本数
        N_k = responsibilities.sum(axis=0)

        # 更新混合权重
        self.weights_ = N_k / n_samples

        # 更新均值
        for k in range(self.n_components):
            if N_k[k] > 0:
                self.means_[k] = np.sum(responsibilities[:, k:k+1] * X, axis=0) / N_k[k]

        # 更新协方差矩阵
        for k in range(self.n_components):
            if N_k[k] > 0:
                diff = X - self.means_[k]
                weighted_diff = responsibilities[:, k:k+1] * diff
                self.covariances_[k] = (weighted_diff.T @ diff) / N_k[k]

                # 添加正则化项
                self.covariances_[k] += self.reg_covar * np.eye(n_features)

    def _compute_log_likelihood(self, X):
        """计算对数似然"""
        log_likelihood = 0

        for i in range(X.shape[0]):
            sample_likelihood = 0
            for k in range(self.n_components):
                sample_likelihood += (self.weights_[k] *
                                    self._multivariate_gaussian_pdf(X[i:i+1], self.means_[k], self.covariances_[k])[0])

            if sample_likelihood > 0:
                log_likelihood += np.log(sample_likelihood)
            else:
                log_likelihood += -1e10  # 避免log(0)

        return log_likelihood

    def fit(self, X):
        """
        使用EM算法训练GMM

        EM算法流程：
        1. 初始化参数
        2. 重复直到收敛：
           a. E步：计算后验概率
           b. M步：更新参数
           c. 检查收敛条件
        """
        # 初始化参数
        self._initialize_parameters(X)

        prev_log_likelihood = -np.inf

        for i in range(self.max_iter):
            # E步
            responsibilities = self._e_step(X)

            # M步
            self._m_step(X, responsibilities)

            # 计算对数似然
            log_likelihood = self._compute_log_likelihood(X)

            # 检查收敛
            if abs(log_likelihood - prev_log_likelihood) < self.tol:
                self.converged_ = True
                break

            prev_log_likelihood = log_likelihood

        self.n_iter_ = i + 1
        self.lower_bound_ = log_likelihood

        return self

    def predict(self, X):
        """预测样本的聚类标签"""
        responsibilities = self._e_step(X)
        return np.argmax(responsibilities, axis=1)

    def predict_proba(self, X):
        """预测样本属于各个分量的概率"""
        return self._e_step(X)

    def sample(self, n_samples=1):
        """从GMM中采样"""
        if self.weights_ is None:
            raise ValueError("模型尚未训练")

        # 根据混合权重选择分量
        component_indices = np.random.choice(
            self.n_components, size=n_samples, p=self.weights_
        )

        # 从选定的分量中采样
        samples = []
        for i in range(n_samples):
            k = component_indices[i]
            sample = np.random.multivariate_normal(self.means_[k], self.covariances_[k])
            samples.append(sample)

        return np.array(samples)

# GMM应用演示
def gmm_applications_demo():
    """GMM应用场景演示"""

    print("=== 高斯混合模型应用场景演示 ===")

    # 场景1：异常检测
    print("\n场景1：制造业质量控制异常检测")
    print("目标：检测生产过程中的异常产品")

    # 生成正常产品数据（两个主要的质量分布）
    np.random.seed(42)

    # 高质量产品
    high_quality = np.random.multivariate_normal([10, 8], [[1, 0.3], [0.3, 1]], 400)

    # 标准质量产品
    standard_quality = np.random.multivariate_normal([7, 6], [[1.5, -0.2], [-0.2, 1.5]], 300)

    # 异常产品（少量）
    anomalies = np.random.multivariate_normal([4, 12], [[0.5, 0], [0, 0.5]], 30)

    # 合并数据
    all_products = np.vstack([high_quality, standard_quality, anomalies])
    true_labels = np.hstack([
        np.zeros(400),      # 高质量
        np.ones(300),       # 标准质量
        np.full(30, 2)      # 异常
    ])

    # 训练GMM（假设我们知道有2个正常分布）
    gmm = GaussianMixtureModel(n_components=2, max_iter=100)
    gmm.fit(all_products)

    # 计算每个样本的对数概率密度
    log_probs = []
    for i, sample in enumerate(all_products):
        sample_prob = 0
        for k in range(gmm.n_components):
            component_prob = (gmm.weights_[k] *
                            gmm._multivariate_gaussian_pdf(sample.reshape(1, -1),
                                                          gmm.means_[k],
                                                          gmm.covariances_[k])[0])
            sample_prob += component_prob
        log_probs.append(np.log(sample_prob) if sample_prob > 0 else -1e10)

    log_probs = np.array(log_probs)

    # 使用概率阈值检测异常
    threshold = np.percentile(log_probs, 5)  # 最低5%作为异常
    detected_anomalies = log_probs < threshold

    # 评估异常检测效果
    true_anomalies = (true_labels == 2)

    tp = np.sum(detected_anomalies & true_anomalies)
    fp = np.sum(detected_anomalies & ~true_anomalies)
    fn = np.sum(~detected_anomalies & true_anomalies)

    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0

    print(f"GMM训练结果:")
    print(f"  收敛状态: {'是' if gmm.converged_ else '否'}")
    print(f"  迭代次数: {gmm.n_iter_}")
    print(f"  对数似然: {gmm.lower_bound_:.2f}")

    print(f"\n异常检测结果:")
    print(f"  检测到的异常数量: {np.sum(detected_anomalies)}")
    print(f"  实际异常数量: {np.sum(true_anomalies)}")
    print(f"  精确率: {precision:.3f}")
    print(f"  召回率: {recall:.3f}")

    # 场景2：数据生成和增强
    print(f"\n场景2：数据增强和合成数据生成")
    print("目标：学习数据分布并生成新的合成样本")

    # 使用训练好的GMM生成新样本
    synthetic_samples = gmm.sample(n_samples=100)

    print(f"生成的合成样本数量: {len(synthetic_samples)}")
    print(f"原始数据统计:")
    print(f"  均值: [{np.mean(all_products[:700], axis=0)[0]:.2f}, {np.mean(all_products[:700], axis=0)[1]:.2f}]")
    print(f"  标准差: [{np.std(all_products[:700], axis=0)[0]:.2f}, {np.std(all_products[:700], axis=0)[1]:.2f}]")

    print(f"合成数据统计:")
    print(f"  均值: [{np.mean(synthetic_samples, axis=0)[0]:.2f}, {np.mean(synthetic_samples, axis=0)[1]:.2f}]")
    print(f"  标准差: [{np.std(synthetic_samples, axis=0)[0]:.2f}, {np.std(synthetic_samples, axis=0)[1]:.2f}]")

    # 分析GMM学到的分布
    print(f"\nGMM学到的分布参数:")
    for k in range(gmm.n_components):
        print(f"  分量 {k+1}:")
        print(f"    权重: {gmm.weights_[k]:.3f}")
        print(f"    均值: [{gmm.means_[k][0]:.2f}, {gmm.means_[k][1]:.2f}]")
        print(f"    协方差对角线: [{gmm.covariances_[k][0,0]:.2f}, {gmm.covariances_[k][1,1]:.2f}]")

gmm_applications_demo()

### 4.2.3 自编码器（Autoencoder）深度解析

自编码器是一种无监督的神经网络，通过学习数据的压缩表示来实现降维、去噪和生成等任务。

```python
class AutoencoderComplete:
    """自编码器的完整实现"""

    def __init__(self, input_dim, encoding_dims, activation='relu', learning_rate=0.001):
        """
        自编码器参数说明：
        - input_dim: 输入数据的维度
        - encoding_dims: 编码器各层的维度列表，如[128, 64, 32]
        - activation: 激活函数类型
        - learning_rate: 学习率

        自编码器结构：
        输入 -> 编码器 -> 潜在表示 -> 解码器 -> 重构输出

        损失函数：重构误差 = ||x - x̂||²
        """
        self.input_dim = input_dim
        self.encoding_dims = encoding_dims
        self.activation = activation
        self.learning_rate = learning_rate

        # 网络参数
        self.encoder_weights = []
        self.encoder_biases = []
        self.decoder_weights = []
        self.decoder_biases = []

        # 训练历史
        self.training_history = {'loss': []}

        self._initialize_network()

    def _initialize_network(self):
        """
        初始化网络参数

        使用Xavier初始化方法：
        W ~ N(0, 2/(n_in + n_out))
        """
        # 构建编码器层维度
        encoder_layers = [self.input_dim] + self.encoding_dims

        # 初始化编码器参数
        for i in range(len(encoder_layers) - 1):
            n_in, n_out = encoder_layers[i], encoder_layers[i + 1]

            # Xavier初始化
            weight = np.random.randn(n_in, n_out) * np.sqrt(2.0 / (n_in + n_out))
            bias = np.zeros(n_out)

            self.encoder_weights.append(weight)
            self.encoder_biases.append(bias)

        # 初始化解码器参数（编码器的镜像）
        decoder_layers = self.encoding_dims[::-1] + [self.input_dim]

        for i in range(len(decoder_layers) - 1):
            n_in, n_out = decoder_layers[i], decoder_layers[i + 1]

            weight = np.random.randn(n_in, n_out) * np.sqrt(2.0 / (n_in + n_out))
            bias = np.zeros(n_out)

            self.decoder_weights.append(weight)
            self.decoder_biases.append(bias)

    def _activation_function(self, x, derivative=False):
        """
        激活函数及其导数

        支持的激活函数：
        - relu: f(x) = max(0, x)
        - sigmoid: f(x) = 1/(1 + e^(-x))
        - tanh: f(x) = tanh(x)
        """
        if self.activation == 'relu':
            if derivative:
                return (x > 0).astype(float)
            else:
                return np.maximum(0, x)

        elif self.activation == 'sigmoid':
            sigmoid_x = 1 / (1 + np.exp(-np.clip(x, -500, 500)))
            if derivative:
                return sigmoid_x * (1 - sigmoid_x)
            else:
                return sigmoid_x

        elif self.activation == 'tanh':
            if derivative:
                return 1 - np.tanh(x)**2
            else:
                return np.tanh(x)

        else:
            raise ValueError(f"不支持的激活函数: {self.activation}")

    def encode(self, X):
        """
        编码过程：将输入数据压缩到潜在空间

        编码器前向传播：
        h₁ = σ(W₁x + b₁)
        h₂ = σ(W₂h₁ + b₂)
        ...
        z = σ(Wₙhₙ₋₁ + bₙ)
        """
        current_input = X
        activations = [current_input]  # 保存激活值用于反向传播

        for i, (weight, bias) in enumerate(zip(self.encoder_weights, self.encoder_biases)):
            # 线性变换
            linear_output = current_input @ weight + bias

            # 激活函数（最后一层编码器可能不使用激活函数）
            if i < len(self.encoder_weights) - 1:
                current_input = self._activation_function(linear_output)
            else:
                current_input = linear_output  # 潜在表示通常是线性的

            activations.append(current_input)

        return current_input, activations

    def decode(self, encoded_data):
        """
        解码过程：将潜在表示重构为原始数据

        解码器前向传播：
        h₁ = σ(W₁z + b₁)
        h₂ = σ(W₂h₁ + b₂)
        ...
        x̂ = σ(Wₙhₙ₋₁ + bₙ)
        """
        current_input = encoded_data
        activations = [current_input]

        for i, (weight, bias) in enumerate(zip(self.decoder_weights, self.decoder_biases)):
            # 线性变换
            linear_output = current_input @ weight + bias

            # 激活函数（最后一层通常使用sigmoid或线性）
            if i < len(self.decoder_weights) - 1:
                current_input = self._activation_function(linear_output)
            else:
                # 输出层：对于图像数据使用sigmoid，其他数据可能使用线性
                current_input = self._activation_function(linear_output) if self.activation == 'sigmoid' else linear_output

            activations.append(current_input)

        return current_input, activations

    def forward_pass(self, X):
        """完整的前向传播"""
        encoded, encoder_activations = self.encode(X)
        decoded, decoder_activations = self.decode(encoded)

        return decoded, encoded, encoder_activations, decoder_activations

    def compute_loss(self, X, X_reconstructed):
        """
        计算重构损失

        常用损失函数：
        - MSE: L = (1/n) * Σ||x - x̂||²
        - Binary Cross-Entropy: L = -Σ[x*log(x̂) + (1-x)*log(1-x̂)]
        """
        # 使用均方误差作为重构损失
        mse_loss = np.mean((X - X_reconstructed)**2)
        return mse_loss

    def backward_pass(self, X, X_reconstructed, encoder_activations, decoder_activations):
        """
        反向传播算法

        计算所有参数的梯度：
        ∂L/∂W = ∂L/∂a * ∂a/∂z * ∂z/∂W
        """
        batch_size = X.shape[0]

        # 计算输出层误差
        output_error = 2 * (X_reconstructed - X) / batch_size  # MSE的梯度

        # 解码器反向传播
        decoder_weight_gradients = []
        decoder_bias_gradients = []

        current_error = output_error

        for i in reversed(range(len(self.decoder_weights))):
            # 计算权重和偏置的梯度
            if i == len(self.decoder_weights) - 1:
                # 输出层
                prev_activation = decoder_activations[i]
            else:
                # 隐藏层：需要考虑激活函数的导数
                activation_derivative = self._activation_function(decoder_activations[i + 1], derivative=True)
                current_error = current_error * activation_derivative
                prev_activation = decoder_activations[i]

            weight_gradient = prev_activation.T @ current_error
            bias_gradient = np.sum(current_error, axis=0)

            decoder_weight_gradients.insert(0, weight_gradient)
            decoder_bias_gradients.insert(0, bias_gradient)

            # 传播误差到前一层
            if i > 0:
                current_error = current_error @ self.decoder_weights[i].T

        # 编码器反向传播
        encoder_weight_gradients = []
        encoder_bias_gradients = []

        # 从解码器传播过来的误差
        current_error = current_error @ self.decoder_weights[0].T

        for i in reversed(range(len(self.encoder_weights))):
            if i < len(self.encoder_weights) - 1:
                # 隐藏层：考虑激活函数导数
                activation_derivative = self._activation_function(encoder_activations[i + 1], derivative=True)
                current_error = current_error * activation_derivative

            prev_activation = encoder_activations[i]

            weight_gradient = prev_activation.T @ current_error
            bias_gradient = np.sum(current_error, axis=0)

            encoder_weight_gradients.insert(0, weight_gradient)
            encoder_bias_gradients.insert(0, bias_gradient)

            # 传播误差到前一层
            if i > 0:
                current_error = current_error @ self.encoder_weights[i].T

        return encoder_weight_gradients, encoder_bias_gradients, decoder_weight_gradients, decoder_bias_gradients

    def update_parameters(self, encoder_weight_grads, encoder_bias_grads,
                         decoder_weight_grads, decoder_bias_grads):
        """使用梯度下降更新参数"""
        # 更新编码器参数
        for i in range(len(self.encoder_weights)):
            self.encoder_weights[i] -= self.learning_rate * encoder_weight_grads[i]
            self.encoder_biases[i] -= self.learning_rate * encoder_bias_grads[i]

        # 更新解码器参数
        for i in range(len(self.decoder_weights)):
            self.decoder_weights[i] -= self.learning_rate * decoder_weight_grads[i]
            self.decoder_biases[i] -= self.learning_rate * decoder_bias_grads[i]

    def fit(self, X, epochs=100, batch_size=32, verbose=True):
        """
        训练自编码器

        训练过程：
        1. 前向传播：计算重构输出
        2. 计算损失：重构误差
        3. 反向传播：计算梯度
        4. 参数更新：梯度下降
        """
        n_samples = X.shape[0]
        n_batches = (n_samples + batch_size - 1) // batch_size

        for epoch in range(epochs):
            epoch_loss = 0

            # 随机打乱数据
            indices = np.random.permutation(n_samples)
            X_shuffled = X[indices]

            for batch_idx in range(n_batches):
                # 获取批次数据
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, n_samples)
                X_batch = X_shuffled[start_idx:end_idx]

                # 前向传播
                X_reconstructed, encoded, encoder_activations, decoder_activations = self.forward_pass(X_batch)

                # 计算损失
                batch_loss = self.compute_loss(X_batch, X_reconstructed)
                epoch_loss += batch_loss

                # 反向传播
                encoder_weight_grads, encoder_bias_grads, decoder_weight_grads, decoder_bias_grads = \
                    self.backward_pass(X_batch, X_reconstructed, encoder_activations, decoder_activations)

                # 更新参数
                self.update_parameters(encoder_weight_grads, encoder_bias_grads,
                                     decoder_weight_grads, decoder_bias_grads)

            # 记录训练历史
            avg_loss = epoch_loss / n_batches
            self.training_history['loss'].append(avg_loss)

            if verbose and (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.6f}")

        return self

    def transform(self, X):
        """将数据编码到潜在空间"""
        encoded, _ = self.encode(X)
        return encoded

    def reconstruct(self, X):
        """重构数据"""
        reconstructed, _, _, _ = self.forward_pass(X)
        return reconstructed

# 自编码器应用场景演示
def autoencoder_applications_demo():
    """自编码器应用场景演示"""

    print("=== 自编码器应用场景演示 ===")

    # 场景1：数据降维和可视化
    print("\n场景1：高维数据降维可视化")
    print("目标：将高维数据压缩到2D空间进行可视化")

    # 生成高维数据（模拟基因表达数据）
    np.random.seed(42)
    n_samples = 500
    n_genes = 100  # 100个基因

    # 创建三种不同的细胞类型
    # 免疫细胞：某些基因高表达
    immune_cells = np.random.normal(0, 1, (n_samples//3, n_genes))
    immune_cells[:, :20] += 3  # 前20个基因高表达

    # 神经细胞：另一组基因高表达
    neural_cells = np.random.normal(0, 1, (n_samples//3, n_genes))
    neural_cells[:, 20:40] += 3  # 20-40基因高表达

    # 肌肉细胞：第三组基因高表达
    muscle_cells = np.random.normal(0, 1, (n_samples//3, n_genes))
    muscle_cells[:, 40:60] += 3  # 40-60基因高表达

    # 合并数据
    gene_data = np.vstack([immune_cells, neural_cells, muscle_cells])
    cell_types = np.hstack([
        np.zeros(n_samples//3),      # 免疫细胞
        np.ones(n_samples//3),       # 神经细胞
        np.full(n_samples//3, 2)     # 肌肉细胞
    ])

    # 数据标准化
    gene_data = (gene_data - np.mean(gene_data, axis=0)) / (np.std(gene_data, axis=0) + 1e-8)

    # 训练自编码器进行降维
    autoencoder = AutoencoderComplete(
        input_dim=n_genes,
        encoding_dims=[50, 20, 2],  # 逐步降维到2D
        activation='relu',
        learning_rate=0.001
    )

    print("训练自编码器...")
    autoencoder.fit(gene_data, epochs=100, batch_size=32, verbose=False)

    # 获取2D表示
    gene_2d = autoencoder.transform(gene_data)

    # 分析降维效果
    reconstructed = autoencoder.reconstruct(gene_data)
    reconstruction_error = np.mean((gene_data - reconstructed)**2)

    print(f"原始数据维度: {gene_data.shape[1]}")
    print(f"降维后维度: {gene_2d.shape[1]}")
    print(f"重构误差: {reconstruction_error:.6f}")

    # 分析不同细胞类型在2D空间的分布
    for cell_type in range(3):
        mask = cell_types == cell_type
        type_data = gene_2d[mask]
        cell_names = ['免疫细胞', '神经细胞', '肌肉细胞']

        print(f"{cell_names[cell_type]}在2D空间的分布:")
        print(f"  中心位置: ({np.mean(type_data[:, 0]):.2f}, {np.mean(type_data[:, 1]):.2f})")
        print(f"  分布范围: X轴[{np.min(type_data[:, 0]):.2f}, {np.max(type_data[:, 0]):.2f}], "
              f"Y轴[{np.min(type_data[:, 1]):.2f}, {np.max(type_data[:, 1]):.2f}]")

    # 场景2：图像去噪
    print(f"\n场景2：图像去噪自编码器")
    print("目标：学习去除图像中的噪声")

    # 创建简单的二值图像数据
    np.random.seed(42)
    image_size = 28
    n_images = 200

    # 生成简单的几何图形
    clean_images = []

    for i in range(n_images):
        img = np.zeros((image_size, image_size))

        # 随机选择图形类型
        shape_type = np.random.choice(['circle', 'square', 'triangle'])

        if shape_type == 'circle':
            # 圆形
            center = np.random.randint(8, 20, 2)
            radius = np.random.randint(3, 8)
            y, x = np.ogrid[:image_size, :image_size]
            mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            img[mask] = 1

        elif shape_type == 'square':
            # 正方形
            top_left = np.random.randint(5, 15, 2)
            size = np.random.randint(5, 10)
            img[top_left[0]:top_left[0]+size, top_left[1]:top_left[1]+size] = 1

        else:  # triangle
            # 简单三角形（用几个点近似）
            center = np.random.randint(8, 20, 2)
            size = np.random.randint(4, 8)
            for dy in range(-size, size+1):
                width = max(1, size - abs(dy))
                start_x = max(0, center[1] - width//2)
                end_x = min(image_size, center[1] + width//2)
                y_pos = center[0] + dy
                if 0 <= y_pos < image_size:
                    img[y_pos, start_x:end_x] = 1

        clean_images.append(img.flatten())

    clean_images = np.array(clean_images)

    # 添加噪声
    noise_level = 0.2
    noisy_images = clean_images + np.random.normal(0, noise_level, clean_images.shape)
    noisy_images = np.clip(noisy_images, 0, 1)  # 确保像素值在[0,1]范围内

    # 训练去噪自编码器
    denoising_autoencoder = AutoencoderComplete(
        input_dim=image_size * image_size,
        encoding_dims=[400, 200, 100, 50],  # 编码器结构
        activation='sigmoid',  # 适合图像数据
        learning_rate=0.01
    )

    print("训练去噪自编码器...")
    # 训练：输入噪声图像，目标是干净图像
    denoising_autoencoder.fit(noisy_images, epochs=50, batch_size=16, verbose=False)

    # 测试去噪效果
    denoised_images = denoising_autoencoder.reconstruct(noisy_images)

    # 计算去噪效果指标
    # PSNR (Peak Signal-to-Noise Ratio)
    def calculate_psnr(original, reconstructed):
        mse = np.mean((original - reconstructed)**2)
        if mse == 0:
            return float('inf')
        return 20 * np.log10(1.0 / np.sqrt(mse))

    # 计算不同阶段的PSNR
    original_psnr = calculate_psnr(clean_images, noisy_images)
    denoised_psnr = calculate_psnr(clean_images, denoised_images)

    print(f"去噪效果评估:")
    print(f"  噪声图像PSNR: {original_psnr:.2f} dB")
    print(f"  去噪后PSNR: {denoised_psnr:.2f} dB")
    print(f"  PSNR提升: {denoised_psnr - original_psnr:.2f} dB")

    # 分析几个样本的去噪效果
    sample_indices = [0, 1, 2]
    print(f"\n样本去噪效果分析:")

    for idx in sample_indices:
        clean_img = clean_images[idx].reshape(image_size, image_size)
        noisy_img = noisy_images[idx].reshape(image_size, image_size)
        denoised_img = denoised_images[idx].reshape(image_size, image_size)

        # 计算各种误差指标
        noise_mse = np.mean((clean_img - noisy_img)**2)
        denoised_mse = np.mean((clean_img - denoised_img)**2)

        print(f"  样本 {idx + 1}:")
        print(f"    噪声MSE: {noise_mse:.4f}")
        print(f"    去噪MSE: {denoised_mse:.4f}")
        print(f"    误差减少: {((noise_mse - denoised_mse) / noise_mse * 100):.1f}%")

autoencoder_applications_demo()

---

# 第五章：深度学习架构全解析

> **核心理念**: 深度学习通过多层神经网络学习数据的层次化表示，是现代AI的核心技术。理解各种架构的设计原理和适用场景是掌握深度学习的关键。

## 5.1 卷积神经网络（CNN）深度解析

### 5.1.1 CNN的数学原理与实现

卷积神经网络专门设计用于处理具有网格结构的数据，如图像。其核心思想是通过卷积操作提取局部特征。

```python
class ConvolutionalNeuralNetwork:
    """卷积神经网络的完整实现"""

    def __init__(self):
        """
        CNN核心概念：
        1. 卷积层：通过卷积核提取局部特征
        2. 池化层：降低空间维度，增加平移不变性
        3. 全连接层：进行最终的分类或回归

        卷积操作数学定义：
        (f * g)[m,n] = ΣΣ f[i,j] * g[m-i, n-j]

        在深度学习中通常使用互相关操作：
        (f ⋆ g)[m,n] = ΣΣ f[i,j] * g[m+i, n+j]
        """
        self.layers = []
        self.training_history = {'loss': [], 'accuracy': []}

    def conv2d(self, input_data, kernel, stride=1, padding=0):
        """
        二维卷积操作的实现

        参数说明：
        - input_data: 输入特征图 (batch_size, channels, height, width)
        - kernel: 卷积核 (out_channels, in_channels, kernel_height, kernel_width)
        - stride: 步长，控制卷积核移动的距离
        - padding: 填充，在输入周围添加零值

        输出尺寸计算：
        output_height = (input_height + 2*padding - kernel_height) // stride + 1
        output_width = (input_width + 2*padding - kernel_width) // stride + 1
        """
        if len(input_data.shape) == 3:
            # 如果输入是3D，添加batch维度
            input_data = input_data[np.newaxis, ...]

        batch_size, in_channels, in_height, in_width = input_data.shape
        out_channels, _, kernel_height, kernel_width = kernel.shape

        # 计算输出尺寸
        out_height = (in_height + 2*padding - kernel_height) // stride + 1
        out_width = (in_width + 2*padding - kernel_width) // stride + 1

        # 添加padding
        if padding > 0:
            padded_input = np.pad(input_data,
                                ((0, 0), (0, 0), (padding, padding), (padding, padding)),
                                mode='constant', constant_values=0)
        else:
            padded_input = input_data

        # 初始化输出
        output = np.zeros((batch_size, out_channels, out_height, out_width))

        # 执行卷积操作
        for b in range(batch_size):
            for oc in range(out_channels):
                for oh in range(out_height):
                    for ow in range(out_width):
                        # 计算感受野的位置
                        h_start = oh * stride
                        h_end = h_start + kernel_height
                        w_start = ow * stride
                        w_end = w_start + kernel_width

                        # 提取感受野
                        receptive_field = padded_input[b, :, h_start:h_end, w_start:w_end]

                        # 计算卷积结果（所有输入通道的加权和）
                        output[b, oc, oh, ow] = np.sum(receptive_field * kernel[oc])

        return output

    def max_pooling2d(self, input_data, pool_size=2, stride=None):
        """
        最大池化操作

        最大池化的作用：
        1. 降低空间维度，减少计算量
        2. 增加平移不变性
        3. 扩大感受野

        操作：在每个池化窗口中选择最大值
        """
        if stride is None:
            stride = pool_size

        batch_size, channels, in_height, in_width = input_data.shape

        # 计算输出尺寸
        out_height = (in_height - pool_size) // stride + 1
        out_width = (in_width - pool_size) // stride + 1

        # 初始化输出
        output = np.zeros((batch_size, channels, out_height, out_width))

        # 执行最大池化
        for b in range(batch_size):
            for c in range(channels):
                for oh in range(out_height):
                    for ow in range(out_width):
                        h_start = oh * stride
                        h_end = h_start + pool_size
                        w_start = ow * stride
                        w_end = w_start + pool_size

                        # 在池化窗口中找最大值
                        pool_region = input_data[b, c, h_start:h_end, w_start:w_end]
                        output[b, c, oh, ow] = np.max(pool_region)

        return output

    def relu_activation(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)

    def softmax(self, x):
        """Softmax激活函数（用于多分类）"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

    def build_lenet_architecture(self, input_shape, num_classes):
        """
        构建LeNet-5架构（经典CNN架构）

        LeNet-5结构：
        输入(32x32) -> Conv1(6@28x28) -> Pool1(6@14x14) ->
        Conv2(16@10x10) -> Pool2(16@5x5) -> FC1(120) -> FC2(84) -> FC3(10)

        这是深度学习历史上第一个成功的CNN架构
        """
        # 定义网络层
        layers = {
            'conv1': {
                'type': 'conv2d',
                'kernel': np.random.randn(6, input_shape[0], 5, 5) * 0.1,  # 6个5x5卷积核
                'bias': np.zeros(6),
                'stride': 1,
                'padding': 0
            },
            'pool1': {
                'type': 'max_pool2d',
                'pool_size': 2,
                'stride': 2
            },
            'conv2': {
                'type': 'conv2d',
                'kernel': np.random.randn(16, 6, 5, 5) * 0.1,  # 16个5x5卷积核
                'bias': np.zeros(16),
                'stride': 1,
                'padding': 0
            },
            'pool2': {
                'type': 'max_pool2d',
                'pool_size': 2,
                'stride': 2
            },
            'flatten': {
                'type': 'flatten'
            },
            'fc1': {
                'type': 'dense',
                'weights': np.random.randn(400, 120) * 0.1,  # 16*5*5 = 400
                'bias': np.zeros(120)
            },
            'fc2': {
                'type': 'dense',
                'weights': np.random.randn(120, 84) * 0.1,
                'bias': np.zeros(84)
            },
            'fc3': {
                'type': 'dense',
                'weights': np.random.randn(84, num_classes) * 0.1,
                'bias': np.zeros(num_classes)
            }
        }

        return layers

    def forward_pass(self, x, layers):
        """
        前向传播过程

        通过网络的每一层传播输入数据：
        1. 卷积层：特征提取
        2. 激活函数：非线性变换
        3. 池化层：降维和不变性
        4. 全连接层：分类决策
        """
        activations = {'input': x}
        current_input = x

        for layer_name, layer_config in layers.items():
            if layer_config['type'] == 'conv2d':
                # 卷积操作
                conv_output = self.conv2d(current_input, layer_config['kernel'],
                                        layer_config['stride'], layer_config['padding'])
                # 添加偏置
                conv_output += layer_config['bias'][np.newaxis, :, np.newaxis, np.newaxis]
                # ReLU激活
                current_input = self.relu_activation(conv_output)

            elif layer_config['type'] == 'max_pool2d':
                # 最大池化
                current_input = self.max_pooling2d(current_input,
                                                 layer_config['pool_size'],
                                                 layer_config['stride'])

            elif layer_config['type'] == 'flatten':
                # 展平为一维
                batch_size = current_input.shape[0]
                current_input = current_input.reshape(batch_size, -1)

            elif layer_config['type'] == 'dense':
                # 全连接层
                linear_output = current_input @ layer_config['weights'] + layer_config['bias']
                if layer_name == 'fc3':  # 输出层使用softmax
                    current_input = self.softmax(linear_output)
                else:  # 隐藏层使用ReLU
                    current_input = self.relu_activation(linear_output)

            activations[layer_name] = current_input

        return current_input, activations

# CNN应用场景演示
def cnn_applications_demo():
    """CNN应用场景演示"""

    print("=== 卷积神经网络应用场景演示 ===")

    # 场景1：手写数字识别
    print("\n场景1：手写数字识别（MNIST风格）")
    print("目标：构建CNN识别0-9数字")

    # 生成模拟的手写数字数据
    np.random.seed(42)

    def generate_digit_image(digit, size=28):
        """生成模拟的数字图像"""
        img = np.zeros((size, size))

        if digit == 0:
            # 圆形
            center = size // 2
            y, x = np.ogrid[:size, :size]
            mask = ((x - center)**2 + (y - center)**2 <= (size//3)**2) & \
                   ((x - center)**2 + (y - center)**2 >= (size//4)**2)
            img[mask] = 1

        elif digit == 1:
            # 竖线
            center_x = size // 2
            img[:, center_x-1:center_x+2] = 1

        elif digit == 2:
            # 简化的2
            img[2:5, 5:23] = 1  # 上横线
            img[12:15, 5:23] = 1  # 中横线
            img[23:26, 5:23] = 1  # 下横线
            img[5:12, 20:23] = 1  # 右竖线
            img[15:23, 5:8] = 1   # 左竖线

        # 添加噪声和变形
        noise = np.random.normal(0, 0.1, (size, size))
        img = np.clip(img + noise, 0, 1)

        return img

    # 生成训练数据
    n_samples_per_digit = 100
    n_digits = 3  # 简化为3个数字：0, 1, 2

    images = []
    labels = []

    for digit in range(n_digits):
        for _ in range(n_samples_per_digit):
            img = generate_digit_image(digit)
            images.append(img)
            labels.append(digit)

    images = np.array(images)
    labels = np.array(labels)

    # 添加通道维度 (batch_size, channels, height, width)
    images = images[:, np.newaxis, :, :]

    print(f"生成的数据集大小: {images.shape}")
    print(f"标签分布: {np.bincount(labels)}")

    # 构建CNN模型
    cnn = ConvolutionalNeuralNetwork()
    input_shape = (1, 28, 28)  # 单通道28x28图像
    layers = cnn.build_lenet_architecture(input_shape, n_digits)

    # 测试前向传播
    sample_batch = images[:5]  # 取5个样本测试
    predictions, activations = cnn.forward_pass(sample_batch, layers)

    print(f"\n网络结构测试:")
    print(f"输入形状: {sample_batch.shape}")

    # 显示各层的输出形状
    layer_shapes = {
        'conv1': 'Conv2D: 6@24x24',
        'pool1': 'MaxPool: 6@12x12',
        'conv2': 'Conv2D: 16@8x8',
        'pool2': 'MaxPool: 16@4x4',
        'flatten': 'Flatten: 256',
        'fc1': 'Dense: 120',
        'fc2': 'Dense: 84',
        'fc3': 'Dense: 3 (softmax)'
    }

    for layer_name, description in layer_shapes.items():
        if layer_name in activations:
            actual_shape = activations[layer_name].shape
            print(f"  {description} -> 实际输出: {actual_shape}")

    print(f"\n预测结果示例:")
    for i in range(5):
        predicted_probs = predictions[i]
        predicted_class = np.argmax(predicted_probs)
        confidence = predicted_probs[predicted_class]
        actual_class = labels[i]

        print(f"  样本 {i+1}: 预测={predicted_class} (置信度:{confidence:.3f}), 实际={actual_class}")

    # 场景2：特征可视化
    print(f"\n场景2：CNN特征可视化分析")
    print("目标：理解CNN各层学到的特征")

    # 分析第一层卷积核
    conv1_kernels = layers['conv1']['kernel']  # (6, 1, 5, 5)

    print(f"第一层卷积核分析:")
    print(f"  卷积核数量: {conv1_kernels.shape[0]}")
    print(f"  卷积核大小: {conv1_kernels.shape[2]}x{conv1_kernels.shape[3]}")

    # 显示每个卷积核的统计信息
    for i in range(conv1_kernels.shape[0]):
        kernel = conv1_kernels[i, 0]  # 取第一个通道
        kernel_mean = np.mean(kernel)
        kernel_std = np.std(kernel)
        kernel_max = np.max(kernel)
        kernel_min = np.min(kernel)

        print(f"  卷积核 {i+1}: 均值={kernel_mean:.3f}, 标准差={kernel_std:.3f}, "
              f"范围=[{kernel_min:.3f}, {kernel_max:.3f}]")

    # 分析特征图激活
    conv1_activations = activations['conv1']  # (5, 6, 24, 24)

    print(f"\n第一层特征图分析:")
    print(f"  特征图形状: {conv1_activations.shape}")

    # 计算每个特征图的激活统计
    for sample_idx in range(min(3, conv1_activations.shape[0])):
        print(f"  样本 {sample_idx + 1} (真实标签: {labels[sample_idx]}):")

        for channel in range(conv1_activations.shape[1]):
            feature_map = conv1_activations[sample_idx, channel]
            activation_mean = np.mean(feature_map)
            activation_max = np.max(feature_map)
            sparsity = np.mean(feature_map == 0)  # 稀疏性（ReLU导致的零值比例）

            print(f"    特征图 {channel+1}: 平均激活={activation_mean:.3f}, "
                  f"最大激活={activation_max:.3f}, 稀疏性={sparsity:.3f}")

cnn_applications_demo()

## 5.2 循环神经网络（RNN）与序列建模

### 5.2.1 RNN的数学原理与LSTM实现

循环神经网络专门设计用于处理序列数据，通过隐藏状态在时间步之间传递信息。

```python
class RecurrentNeuralNetwork:
    """循环神经网络的完整实现"""

    def __init__(self):
        """
        RNN核心概念：
        1. 隐藏状态：h_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)
        2. 输出：y_t = W_hy * h_t + b_y
        3. 时间展开：将循环结构展开为前馈网络

        RNN的挑战：
        - 梯度消失：长序列中早期信息难以传播
        - 梯度爆炸：梯度可能指数级增长
        """
        self.hidden_size = None
        self.input_size = None
        self.output_size = None
        self.parameters = {}

    def initialize_parameters(self, input_size, hidden_size, output_size):
        """
        初始化RNN参数

        参数矩阵：
        - W_xh: 输入到隐藏层的权重 (input_size, hidden_size)
        - W_hh: 隐藏层到隐藏层的权重 (hidden_size, hidden_size)
        - W_hy: 隐藏层到输出的权重 (hidden_size, output_size)
        - b_h: 隐藏层偏置 (hidden_size,)
        - b_y: 输出层偏置 (output_size,)
        """
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size

        # Xavier初始化
        self.parameters = {
            'W_xh': np.random.randn(input_size, hidden_size) * np.sqrt(2.0 / input_size),
            'W_hh': np.random.randn(hidden_size, hidden_size) * np.sqrt(2.0 / hidden_size),
            'W_hy': np.random.randn(hidden_size, output_size) * np.sqrt(2.0 / hidden_size),
            'b_h': np.zeros(hidden_size),
            'b_y': np.zeros(output_size)
        }

    def tanh_activation(self, x):
        """Tanh激活函数"""
        return np.tanh(x)

    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

    def forward_pass(self, inputs, initial_hidden=None):
        """
        RNN前向传播

        对于序列 x_1, x_2, ..., x_T：
        h_0 = initial_hidden (通常为零向量)
        for t in 1..T:
            h_t = tanh(W_xh @ x_t + W_hh @ h_{t-1} + b_h)
            y_t = softmax(W_hy @ h_t + b_y)
        """
        sequence_length = len(inputs)

        # 初始化隐藏状态
        if initial_hidden is None:
            hidden_state = np.zeros(self.hidden_size)
        else:
            hidden_state = initial_hidden.copy()

        # 存储中间结果
        hidden_states = [hidden_state]
        outputs = []

        # 时间步循环
        for t in range(sequence_length):
            # 计算新的隐藏状态
            hidden_input = (inputs[t] @ self.parameters['W_xh'] +
                          hidden_state @ self.parameters['W_hh'] +
                          self.parameters['b_h'])
            hidden_state = self.tanh_activation(hidden_input)
            hidden_states.append(hidden_state)

            # 计算输出
            output_input = hidden_state @ self.parameters['W_hy'] + self.parameters['b_y']
            output = self.softmax(output_input)
            outputs.append(output)

        return outputs, hidden_states

class LSTMNetwork:
    """LSTM网络的完整实现"""

    def __init__(self):
        """
        LSTM核心思想：
        通过门控机制解决RNN的梯度消失问题

        三个门：
        1. 遗忘门：决定从细胞状态中丢弃什么信息
        2. 输入门：决定什么新信息存储在细胞状态中
        3. 输出门：决定输出什么部分的细胞状态

        LSTM方程：
        f_t = σ(W_f @ [h_{t-1}, x_t] + b_f)  # 遗忘门
        i_t = σ(W_i @ [h_{t-1}, x_t] + b_i)  # 输入门
        C̃_t = tanh(W_C @ [h_{t-1}, x_t] + b_C)  # 候选值
        C_t = f_t * C_{t-1} + i_t * C̃_t  # 细胞状态
        o_t = σ(W_o @ [h_{t-1}, x_t] + b_o)  # 输出门
        h_t = o_t * tanh(C_t)  # 隐藏状态
        """
        self.parameters = {}
        self.input_size = None
        self.hidden_size = None
        self.output_size = None

    def initialize_parameters(self, input_size, hidden_size, output_size):
        """初始化LSTM参数"""
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size

        # 输入维度：[h_{t-1}, x_t]的拼接
        concat_size = hidden_size + input_size

        # 初始化门控参数
        self.parameters = {
            # 遗忘门
            'W_f': np.random.randn(concat_size, hidden_size) * 0.1,
            'b_f': np.ones(hidden_size),  # 初始化为1，倾向于记住信息

            # 输入门
            'W_i': np.random.randn(concat_size, hidden_size) * 0.1,
            'b_i': np.zeros(hidden_size),

            # 候选值
            'W_C': np.random.randn(concat_size, hidden_size) * 0.1,
            'b_C': np.zeros(hidden_size),

            # 输出门
            'W_o': np.random.randn(concat_size, hidden_size) * 0.1,
            'b_o': np.zeros(hidden_size),

            # 输出层
            'W_y': np.random.randn(hidden_size, output_size) * 0.1,
            'b_y': np.zeros(output_size)
        }

    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(x)

    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)

    def lstm_cell_forward(self, x_t, h_prev, C_prev):
        """
        单个LSTM细胞的前向传播

        输入：
        - x_t: 当前时刻的输入
        - h_prev: 前一时刻的隐藏状态
        - C_prev: 前一时刻的细胞状态

        输出：
        - h_t: 当前时刻的隐藏状态
        - C_t: 当前时刻的细胞状态
        """
        # 拼接输入
        concat_input = np.concatenate([h_prev, x_t])

        # 计算门控值
        forget_gate = self.sigmoid(concat_input @ self.parameters['W_f'] + self.parameters['b_f'])
        input_gate = self.sigmoid(concat_input @ self.parameters['W_i'] + self.parameters['b_i'])
        candidate_values = self.tanh(concat_input @ self.parameters['W_C'] + self.parameters['b_C'])
        output_gate = self.sigmoid(concat_input @ self.parameters['W_o'] + self.parameters['b_o'])

        # 更新细胞状态
        C_t = forget_gate * C_prev + input_gate * candidate_values

        # 计算隐藏状态
        h_t = output_gate * self.tanh(C_t)

        # 保存中间值用于反向传播
        cache = {
            'concat_input': concat_input,
            'forget_gate': forget_gate,
            'input_gate': input_gate,
            'candidate_values': candidate_values,
            'output_gate': output_gate,
            'C_prev': C_prev,
            'C_t': C_t,
            'h_prev': h_prev,
            'x_t': x_t
        }

        return h_t, C_t, cache

    def forward_pass(self, inputs, initial_hidden=None, initial_cell=None):
        """
        LSTM前向传播

        处理整个序列，返回所有时刻的输出
        """
        sequence_length = len(inputs)

        # 初始化状态
        if initial_hidden is None:
            h_t = np.zeros(self.hidden_size)
        else:
            h_t = initial_hidden.copy()

        if initial_cell is None:
            C_t = np.zeros(self.hidden_size)
        else:
            C_t = initial_cell.copy()

        # 存储结果
        outputs = []
        hidden_states = [h_t]
        cell_states = [C_t]
        caches = []

        # 时间步循环
        for t in range(sequence_length):
            h_t, C_t, cache = self.lstm_cell_forward(inputs[t], h_t, C_t)

            # 计算输出
            y_t = self.softmax(h_t @ self.parameters['W_y'] + self.parameters['b_y'])

            outputs.append(y_t)
            hidden_states.append(h_t)
            cell_states.append(C_t)
            caches.append(cache)

        return outputs, hidden_states, cell_states, caches

# RNN/LSTM应用场景演示
def rnn_lstm_applications_demo():
    """RNN和LSTM应用场景演示"""

    print("=== 循环神经网络应用场景演示 ===")

    # 场景1：文本生成
    print("\n场景1：字符级文本生成")
    print("目标：训练RNN学习文本模式并生成新文本")

    # 创建简单的字符级数据集
    text_data = "hello world this is a simple text generation example using rnn"

    # 构建字符词汇表
    chars = sorted(list(set(text_data)))
    char_to_idx = {ch: i for i, ch in enumerate(chars)}
    idx_to_char = {i: ch for i, ch in enumerate(chars)}

    vocab_size = len(chars)

    print(f"文本长度: {len(text_data)}")
    print(f"词汇表大小: {vocab_size}")
    print(f"字符集: {chars}")

    # 准备训练数据（字符序列）
    sequence_length = 10
    sequences = []
    targets = []

    for i in range(len(text_data) - sequence_length):
        seq = text_data[i:i + sequence_length]
        target = text_data[i + sequence_length]

        # 转换为one-hot编码
        seq_encoded = []
        for char in seq:
            one_hot = np.zeros(vocab_size)
            one_hot[char_to_idx[char]] = 1
            seq_encoded.append(one_hot)

        target_encoded = np.zeros(vocab_size)
        target_encoded[char_to_idx[target]] = 1

        sequences.append(seq_encoded)
        targets.append(target_encoded)

    sequences = np.array(sequences)
    targets = np.array(targets)

    print(f"训练序列数量: {len(sequences)}")
    print(f"序列长度: {sequence_length}")

    # 构建RNN模型
    rnn = RecurrentNeuralNetwork()
    rnn.initialize_parameters(vocab_size, hidden_size=50, output_size=vocab_size)

    # 测试前向传播
    sample_sequence = sequences[0]
    outputs, hidden_states = rnn.forward_pass(sample_sequence)

    print(f"\nRNN前向传播测试:")
    print(f"输入序列形状: {sample_sequence.shape}")
    print(f"输出数量: {len(outputs)}")
    print(f"每个输出形状: {outputs[0].shape}")
    print(f"隐藏状态数量: {len(hidden_states)}")

    # 文本生成示例
    def generate_text(model, seed_text, length=20):
        """使用训练好的模型生成文本"""
        generated = seed_text
        current_seq = seed_text[-sequence_length:]

        for _ in range(length):
            # 编码当前序列
            seq_encoded = []
            for char in current_seq:
                if char in char_to_idx:
                    one_hot = np.zeros(vocab_size)
                    one_hot[char_to_idx[char]] = 1
                    seq_encoded.append(one_hot)
                else:
                    # 未知字符用空格代替
                    one_hot = np.zeros(vocab_size)
                    one_hot[char_to_idx[' ']] = 1
                    seq_encoded.append(one_hot)

            # 预测下一个字符
            outputs, _ = model.forward_pass(seq_encoded)
            next_char_probs = outputs[-1]

            # 采样下一个字符（使用概率分布）
            next_char_idx = np.random.choice(vocab_size, p=next_char_probs)
            next_char = idx_to_char[next_char_idx]

            generated += next_char
            current_seq = current_seq[1:] + next_char

        return generated

    # 生成文本示例
    seed = "hello worl"
    generated_text = generate_text(rnn, seed, length=30)
    print(f"\n文本生成示例:")
    print(f"种子文本: '{seed}'")
    print(f"生成文本: '{generated_text}'")

    # 场景2：序列分类（情感分析）
    print(f"\n场景2：序列分类 - 情感分析")
    print("目标：使用LSTM对文本序列进行情感分类")

    # 创建模拟的情感分析数据
    positive_words = ["good", "great", "excellent", "amazing", "wonderful", "fantastic"]
    negative_words = ["bad", "terrible", "awful", "horrible", "disappointing", "poor"]
    neutral_words = ["okay", "fine", "normal", "average", "standard", "typical"]

    # 生成训练数据
    def generate_sentiment_data(n_samples=100):
        """生成情感分析训练数据"""
        data = []
        labels = []

        for _ in range(n_samples):
            # 随机选择情感类别
            sentiment = np.random.choice([0, 1, 2])  # 0:负面, 1:中性, 2:正面

            if sentiment == 0:
                words = np.random.choice(negative_words, size=np.random.randint(3, 8))
            elif sentiment == 1:
                words = np.random.choice(neutral_words, size=np.random.randint(3, 8))
            else:
                words = np.random.choice(positive_words, size=np.random.randint(3, 8))

            # 添加一些随机词汇
            if np.random.random() < 0.3:
                random_words = np.random.choice(["the", "is", "was", "very", "quite"],
                                              size=np.random.randint(1, 3))
                words = np.concatenate([words, random_words])

            sentence = " ".join(words)
            data.append(sentence)
            labels.append(sentiment)

        return data, labels

    # 生成数据
    sentences, sentiment_labels = generate_sentiment_data(200)

    print(f"情感分析数据集:")
    print(f"样本数量: {len(sentences)}")
    print(f"标签分布: {np.bincount(sentiment_labels)}")

    # 显示样本
    for i in range(3):
        sentiment_names = ["负面", "中性", "正面"]
        print(f"  样本 {i+1}: '{sentences[i]}' -> {sentiment_names[sentiment_labels[i]]}")

    # 构建词汇表
    all_words = []
    for sentence in sentences:
        all_words.extend(sentence.split())

    word_vocab = sorted(list(set(all_words)))
    word_to_idx = {word: i for i, word in enumerate(word_vocab)}

    print(f"词汇表大小: {len(word_vocab)}")

    # 构建LSTM模型
    lstm = LSTMNetwork()
    lstm.initialize_parameters(len(word_vocab), hidden_size=64, output_size=3)

    # 编码句子
    def encode_sentence(sentence, max_length=10):
        """将句子编码为词汇索引序列"""
        words = sentence.split()[:max_length]  # 截断到最大长度

        encoded = []
        for word in words:
            if word in word_to_idx:
                one_hot = np.zeros(len(word_vocab))
                one_hot[word_to_idx[word]] = 1
                encoded.append(one_hot)

        # 填充到固定长度
        while len(encoded) < max_length:
            encoded.append(np.zeros(len(word_vocab)))

        return encoded

    # 测试LSTM前向传播
    sample_sentence = sentences[0]
    encoded_sentence = encode_sentence(sample_sentence)

    outputs, hidden_states, cell_states, caches = lstm.forward_pass(encoded_sentence)

    print(f"\nLSTM情感分析测试:")
    print(f"样本句子: '{sample_sentence}'")
    print(f"编码后序列长度: {len(encoded_sentence)}")
    print(f"LSTM输出形状: {outputs[-1].shape}")
    print(f"预测概率: {outputs[-1]}")
    print(f"预测类别: {np.argmax(outputs[-1])} ({'负面' if np.argmax(outputs[-1])==0 else '中性' if np.argmax(outputs[-1])==1 else '正面'})")
    print(f"真实类别: {sentiment_labels[0]} ({'负面' if sentiment_labels[0]==0 else '中性' if sentiment_labels[0]==1 else '正面'})")

rnn_lstm_applications_demo()

## 5.3 Transformer架构深度解析

### 5.3.1 注意力机制与Transformer实现

Transformer通过自注意力机制彻底改变了序列建模，成为现代NLP和多模态AI的基础架构。

```python
class TransformerArchitecture:
    """Transformer架构的完整实现"""

    def __init__(self, d_model=512, n_heads=8, d_ff=2048, max_seq_len=1000):
        """
        Transformer核心参数：
        - d_model: 模型维度（嵌入维度）
        - n_heads: 多头注意力的头数
        - d_ff: 前馈网络的隐藏层维度
        - max_seq_len: 最大序列长度

        Transformer核心创新：
        1. 自注意力机制：Attention(Q,K,V) = softmax(QK^T/√d_k)V
        2. 多头注意力：并行计算多个注意力头
        3. 位置编码：为序列添加位置信息
        4. 残差连接和层归一化：稳定训练
        """
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads  # 每个头的维度
        self.d_ff = d_ff
        self.max_seq_len = max_seq_len

        # 初始化参数
        self._initialize_parameters()

    def _initialize_parameters(self):
        """初始化Transformer参数"""
        # 多头注意力参数
        self.W_q = np.random.randn(self.n_heads, self.d_model, self.d_k) * 0.1
        self.W_k = np.random.randn(self.n_heads, self.d_model, self.d_k) * 0.1
        self.W_v = np.random.randn(self.n_heads, self.d_model, self.d_k) * 0.1
        self.W_o = np.random.randn(self.d_model, self.d_model) * 0.1

        # 前馈网络参数
        self.W_ff1 = np.random.randn(self.d_model, self.d_ff) * 0.1
        self.b_ff1 = np.zeros(self.d_ff)
        self.W_ff2 = np.random.randn(self.d_ff, self.d_model) * 0.1
        self.b_ff2 = np.zeros(self.d_model)

        # 层归一化参数
        self.gamma_1 = np.ones(self.d_model)
        self.beta_1 = np.zeros(self.d_model)
        self.gamma_2 = np.ones(self.d_model)
        self.beta_2 = np.zeros(self.d_model)

    def positional_encoding(self, seq_len):
        """
        位置编码

        由于Transformer没有循环结构，需要显式添加位置信息：
        PE(pos, 2i) = sin(pos / 10000^(2i/d_model))
        PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))

        这种设计允许模型学习相对位置关系
        """
        pos_encoding = np.zeros((seq_len, self.d_model))

        for pos in range(seq_len):
            for i in range(0, self.d_model, 2):
                # 计算角度
                angle = pos / np.power(10000, 2 * i / self.d_model)

                # 正弦和余弦
                pos_encoding[pos, i] = np.sin(angle)
                if i + 1 < self.d_model:
                    pos_encoding[pos, i + 1] = np.cos(angle)

        return pos_encoding

    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        """
        缩放点积注意力

        Attention(Q,K,V) = softmax(QK^T / √d_k)V

        步骤：
        1. 计算注意力分数：QK^T
        2. 缩放：除以√d_k防止softmax饱和
        3. 应用掩码（可选）
        4. Softmax归一化
        5. 加权求和：乘以V
        """
        # 计算注意力分数
        scores = np.matmul(Q, K.transpose(-1, -2)) / np.sqrt(self.d_k)

        # 应用掩码（用于防止看到未来信息）
        if mask is not None:
            scores = np.where(mask == 0, -1e9, scores)

        # Softmax归一化
        attention_weights = self.softmax(scores)

        # 加权求和
        output = np.matmul(attention_weights, V)

        return output, attention_weights

    def multi_head_attention(self, X, mask=None):
        """
        多头注意力机制

        核心思想：
        1. 将输入投影到多个子空间（头）
        2. 在每个子空间中计算注意力
        3. 拼接所有头的输出
        4. 通过线性变换得到最终输出

        MultiHead(Q,K,V) = Concat(head_1, ..., head_h)W^O
        where head_i = Attention(QW_i^Q, KW_i^K, VW_i^V)
        """
        batch_size, seq_len, d_model = X.shape

        # 存储所有头的输出
        head_outputs = []
        attention_weights_all = []

        for head in range(self.n_heads):
            # 线性投影得到Q, K, V
            Q = np.matmul(X, self.W_q[head])  # (batch_size, seq_len, d_k)
            K = np.matmul(X, self.W_k[head])
            V = np.matmul(X, self.W_v[head])

            # 计算注意力
            head_output, attention_weights = self.scaled_dot_product_attention(Q, K, V, mask)

            head_outputs.append(head_output)
            attention_weights_all.append(attention_weights)

        # 拼接所有头的输出
        concatenated = np.concatenate(head_outputs, axis=-1)

        # 最终线性变换
        output = np.matmul(concatenated, self.W_o)

        return output, attention_weights_all

    def feed_forward_network(self, X):
        """
        位置前馈网络

        FFN(x) = max(0, xW_1 + b_1)W_2 + b_2

        这是一个两层的全连接网络，中间使用ReLU激活
        """
        # 第一层：线性变换 + ReLU
        hidden = np.maximum(0, np.matmul(X, self.W_ff1) + self.b_ff1)

        # 第二层：线性变换
        output = np.matmul(hidden, self.W_ff2) + self.b_ff2

        return output

    def layer_normalization(self, X, gamma, beta, epsilon=1e-6):
        """
        层归一化

        LayerNorm(x) = γ * (x - μ) / σ + β

        其中μ和σ是在最后一个维度上计算的均值和标准差
        """
        mean = np.mean(X, axis=-1, keepdims=True)
        variance = np.var(X, axis=-1, keepdims=True)

        normalized = (X - mean) / np.sqrt(variance + epsilon)
        output = gamma * normalized + beta

        return output

    def transformer_block(self, X, mask=None):
        """
        Transformer块

        结构：
        1. 多头自注意力 + 残差连接 + 层归一化
        2. 前馈网络 + 残差连接 + 层归一化

        这种设计确保了梯度的稳定传播
        """
        # 多头自注意力
        attention_output, attention_weights = self.multi_head_attention(X, mask)

        # 残差连接 + 层归一化
        X1 = self.layer_normalization(X + attention_output, self.gamma_1, self.beta_1)

        # 前馈网络
        ff_output = self.feed_forward_network(X1)

        # 残差连接 + 层归一化
        X2 = self.layer_normalization(X1 + ff_output, self.gamma_2, self.beta_2)

        return X2, attention_weights

    def softmax(self, x):
        """数值稳定的Softmax"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

    def create_padding_mask(self, seq, pad_token=0):
        """创建填充掩码"""
        return (seq != pad_token).astype(float)

    def create_look_ahead_mask(self, seq_len):
        """创建前瞻掩码（用于解码器）"""
        mask = np.triu(np.ones((seq_len, seq_len)), k=1)
        return (mask == 0).astype(float)

# Transformer应用场景演示
def transformer_applications_demo():
    """Transformer应用场景演示"""

    print("=== Transformer架构应用场景演示 ===")

    # 场景1：机器翻译
    print("\n场景1：机器翻译（英文->中文概念演示）")
    print("目标：使用Transformer进行序列到序列的翻译")

    # 创建简化的翻译数据
    # 实际应用中会使用词嵌入，这里用随机向量模拟
    np.random.seed(42)

    # 模拟词汇表
    en_vocab = ["hello", "world", "good", "morning", "how", "are", "you", "<pad>", "<sos>", "<eos>"]
    zh_vocab = ["你好", "世界", "早上", "好", "怎么", "样", "你", "<pad>", "<sos>", "<eos>"]

    vocab_size = len(en_vocab)
    d_model = 128

    # 创建词嵌入矩阵（随机初始化）
    en_embeddings = np.random.randn(vocab_size, d_model) * 0.1
    zh_embeddings = np.random.randn(vocab_size, d_model) * 0.1

    # 构建Transformer模型
    transformer = TransformerArchitecture(d_model=d_model, n_heads=4, d_ff=256)

    # 模拟输入序列："hello world" -> "你好 世界"
    en_sequence = [0, 1, 9]  # hello, world, <eos>
    zh_sequence = [8, 0, 1, 9]  # <sos>, 你好, 世界, <eos>

    # 转换为嵌入向量
    en_embedded = en_embeddings[en_sequence]  # (3, d_model)
    zh_embedded = zh_embeddings[zh_sequence]  # (4, d_model)

    # 添加批次维度
    en_embedded = en_embedded[np.newaxis, ...]  # (1, 3, d_model)
    zh_embedded = zh_embedded[np.newaxis, ...]  # (1, 4, d_model)

    print(f"英文序列: {[en_vocab[i] for i in en_sequence]}")
    print(f"中文序列: {[zh_vocab[i] for i in zh_sequence]}")
    print(f"英文嵌入形状: {en_embedded.shape}")
    print(f"中文嵌入形状: {zh_embedded.shape}")

    # 添加位置编码
    en_pos_encoding = transformer.positional_encoding(en_embedded.shape[1])
    zh_pos_encoding = transformer.positional_encoding(zh_embedded.shape[1])

    en_input = en_embedded + en_pos_encoding[np.newaxis, ...]
    zh_input = zh_embedded + zh_pos_encoding[np.newaxis, ...]

    # 编码器处理（英文）
    print(f"\n编码器处理:")
    encoder_output, encoder_attention = transformer.transformer_block(en_input)
    print(f"编码器输出形状: {encoder_output.shape}")
    print(f"编码器注意力权重数量: {len(encoder_attention)}")

    # 解码器处理（中文，带前瞻掩码）
    print(f"\n解码器处理:")
    look_ahead_mask = transformer.create_look_ahead_mask(zh_input.shape[1])
    decoder_output, decoder_attention = transformer.transformer_block(zh_input, look_ahead_mask)
    print(f"解码器输出形状: {decoder_output.shape}")
    print(f"前瞻掩码形状: {look_ahead_mask.shape}")

    # 分析注意力模式
    print(f"\n注意力分析:")
    for head_idx, attention_weights in enumerate(encoder_attention):
        print(f"编码器头 {head_idx + 1} 注意力权重形状: {attention_weights.shape}")

        # 显示注意力权重矩阵
        weights = attention_weights[0]  # 取第一个批次
        print(f"  注意力权重矩阵:")
        for i, source_word in enumerate([en_vocab[idx] for idx in en_sequence]):
            attention_str = " ".join([f"{w:.3f}" for w in weights[i]])
            print(f"    {source_word}: [{attention_str}]")

    # 场景2：文本分类
    print(f"\n场景2：文本分类（情感分析）")
    print("目标：使用Transformer对文本进行情感分类")

    # 创建文本分类数据
    sentences = [
        "this movie is great and amazing",
        "terrible film with poor acting",
        "okay movie nothing special",
        "fantastic story and excellent direction"
    ]

    labels = [1, 0, 0, 1]  # 1: 正面, 0: 负面

    # 简化的词汇表
    words = set()
    for sentence in sentences:
        words.update(sentence.split())

    word_vocab = sorted(list(words)) + ["<pad>", "<cls>"]
    word_to_idx = {word: i for i, word in enumerate(word_vocab)}

    print(f"文本分类数据:")
    print(f"句子数量: {len(sentences)}")
    print(f"词汇表大小: {len(word_vocab)}")

    # 编码句子
    max_len = 8
    encoded_sentences = []

    for sentence in sentences:
        # 添加[CLS]标记用于分类
        tokens = ["<cls>"] + sentence.split()[:max_len-1]

        # 转换为索引
        indices = [word_to_idx[token] for token in tokens]

        # 填充到固定长度
        while len(indices) < max_len:
            indices.append(word_to_idx["<pad>"])

        encoded_sentences.append(indices)

    encoded_sentences = np.array(encoded_sentences)

    # 创建词嵌入
    classification_embeddings = np.random.randn(len(word_vocab), d_model) * 0.1

    # 转换为嵌入向量
    embedded_sentences = classification_embeddings[encoded_sentences]  # (4, 8, d_model)

    # 添加位置编码
    pos_encoding = transformer.positional_encoding(max_len)
    classification_input = embedded_sentences + pos_encoding[np.newaxis, ...]

    print(f"\n文本分类处理:")
    print(f"输入形状: {classification_input.shape}")

    # 通过Transformer处理
    classification_output, classification_attention = transformer.transformer_block(classification_input)

    # 使用[CLS]标记的输出进行分类
    cls_outputs = classification_output[:, 0, :]  # 取第一个位置（[CLS]）的输出

    print(f"[CLS]标记输出形状: {cls_outputs.shape}")

    # 简单的分类头（线性层）
    W_classifier = np.random.randn(d_model, 2) * 0.1  # 2个类别
    b_classifier = np.zeros(2)

    logits = cls_outputs @ W_classifier + b_classifier
    probabilities = transformer.softmax(logits)

    print(f"\n分类结果:")
    for i, (sentence, true_label) in enumerate(zip(sentences, labels)):
        pred_label = np.argmax(probabilities[i])
        confidence = probabilities[i][pred_label]

        print(f"句子: '{sentence}'")
        print(f"  真实标签: {'正面' if true_label == 1 else '负面'}")
        print(f"  预测标签: {'正面' if pred_label == 1 else '负面'}")
        print(f"  置信度: {confidence:.3f}")
        print(f"  概率分布: [负面: {probabilities[i][0]:.3f}, 正面: {probabilities[i][1]:.3f}]")

transformer_applications_demo()

---

# 第六章：计算机视觉技术栈

> **核心理念**: 计算机视觉让机器能够"看懂"世界，从基础的图像处理到复杂的场景理解，是AI最直观和应用最广泛的领域之一。

## 6.1 图像处理基础与特征提取

### 6.1.1 传统计算机视觉方法

在深度学习兴起之前，计算机视觉主要依赖手工设计的特征提取器和传统机器学习方法。

```python
class TraditionalComputerVision:
    """传统计算机视觉方法的完整实现"""

    def __init__(self):
        """
        传统CV的核心思想：
        1. 预处理：噪声去除、增强对比度
        2. 特征提取：边缘、角点、纹理等
        3. 特征描述：SIFT、SURF、HOG等
        4. 分类/检测：SVM、随机森林等
        """
        pass

    def gaussian_filter(self, image, sigma=1.0, kernel_size=5):
        """
        高斯滤波器

        高斯核函数：G(x,y) = (1/2πσ²) * exp(-(x²+y²)/2σ²)

        作用：
        - 去除噪声
        - 图像平滑
        - 为后续处理做准备
        """
        # 创建高斯核
        k = kernel_size // 2
        kernel = np.zeros((kernel_size, kernel_size))

        for i in range(kernel_size):
            for j in range(kernel_size):
                x, y = i - k, j - k
                kernel[i, j] = np.exp(-(x**2 + y**2) / (2 * sigma**2))

        # 归一化
        kernel = kernel / np.sum(kernel)

        # 应用卷积
        return self.convolution_2d(image, kernel)

    def convolution_2d(self, image, kernel):
        """二维卷积操作"""
        if len(image.shape) == 3:
            # 彩色图像，对每个通道分别处理
            result = np.zeros_like(image)
            for c in range(image.shape[2]):
                result[:, :, c] = self._convolution_single_channel(image[:, :, c], kernel)
            return result
        else:
            # 灰度图像
            return self._convolution_single_channel(image, kernel)

    def _convolution_single_channel(self, image, kernel):
        """单通道卷积"""
        h, w = image.shape
        kh, kw = kernel.shape

        # 计算填充
        pad_h, pad_w = kh // 2, kw // 2

        # 填充图像
        padded = np.pad(image, ((pad_h, pad_h), (pad_w, pad_w)), mode='reflect')

        # 输出图像
        result = np.zeros_like(image)

        # 执行卷积
        for i in range(h):
            for j in range(w):
                result[i, j] = np.sum(padded[i:i+kh, j:j+kw] * kernel)

        return result

    def sobel_edge_detection(self, image):
        """
        Sobel边缘检测

        Sobel算子：
        Gx = [[-1, 0, 1],     Gy = [[-1, -2, -1],
              [-2, 0, 2],           [ 0,  0,  0],
              [-1, 0, 1]]           [ 1,  2,  1]]

        边缘强度：|G| = √(Gx² + Gy²)
        边缘方向：θ = arctan(Gy/Gx)
        """
        # Sobel核
        sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])
        sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])

        # 计算梯度
        grad_x = self.convolution_2d(image, sobel_x)
        grad_y = self.convolution_2d(image, sobel_y)

        # 计算边缘强度和方向
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        direction = np.arctan2(grad_y, grad_x)

        return magnitude, direction, grad_x, grad_y

    def harris_corner_detection(self, image, k=0.04, threshold=0.01):
        """
        Harris角点检测

        Harris响应函数：R = det(M) - k(trace(M))²

        其中M是结构张量：
        M = [Ix²   IxIy]
            [IxIy  Iy² ]

        角点特征：R值较大的点
        """
        # 计算图像梯度
        sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])
        sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])

        Ix = self.convolution_2d(image, sobel_x)
        Iy = self.convolution_2d(image, sobel_y)

        # 计算结构张量的元素
        Ix2 = Ix * Ix
        Iy2 = Iy * Iy
        Ixy = Ix * Iy

        # 高斯加权
        gaussian_kernel = self.create_gaussian_kernel(3, 1.0)
        Ix2 = self.convolution_2d(Ix2, gaussian_kernel)
        Iy2 = self.convolution_2d(Iy2, gaussian_kernel)
        Ixy = self.convolution_2d(Ixy, gaussian_kernel)

        # 计算Harris响应
        det_M = Ix2 * Iy2 - Ixy * Ixy
        trace_M = Ix2 + Iy2

        R = det_M - k * (trace_M ** 2)

        # 非极大值抑制和阈值化
        corners = self.non_maximum_suppression(R, threshold)

        return corners, R

    def create_gaussian_kernel(self, size, sigma):
        """创建高斯核"""
        kernel = np.zeros((size, size))
        center = size // 2

        for i in range(size):
            for j in range(size):
                x, y = i - center, j - center
                kernel[i, j] = np.exp(-(x**2 + y**2) / (2 * sigma**2))

        return kernel / np.sum(kernel)

    def non_maximum_suppression(self, response, threshold):
        """非极大值抑制"""
        corners = []
        h, w = response.shape

        for i in range(1, h-1):
            for j in range(1, w-1):
                if response[i, j] > threshold:
                    # 检查是否为局部最大值
                    local_max = True
                    for di in [-1, 0, 1]:
                        for dj in [-1, 0, 1]:
                            if response[i+di, j+dj] > response[i, j]:
                                local_max = False
                                break
                        if not local_max:
                            break

                    if local_max:
                        corners.append((i, j, response[i, j]))

        return corners

    def hog_features(self, image, cell_size=8, block_size=2, bins=9):
        """
        HOG (Histogram of Oriented Gradients) 特征

        HOG算法步骤：
        1. 计算梯度：使用Sobel算子
        2. 计算梯度方向直方图：在每个cell中
        3. 块归一化：在每个block中归一化
        4. 特征向量：拼接所有块的特征

        HOG特征对光照变化和小的形变具有鲁棒性
        """
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = np.mean(image, axis=2)
        else:
            gray = image

        # 计算梯度
        magnitude, direction, _, _ = self.sobel_edge_detection(gray)

        # 将方向转换到0-180度
        direction = np.degrees(direction) % 180

        h, w = gray.shape

        # 计算cell数量
        cells_y = h // cell_size
        cells_x = w // cell_size

        # 初始化HOG特征
        hog_features = []

        # 对每个block计算特征
        for block_y in range(cells_y - block_size + 1):
            for block_x in range(cells_x - block_size + 1):
                block_features = []

                # 对block中的每个cell计算直方图
                for cell_y in range(block_y, block_y + block_size):
                    for cell_x in range(block_x, block_x + block_size):
                        # 提取cell区域
                        y_start = cell_y * cell_size
                        y_end = (cell_y + 1) * cell_size
                        x_start = cell_x * cell_size
                        x_end = (cell_x + 1) * cell_size

                        cell_magnitude = magnitude[y_start:y_end, x_start:x_end]
                        cell_direction = direction[y_start:y_end, x_start:x_end]

                        # 计算方向直方图
                        hist = self.compute_histogram(cell_magnitude, cell_direction, bins)
                        block_features.extend(hist)

                # L2归一化
                block_features = np.array(block_features)
                norm = np.linalg.norm(block_features)
                if norm > 0:
                    block_features = block_features / norm

                hog_features.extend(block_features)

        return np.array(hog_features)

    def compute_histogram(self, magnitude, direction, bins):
        """计算梯度方向直方图"""
        hist = np.zeros(bins)
        bin_width = 180.0 / bins

        for i in range(magnitude.shape[0]):
            for j in range(magnitude.shape[1]):
                angle = direction[i, j]
                mag = magnitude[i, j]

                # 计算bin索引
                bin_idx = int(angle / bin_width)
                bin_idx = min(bin_idx, bins - 1)

                # 线性插值
                bin_center = (bin_idx + 0.5) * bin_width
                if angle < bin_center:
                    if bin_idx > 0:
                        weight = (bin_center - angle) / bin_width
                        hist[bin_idx - 1] += weight * mag
                        hist[bin_idx] += (1 - weight) * mag
                    else:
                        hist[bin_idx] += mag
                else:
                    if bin_idx < bins - 1:
                        weight = (angle - bin_center) / bin_width
                        hist[bin_idx] += (1 - weight) * mag
                        hist[bin_idx + 1] += weight * mag
                    else:
                        hist[bin_idx] += mag

        return hist

# 计算机视觉应用场景演示
def computer_vision_applications_demo():
    """计算机视觉应用场景演示"""

    print("=== 计算机视觉应用场景演示 ===")

    # 场景1：人脸检测与识别
    print("\n场景1：人脸检测系统")
    print("目标：使用传统CV方法检测图像中的人脸")

    # 创建模拟人脸图像
    np.random.seed(42)

    def create_face_image(size=64):
        """创建简化的人脸图像"""
        img = np.zeros((size, size))
        center = size // 2

        # 脸部轮廓（椭圆）
        y, x = np.ogrid[:size, :size]
        face_mask = ((x - center)**2 / (size//3)**2 + (y - center)**2 / (size//2.5)**2) <= 1
        img[face_mask] = 0.8

        # 眼睛
        eye_y, eye_x = center - size//6, center - size//8
        img[eye_y-2:eye_y+3, eye_x-3:eye_x+4] = 0.2
        eye_x = center + size//8
        img[eye_y-2:eye_y+3, eye_x-3:eye_x+4] = 0.2

        # 鼻子
        nose_y = center
        img[nose_y-1:nose_y+2, center-1:center+2] = 0.6

        # 嘴巴
        mouth_y = center + size//6
        img[mouth_y-1:mouth_y+2, center-4:center+5] = 0.3

        # 添加噪声
        noise = np.random.normal(0, 0.05, (size, size))
        img = np.clip(img + noise, 0, 1)

        return img

    def create_non_face_image(size=64):
        """创建非人脸图像"""
        # 创建随机纹理
        img = np.random.random((size, size)) * 0.5

        # 添加一些几何形状
        center = size // 2
        y, x = np.ogrid[:size, :size]

        # 随机形状
        shape_type = np.random.choice(['circle', 'square', 'lines'])

        if shape_type == 'circle':
            mask = (x - center)**2 + (y - center)**2 <= (size//4)**2
            img[mask] = 0.8
        elif shape_type == 'square':
            img[center-size//4:center+size//4, center-size//4:center+size//4] = 0.8
        else:
            # 添加一些线条
            img[::4, :] = 0.8
            img[:, ::4] = 0.8

        return img

    # 生成训练数据
    n_faces = 50
    n_non_faces = 50

    face_images = [create_face_image() for _ in range(n_faces)]
    non_face_images = [create_non_face_image() for _ in range(n_non_faces)]

    all_images = face_images + non_face_images
    labels = [1] * n_faces + [0] * n_non_faces  # 1: 人脸, 0: 非人脸

    print(f"生成的数据集:")
    print(f"  人脸图像: {n_faces}")
    print(f"  非人脸图像: {n_non_faces}")
    print(f"  图像尺寸: 64x64")

    # 使用HOG特征进行人脸检测
    cv = TraditionalComputerVision()

    # 提取HOG特征
    hog_features = []
    for img in all_images:
        hog_feat = cv.hog_features(img, cell_size=8, block_size=2, bins=9)
        hog_features.append(hog_feat)

    hog_features = np.array(hog_features)

    print(f"\nHOG特征提取:")
    print(f"  特征维度: {hog_features.shape[1]}")
    print(f"  特征范围: [{np.min(hog_features):.3f}, {np.max(hog_features):.3f}]")

    # 简单的分类器（基于特征统计）
    face_features = hog_features[:n_faces]
    non_face_features = hog_features[n_faces:]

    face_mean = np.mean(face_features, axis=0)
    non_face_mean = np.mean(non_face_features, axis=0)

    # 使用欧几里得距离进行分类
    def classify_face(hog_feat):
        dist_to_face = np.linalg.norm(hog_feat - face_mean)
        dist_to_non_face = np.linalg.norm(hog_feat - non_face_mean)
        return 1 if dist_to_face < dist_to_non_face else 0

    # 测试分类器
    predictions = [classify_face(feat) for feat in hog_features]
    accuracy = np.mean(np.array(predictions) == np.array(labels))

    print(f"\n人脸检测结果:")
    print(f"  分类准确率: {accuracy:.3f}")

    # 混淆矩阵
    tp = sum(1 for p, l in zip(predictions, labels) if p == 1 and l == 1)
    tn = sum(1 for p, l in zip(predictions, labels) if p == 0 and l == 0)
    fp = sum(1 for p, l in zip(predictions, labels) if p == 1 and l == 0)
    fn = sum(1 for p, l in zip(predictions, labels) if p == 0 and l == 1)

    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0

    print(f"  精确率: {precision:.3f}")
    print(f"  召回率: {recall:.3f}")
    print(f"  混淆矩阵: TP={tp}, TN={tn}, FP={fp}, FN={fn}")

    # 场景2：边缘检测与特征分析
    print(f"\n场景2：工业质检 - 缺陷检测")
    print("目标：使用边缘检测识别产品表面缺陷")

    def create_product_image(size=100, has_defect=False):
        """创建产品图像"""
        img = np.ones((size, size)) * 0.7  # 基础背景

        # 添加产品纹理
        for i in range(0, size, 10):
            img[i:i+2, :] = 0.8
            img[:, i:i+2] = 0.8

        if has_defect:
            # 添加缺陷（裂纹或污点）
            defect_type = np.random.choice(['crack', 'spot'])

            if defect_type == 'crack':
                # 裂纹：随机线条
                start_x, start_y = np.random.randint(20, 80, 2)
                for i in range(20):
                    x = start_x + i + np.random.randint(-2, 3)
                    y = start_y + np.random.randint(-1, 2)
                    if 0 <= x < size and 0 <= y < size:
                        img[y, x] = 0.2
            else:
                # 污点：圆形区域
                center_x, center_y = np.random.randint(20, 80, 2)
                y, x = np.ogrid[:size, :size]
                mask = (x - center_x)**2 + (y - center_y)**2 <= 25
                img[mask] = 0.3

        # 添加噪声
        noise = np.random.normal(0, 0.02, (size, size))
        img = np.clip(img + noise, 0, 1)

        return img

    # 生成产品图像
    normal_products = [create_product_image(has_defect=False) for _ in range(20)]
    defective_products = [create_product_image(has_defect=True) for _ in range(20)]

    print(f"产品质检数据:")
    print(f"  正常产品: {len(normal_products)}")
    print(f"  缺陷产品: {len(defective_products)}")

    # 缺陷检测算法
    def detect_defects(image, edge_threshold=0.1):
        """基于边缘检测的缺陷检测"""
        # 高斯平滑
        smoothed = cv.gaussian_filter(image, sigma=1.0)

        # 边缘检测
        magnitude, direction, _, _ = cv.sobel_edge_detection(smoothed)

        # 统计边缘强度
        edge_density = np.mean(magnitude > edge_threshold)
        edge_max = np.max(magnitude)
        edge_std = np.std(magnitude)

        # 简单的异常检测：基于边缘统计
        anomaly_score = edge_density * edge_max + edge_std

        return anomaly_score, magnitude

    # 分析正常产品的边缘特征
    normal_scores = []
    for img in normal_products:
        score, _ = detect_defects(img)
        normal_scores.append(score)

    # 计算正常产品的统计特征
    normal_mean = np.mean(normal_scores)
    normal_std = np.std(normal_scores)
    threshold = normal_mean + 2 * normal_std  # 2σ阈值

    print(f"\n缺陷检测分析:")
    print(f"  正常产品异常分数: {normal_mean:.4f} ± {normal_std:.4f}")
    print(f"  检测阈值: {threshold:.4f}")

    # 测试缺陷检测
    all_test_images = normal_products + defective_products
    test_labels = [0] * len(normal_products) + [1] * len(defective_products)

    detections = []
    for img in all_test_images:
        score, _ = detect_defects(img)
        is_defective = 1 if score > threshold else 0
        detections.append(is_defective)

    # 评估检测性能
    detection_accuracy = np.mean(np.array(detections) == np.array(test_labels))

    # 计算检测指标
    tp_detect = sum(1 for d, l in zip(detections, test_labels) if d == 1 and l == 1)
    tn_detect = sum(1 for d, l in zip(detections, test_labels) if d == 0 and l == 0)
    fp_detect = sum(1 for d, l in zip(detections, test_labels) if d == 1 and l == 0)
    fn_detect = sum(1 for d, l in zip(detections, test_labels) if d == 0 and l == 1)

    precision_detect = tp_detect / (tp_detect + fp_detect) if (tp_detect + fp_detect) > 0 else 0
    recall_detect = tp_detect / (tp_detect + fn_detect) if (tp_detect + fn_detect) > 0 else 0

    print(f"\n缺陷检测结果:")
    print(f"  检测准确率: {detection_accuracy:.3f}")
    print(f"  精确率: {precision_detect:.3f}")
    print(f"  召回率: {recall_detect:.3f}")
    print(f"  检测统计: TP={tp_detect}, TN={tn_detect}, FP={fp_detect}, FN={fn_detect}")

    # 场景3：角点检测与特征匹配
    print(f"\n场景3：图像配准 - 特征点匹配")
    print("目标：使用Harris角点检测进行图像配准")

    def create_reference_image(size=80):
        """创建参考图像"""
        img = np.zeros((size, size))

        # 添加一些几何特征
        # 矩形
        img[20:30, 20:40] = 1.0
        # 圆形
        center = 60
        y, x = np.ogrid[:size, :size]
        mask = (x - center)**2 + (y - center)**2 <= 100
        img[mask] = 0.8
        # L形状
        img[50:70, 10:15] = 0.6
        img[65:70, 10:30] = 0.6

        return img

    def transform_image(img, dx=5, dy=3, rotation=0.1):
        """对图像进行简单变换"""
        # 这里简化实现，实际应用中会使用更复杂的几何变换
        h, w = img.shape
        transformed = np.zeros_like(img)

        cos_r, sin_r = np.cos(rotation), np.sin(rotation)
        center_x, center_y = w // 2, h // 2

        for i in range(h):
            for j in range(w):
                # 旋转变换
                x_rot = cos_r * (j - center_x) - sin_r * (i - center_y) + center_x
                y_rot = sin_r * (j - center_x) + cos_r * (i - center_y) + center_y

                # 平移变换
                x_final = int(x_rot + dx)
                y_final = int(y_rot + dy)

                if 0 <= x_final < w and 0 <= y_final < h:
                    transformed[i, j] = img[y_final, x_final]

        return transformed

    # 创建参考图像和变换图像
    reference_img = create_reference_image()
    transformed_img = transform_image(reference_img, dx=8, dy=5, rotation=0.15)

    # 添加噪声
    reference_img += np.random.normal(0, 0.05, reference_img.shape)
    transformed_img += np.random.normal(0, 0.05, transformed_img.shape)

    # Harris角点检测
    ref_corners, ref_response = cv.harris_corner_detection(reference_img, threshold=0.01)
    trans_corners, trans_response = cv.harris_corner_detection(transformed_img, threshold=0.01)

    print(f"Harris角点检测结果:")
    print(f"  参考图像角点数: {len(ref_corners)}")
    print(f"  变换图像角点数: {len(trans_corners)}")

    # 显示检测到的角点信息
    if len(ref_corners) > 0:
        ref_corners_sorted = sorted(ref_corners, key=lambda x: x[2], reverse=True)
        print(f"  参考图像前5个角点:")
        for i, (y, x, response) in enumerate(ref_corners_sorted[:5]):
            print(f"    角点 {i+1}: 位置({x}, {y}), 响应值={response:.4f}")

    if len(trans_corners) > 0:
        trans_corners_sorted = sorted(trans_corners, key=lambda x: x[2], reverse=True)
        print(f"  变换图像前5个角点:")
        for i, (y, x, response) in enumerate(trans_corners_sorted[:5]):
            print(f"    角点 {i+1}: 位置({x}, {y}), 响应值={response:.4f}")

    # 简单的特征匹配（基于位置距离）
    def match_corners(corners1, corners2, max_distance=15):
        """基于距离的简单角点匹配"""
        matches = []

        for i, (y1, x1, r1) in enumerate(corners1):
            best_match = None
            best_distance = float('inf')

            for j, (y2, x2, r2) in enumerate(corners2):
                distance = np.sqrt((x1 - x2)**2 + (y1 - y2)**2)
                if distance < best_distance and distance < max_distance:
                    best_distance = distance
                    best_match = j

            if best_match is not None:
                matches.append((i, best_match, best_distance))

        return matches

    # 执行角点匹配
    matches = match_corners(ref_corners, trans_corners)

    print(f"\n特征点匹配结果:")
    print(f"  匹配对数: {len(matches)}")

    if len(matches) > 0:
        avg_distance = np.mean([dist for _, _, dist in matches])
        print(f"  平均匹配距离: {avg_distance:.2f} 像素")

        print(f"  前3个匹配对:")
        for i, (ref_idx, trans_idx, dist) in enumerate(matches[:3]):
            ref_y, ref_x, _ = ref_corners[ref_idx]
            trans_y, trans_x, _ = trans_corners[trans_idx]
            print(f"    匹配 {i+1}: ({ref_x}, {ref_y}) -> ({trans_x}, {trans_y}), 距离={dist:.2f}")

computer_vision_applications_demo()

---

# 第七章：自然语言处理技术

> **核心理念**: 自然语言处理让机器能够理解、生成和处理人类语言，是实现人机智能交互的关键技术。

## 7.1 文本预处理与特征工程

### 7.1.1 传统NLP方法与现代技术

从词袋模型到Transformer，NLP技术经历了从统计方法到深度学习的重大变革。

```python
class NaturalLanguageProcessing:
    """自然语言处理的完整实现"""

    def __init__(self):
        """
        NLP核心任务：
        1. 文本预处理：分词、去停用词、词干提取
        2. 特征提取：词袋模型、TF-IDF、词嵌入
        3. 语言模型：N-gram、神经语言模型
        4. 应用任务：分类、情感分析、机器翻译
        """
        self.stop_words = set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at',
                              'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'])
        self.vocabulary = {}
        self.word_to_idx = {}
        self.idx_to_word = {}

    def tokenize(self, text):
        """
        分词处理

        将连续的文本分割成词汇单元：
        - 基于空格和标点符号分割
        - 处理缩写和特殊字符
        - 统一大小写
        """
        import re

        # 转换为小写
        text = text.lower()

        # 移除标点符号，保留字母和数字
        text = re.sub(r'[^a-zA-Z0-9\s]', '', text)

        # 分割成词汇
        tokens = text.split()

        return tokens

    def remove_stop_words(self, tokens):
        """去除停用词"""
        return [token for token in tokens if token not in self.stop_words]

    def stem_words(self, tokens):
        """
        词干提取（简化版Porter Stemmer）

        将词汇还原到词根形式：
        - running -> run
        - better -> better
        - flies -> fli
        """
        def simple_stem(word):
            # 简化的词干提取规则
            if word.endswith('ing'):
                return word[:-3]
            elif word.endswith('ed'):
                return word[:-2]
            elif word.endswith('er'):
                return word[:-2]
            elif word.endswith('est'):
                return word[:-3]
            elif word.endswith('ly'):
                return word[:-2]
            elif word.endswith('s') and len(word) > 3:
                return word[:-1]
            return word

        return [simple_stem(token) for token in tokens]

    def build_vocabulary(self, documents):
        """构建词汇表"""
        word_counts = {}

        for doc in documents:
            tokens = self.tokenize(doc)
            tokens = self.remove_stop_words(tokens)
            tokens = self.stem_words(tokens)

            for token in tokens:
                word_counts[token] = word_counts.get(token, 0) + 1

        # 按频率排序
        sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)

        # 构建词汇映射
        self.vocabulary = dict(sorted_words)
        self.word_to_idx = {word: idx for idx, (word, _) in enumerate(sorted_words)}
        self.idx_to_word = {idx: word for word, idx in self.word_to_idx.items()}

        return self.vocabulary

    def bag_of_words(self, documents):
        """
        词袋模型 (Bag of Words)

        将文档表示为词汇频率向量：
        - 忽略词序信息
        - 只考虑词汇出现频率
        - 高维稀疏向量表示
        """
        if not self.vocabulary:
            self.build_vocabulary(documents)

        vocab_size = len(self.vocabulary)
        bow_matrix = np.zeros((len(documents), vocab_size))

        for doc_idx, doc in enumerate(documents):
            tokens = self.tokenize(doc)
            tokens = self.remove_stop_words(tokens)
            tokens = self.stem_words(tokens)

            for token in tokens:
                if token in self.word_to_idx:
                    word_idx = self.word_to_idx[token]
                    bow_matrix[doc_idx, word_idx] += 1

        return bow_matrix

    def tf_idf(self, documents):
        """
        TF-IDF (Term Frequency-Inverse Document Frequency)

        TF-IDF = TF(t,d) × IDF(t)

        其中：
        - TF(t,d) = (词汇t在文档d中的频率) / (文档d的总词数)
        - IDF(t) = log(总文档数 / 包含词汇t的文档数)

        TF-IDF能够突出重要且具有区分性的词汇
        """
        if not self.vocabulary:
            self.build_vocabulary(documents)

        vocab_size = len(self.vocabulary)
        n_docs = len(documents)

        # 计算TF矩阵
        tf_matrix = np.zeros((n_docs, vocab_size))
        doc_word_counts = []

        for doc_idx, doc in enumerate(documents):
            tokens = self.tokenize(doc)
            tokens = self.remove_stop_words(tokens)
            tokens = self.stem_words(tokens)

            doc_word_counts.append(len(tokens))

            for token in tokens:
                if token in self.word_to_idx:
                    word_idx = self.word_to_idx[token]
                    tf_matrix[doc_idx, word_idx] += 1

        # 归一化TF
        for doc_idx in range(n_docs):
            if doc_word_counts[doc_idx] > 0:
                tf_matrix[doc_idx] /= doc_word_counts[doc_idx]

        # 计算IDF
        idf_vector = np.zeros(vocab_size)

        for word_idx in range(vocab_size):
            # 计算包含该词的文档数
            docs_with_word = np.sum(tf_matrix[:, word_idx] > 0)
            if docs_with_word > 0:
                idf_vector[word_idx] = np.log(n_docs / docs_with_word)

        # 计算TF-IDF
        tfidf_matrix = tf_matrix * idf_vector

        return tfidf_matrix, idf_vector

    def n_gram_model(self, documents, n=2):
        """
        N-gram语言模型

        N-gram模型基于马尔可夫假设：
        P(w_i | w_1, w_2, ..., w_{i-1}) ≈ P(w_i | w_{i-n+1}, ..., w_{i-1})

        用于：
        - 语言建模
        - 文本生成
        - 拼写检查
        """
        ngram_counts = {}
        context_counts = {}

        for doc in documents:
            tokens = self.tokenize(doc)
            tokens = ['<START>'] * (n-1) + tokens + ['<END>']

            for i in range(len(tokens) - n + 1):
                ngram = tuple(tokens[i:i+n])
                context = ngram[:-1]
                word = ngram[-1]

                # 统计n-gram频率
                ngram_counts[ngram] = ngram_counts.get(ngram, 0) + 1
                context_counts[context] = context_counts.get(context, 0) + 1

        # 计算条件概率
        ngram_probs = {}
        for ngram, count in ngram_counts.items():
            context = ngram[:-1]
            ngram_probs[ngram] = count / context_counts[context]

        return ngram_probs, ngram_counts, context_counts

    def generate_text_ngram(self, ngram_probs, context_counts, n=2, max_length=20):
        """使用N-gram模型生成文本"""
        # 从开始标记开始
        current_context = tuple(['<START>'] * (n-1))
        generated_text = []

        for _ in range(max_length):
            # 找到所有可能的下一个词
            possible_words = []
            probabilities = []

            for ngram, prob in ngram_probs.items():
                if ngram[:-1] == current_context:
                    possible_words.append(ngram[-1])
                    probabilities.append(prob)

            if not possible_words:
                break

            # 根据概率选择下一个词
            probabilities = np.array(probabilities)
            probabilities = probabilities / np.sum(probabilities)

            next_word = np.random.choice(possible_words, p=probabilities)

            if next_word == '<END>':
                break

            generated_text.append(next_word)

            # 更新上下文
            current_context = current_context[1:] + (next_word,)

        return ' '.join(generated_text)

# NLP应用场景演示
def nlp_applications_demo():
    """自然语言处理应用场景演示"""

    print("=== 自然语言处理应用场景演示 ===")

    # 场景1：文档分类系统
    print("\n场景1：新闻文档分类")
    print("目标：使用TF-IDF特征对新闻文档进行主题分类")

    # 创建模拟新闻数据
    news_documents = [
        # 科技类
        "Apple releases new iPhone with advanced AI capabilities and improved camera technology",
        "Google announces breakthrough in quantum computing research and development",
        "Microsoft launches new cloud computing platform for artificial intelligence applications",
        "Tesla unveils autonomous driving technology with neural network improvements",
        "Facebook develops new virtual reality headset with enhanced user experience",

        # 体育类
        "Basketball championship finals draw millions of viewers worldwide",
        "Soccer world cup preparations begin with team training sessions",
        "Olympic games feature record-breaking performances in swimming and athletics",
        "Tennis tournament showcases incredible matches between top-ranked players",
        "Baseball season concludes with exciting playoff games and championships",

        # 经济类
        "Stock market reaches new highs amid economic recovery and growth",
        "Federal Reserve announces interest rate changes affecting global markets",
        "Cryptocurrency prices fluctuate as investors react to regulatory news",
        "International trade agreements impact global supply chain operations",
        "Banking sector reports strong quarterly earnings and profit margins"
    ]

    # 对应的标签
    news_labels = [0, 0, 0, 0, 0,  # 科技类
                   1, 1, 1, 1, 1,  # 体育类
                   2, 2, 2, 2, 2]  # 经济类

    label_names = ['科技', '体育', '经济']

    print(f"新闻数据集:")
    print(f"  文档总数: {len(news_documents)}")
    print(f"  类别数量: {len(label_names)}")
    print(f"  类别分布: {np.bincount(news_labels)}")

    # 初始化NLP处理器
    nlp = NaturalLanguageProcessing()

    # 构建词汇表
    vocabulary = nlp.build_vocabulary(news_documents)
    print(f"\n词汇表统计:")
    print(f"  词汇总数: {len(vocabulary)}")
    print(f"  前10个高频词: {list(vocabulary.items())[:10]}")

    # 提取TF-IDF特征
    tfidf_matrix, idf_vector = nlp.tf_idf(news_documents)

    print(f"\nTF-IDF特征:")
    print(f"  特征矩阵形状: {tfidf_matrix.shape}")
    print(f"  特征密度: {np.count_nonzero(tfidf_matrix) / tfidf_matrix.size:.3f}")

    # 分析每个类别的特征词
    print(f"\n各类别特征词分析:")
    for class_id, class_name in enumerate(label_names):
        class_mask = np.array(news_labels) == class_id
        class_tfidf = tfidf_matrix[class_mask]

        # 计算每个词在该类别中的平均TF-IDF
        avg_tfidf = np.mean(class_tfidf, axis=0)

        # 找到最重要的词
        top_word_indices = np.argsort(avg_tfidf)[-5:][::-1]

        print(f"  {class_name}类特征词:")
        for idx in top_word_indices:
            word = nlp.idx_to_word[idx]
            score = avg_tfidf[idx]
            print(f"    {word}: {score:.4f}")

    # 简单的文档分类（基于余弦相似度）
    def classify_document_tfidf(doc_vector, class_centroids):
        """基于余弦相似度的文档分类"""
        similarities = []

        for centroid in class_centroids:
            # 计算余弦相似度
            dot_product = np.dot(doc_vector, centroid)
            norm_doc = np.linalg.norm(doc_vector)
            norm_centroid = np.linalg.norm(centroid)

            if norm_doc > 0 and norm_centroid > 0:
                similarity = dot_product / (norm_doc * norm_centroid)
            else:
                similarity = 0

            similarities.append(similarity)

        return np.argmax(similarities)

    # 计算每个类别的中心点
    class_centroids = []
    for class_id in range(len(label_names)):
        class_mask = np.array(news_labels) == class_id
        class_tfidf = tfidf_matrix[class_mask]
        centroid = np.mean(class_tfidf, axis=0)
        class_centroids.append(centroid)

    # 测试分类性能
    predictions = []
    for i, doc_vector in enumerate(tfidf_matrix):
        pred = classify_document_tfidf(doc_vector, class_centroids)
        predictions.append(pred)

    accuracy = np.mean(np.array(predictions) == np.array(news_labels))

    print(f"\n文档分类结果:")
    print(f"  分类准确率: {accuracy:.3f}")

    # 显示分类详情
    for i in range(min(5, len(news_documents))):
        doc_preview = news_documents[i][:50] + "..."
        true_label = label_names[news_labels[i]]
        pred_label = label_names[predictions[i]]
        correct = "✓" if news_labels[i] == predictions[i] else "✗"

        print(f"  文档 {i+1}: {doc_preview}")
        print(f"    真实类别: {true_label}, 预测类别: {pred_label} {correct}")

    # 场景2：情感分析
    print(f"\n场景2：产品评论情感分析")
    print("目标：分析用户评论的情感倾向")

    # 创建产品评论数据
    reviews = [
        # 正面评论
        "This product is absolutely amazing and exceeded my expectations completely",
        "Excellent quality and fast delivery, highly recommend to everyone",
        "Love this item, works perfectly and great value for money",
        "Outstanding customer service and fantastic product quality",
        "Best purchase I have made this year, totally satisfied",

        # 负面评论
        "Terrible product quality, broke after just one day of use",
        "Worst customer service experience, very disappointed and frustrated",
        "Complete waste of money, product does not work as advertised",
        "Poor quality materials and terrible design, avoid this product",
        "Horrible experience, product arrived damaged and unusable",

        # 中性评论
        "Product is okay, nothing special but does the job adequately",
        "Average quality for the price, meets basic expectations",
        "Decent product with some good features and some limitations",
        "Standard quality item, works as expected without surprises",
        "Fair product for the price range, acceptable performance"
    ]

    sentiment_labels = [1, 1, 1, 1, 1,  # 正面
                       0, 0, 0, 0, 0,  # 负面
                       2, 2, 2, 2, 2]  # 中性

    sentiment_names = ['负面', '正面', '中性']

    print(f"情感分析数据:")
    print(f"  评论总数: {len(reviews)}")
    print(f"  情感分布: {np.bincount(sentiment_labels)}")

    # 构建情感词典（简化版）
    positive_words = ['amazing', 'excellent', 'love', 'outstanding', 'best', 'fantastic',
                     'great', 'perfect', 'satisfied', 'recommend']
    negative_words = ['terrible', 'worst', 'waste', 'poor', 'horrible', 'disappointed',
                     'frustrated', 'damaged', 'broke', 'avoid']

    def sentiment_score_lexicon(text):
        """基于词典的情感分析"""
        tokens = nlp.tokenize(text)

        positive_count = sum(1 for token in tokens if token in positive_words)
        negative_count = sum(1 for token in tokens if token in negative_words)

        # 计算情感分数
        if positive_count > negative_count:
            return 1  # 正面
        elif negative_count > positive_count:
            return 0  # 负面
        else:
            return 2  # 中性

    # 基于词典的情感分析
    lexicon_predictions = [sentiment_score_lexicon(review) for review in reviews]
    lexicon_accuracy = np.mean(np.array(lexicon_predictions) == np.array(sentiment_labels))

    print(f"\n基于词典的情感分析:")
    print(f"  准确率: {lexicon_accuracy:.3f}")

    # 基于TF-IDF的情感分析
    nlp_sentiment = NaturalLanguageProcessing()
    sentiment_tfidf, _ = nlp_sentiment.tf_idf(reviews)

    # 计算情感类别中心点
    sentiment_centroids = []
    for sentiment_id in range(3):
        sentiment_mask = np.array(sentiment_labels) == sentiment_id
        sentiment_vectors = sentiment_tfidf[sentiment_mask]
        centroid = np.mean(sentiment_vectors, axis=0)
        sentiment_centroids.append(centroid)

    # TF-IDF分类
    tfidf_predictions = []
    for doc_vector in sentiment_tfidf:
        pred = classify_document_tfidf(doc_vector, sentiment_centroids)
        tfidf_predictions.append(pred)

    tfidf_accuracy = np.mean(np.array(tfidf_predictions) == np.array(sentiment_labels))

    print(f"基于TF-IDF的情感分析:")
    print(f"  准确率: {tfidf_accuracy:.3f}")

    # 详细分析
    print(f"\n情感分析详细结果:")
    for i in range(min(6, len(reviews))):
        review_preview = reviews[i][:60] + "..."
        true_sentiment = sentiment_names[sentiment_labels[i]]
        lexicon_pred = sentiment_names[lexicon_predictions[i]]
        tfidf_pred = sentiment_names[tfidf_predictions[i]]

        print(f"  评论 {i+1}: {review_preview}")
        print(f"    真实情感: {true_sentiment}")
        print(f"    词典预测: {lexicon_pred}")
        print(f"    TF-IDF预测: {tfidf_pred}")

    # 场景3：文本生成
    print(f"\n场景3：基于N-gram的文本生成")
    print("目标：使用N-gram语言模型生成类似风格的文本")

    # 准备训练文本
    training_texts = [
        "artificial intelligence is transforming the world with machine learning algorithms",
        "machine learning algorithms enable computers to learn from data automatically",
        "deep learning networks process information using artificial neural networks",
        "neural networks mimic the human brain to solve complex problems",
        "data science combines statistics programming and domain expertise effectively",
        "algorithms process large datasets to discover hidden patterns and insights",
        "computer vision enables machines to interpret and understand visual information",
        "natural language processing helps computers understand human language naturally"
    ]

    print(f"文本生成训练数据:")
    print(f"  训练句子数: {len(training_texts)}")

    # 训练不同的N-gram模型
    for n in [2, 3]:
        print(f"\n{n}-gram语言模型:")

        ngram_probs, ngram_counts, context_counts = nlp.n_gram_model(training_texts, n=n)

        print(f"  {n}-gram总数: {len(ngram_counts)}")
        print(f"  上下文数: {len(context_counts)}")

        # 显示一些高频n-gram
        sorted_ngrams = sorted(ngram_counts.items(), key=lambda x: x[1], reverse=True)
        print(f"  高频{n}-gram:")
        for ngram, count in sorted_ngrams[:5]:
            prob = ngram_probs[ngram]
            print(f"    {' '.join(ngram)}: 出现{count}次, 概率={prob:.3f}")

        # 生成文本
        print(f"  生成的文本样例:")
        for i in range(3):
            generated = nlp.generate_text_ngram(ngram_probs, context_counts, n=n, max_length=10)
            print(f"    样例 {i+1}: {generated}")

nlp_applications_demo()

---

# 第八章：模型评估的工业标准

> **核心理念**: 模型评估是机器学习项目成功的关键，需要从多个维度全面评估模型性能，确保模型在实际应用中的可靠性和有效性。

## 8.1 评估指标体系

### 8.1.1 分类任务评估指标

分类任务的评估需要考虑准确率、精确率、召回率等多个维度，不同的业务场景需要关注不同的指标。

```python
class ModelEvaluationMetrics:
    """模型评估指标的完整实现"""

    def __init__(self):
        """
        评估指标的核心思想：
        1. 准确性指标：模型预测的正确程度
        2. 鲁棒性指标：模型对异常数据的抗干扰能力
        3. 效率指标：模型的计算和存储效率
        4. 公平性指标：模型对不同群体的公平性
        """
        pass

    def confusion_matrix(self, y_true, y_pred, labels=None):
        """
        混淆矩阵

        混淆矩阵是分类评估的基础：
                预测
        真实    P    N
          P   TP   FN
          N   FP   TN

        其中：
        - TP: True Positive (真正例)
        - TN: True Negative (真负例)
        - FP: False Positive (假正例)
        - FN: False Negative (假负例)
        """
        if labels is None:
            labels = sorted(list(set(y_true) | set(y_pred)))

        n_labels = len(labels)
        label_to_idx = {label: idx for idx, label in enumerate(labels)}

        # 初始化混淆矩阵
        cm = np.zeros((n_labels, n_labels), dtype=int)

        # 填充混淆矩阵
        for true_label, pred_label in zip(y_true, y_pred):
            true_idx = label_to_idx[true_label]
            pred_idx = label_to_idx[pred_label]
            cm[true_idx, pred_idx] += 1

        return cm, labels

    def classification_metrics(self, y_true, y_pred, average='macro'):
        """
        分类评估指标

        核心指标：
        - Precision = TP / (TP + FP)  # 精确率
        - Recall = TP / (TP + FN)     # 召回率
        - F1-Score = 2 * (Precision * Recall) / (Precision + Recall)
        - Accuracy = (TP + TN) / (TP + TN + FP + FN)
        """
        cm, labels = self.confusion_matrix(y_true, y_pred)
        n_classes = len(labels)

        # 计算每个类别的指标
        precision_per_class = []
        recall_per_class = []
        f1_per_class = []

        for i in range(n_classes):
            tp = cm[i, i]
            fp = np.sum(cm[:, i]) - tp
            fn = np.sum(cm[i, :]) - tp
            tn = np.sum(cm) - tp - fp - fn

            # 精确率
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            precision_per_class.append(precision)

            # 召回率
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            recall_per_class.append(recall)

            # F1分数
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            f1_per_class.append(f1)

        # 计算总体准确率
        accuracy = np.trace(cm) / np.sum(cm)

        # 计算平均指标
        if average == 'macro':
            avg_precision = np.mean(precision_per_class)
            avg_recall = np.mean(recall_per_class)
            avg_f1 = np.mean(f1_per_class)
        elif average == 'weighted':
            # 按类别样本数加权
            class_counts = np.sum(cm, axis=1)
            total_samples = np.sum(class_counts)
            weights = class_counts / total_samples

            avg_precision = np.average(precision_per_class, weights=weights)
            avg_recall = np.average(recall_per_class, weights=weights)
            avg_f1 = np.average(f1_per_class, weights=weights)
        else:
            avg_precision = np.mean(precision_per_class)
            avg_recall = np.mean(recall_per_class)
            avg_f1 = np.mean(f1_per_class)

        return {
            'accuracy': accuracy,
            'precision': avg_precision,
            'recall': avg_recall,
            'f1_score': avg_f1,
            'precision_per_class': precision_per_class,
            'recall_per_class': recall_per_class,
            'f1_per_class': f1_per_class,
            'confusion_matrix': cm,
            'labels': labels
        }

    def roc_auc_analysis(self, y_true, y_scores):
        """
        ROC曲线和AUC分析

        ROC曲线：
        - X轴：False Positive Rate = FP / (FP + TN)
        - Y轴：True Positive Rate = TP / (TP + FN)

        AUC (Area Under Curve)：
        - AUC = 1: 完美分类器
        - AUC = 0.5: 随机分类器
        - AUC < 0.5: 比随机还差
        """
        # 获取不同阈值
        thresholds = np.unique(y_scores)
        thresholds = np.sort(thresholds)[::-1]  # 降序排列

        tpr_list = []  # True Positive Rate
        fpr_list = []  # False Positive Rate

        for threshold in thresholds:
            y_pred = (y_scores >= threshold).astype(int)

            # 计算TP, FP, TN, FN
            tp = np.sum((y_true == 1) & (y_pred == 1))
            fp = np.sum((y_true == 0) & (y_pred == 1))
            tn = np.sum((y_true == 0) & (y_pred == 0))
            fn = np.sum((y_true == 1) & (y_pred == 0))

            # 计算TPR和FPR
            tpr = tp / (tp + fn) if (tp + fn) > 0 else 0
            fpr = fp / (fp + tn) if (fp + tn) > 0 else 0

            tpr_list.append(tpr)
            fpr_list.append(fpr)

        # 计算AUC（使用梯形法则）
        auc = 0
        for i in range(1, len(fpr_list)):
            auc += (fpr_list[i] - fpr_list[i-1]) * (tpr_list[i] + tpr_list[i-1]) / 2

        return {
            'fpr': fpr_list,
            'tpr': tpr_list,
            'thresholds': thresholds,
            'auc': abs(auc)  # 取绝对值确保AUC为正
        }

    def precision_recall_curve(self, y_true, y_scores):
        """
        Precision-Recall曲线

        适用于不平衡数据集：
        - 关注正类的预测性能
        - PR曲线下面积(AP)是重要指标
        """
        thresholds = np.unique(y_scores)
        thresholds = np.sort(thresholds)[::-1]

        precision_list = []
        recall_list = []

        for threshold in thresholds:
            y_pred = (y_scores >= threshold).astype(int)

            tp = np.sum((y_true == 1) & (y_pred == 1))
            fp = np.sum((y_true == 0) & (y_pred == 1))
            fn = np.sum((y_true == 1) & (y_pred == 0))

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0

            precision_list.append(precision)
            recall_list.append(recall)

        # 计算Average Precision (AP)
        ap = 0
        for i in range(1, len(recall_list)):
            ap += (recall_list[i-1] - recall_list[i]) * precision_list[i]

        return {
            'precision': precision_list,
            'recall': recall_list,
            'thresholds': thresholds,
            'average_precision': ap
        }

    def regression_metrics(self, y_true, y_pred):
        """
        回归评估指标

        核心指标：
        - MAE: Mean Absolute Error
        - MSE: Mean Squared Error
        - RMSE: Root Mean Squared Error
        - R²: Coefficient of Determination
        - MAPE: Mean Absolute Percentage Error
        """
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)

        # 基本误差指标
        mae = np.mean(np.abs(y_true - y_pred))
        mse = np.mean((y_true - y_pred)**2)
        rmse = np.sqrt(mse)

        # R²决定系数
        ss_res = np.sum((y_true - y_pred)**2)
        ss_tot = np.sum((y_true - np.mean(y_true))**2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

        # MAPE (避免除零)
        mask = y_true != 0
        mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100 if np.any(mask) else 0

        # 调整R²
        n = len(y_true)
        p = 1  # 假设单变量回归
        adj_r2 = 1 - (1 - r2) * (n - 1) / (n - p - 1) if n > p + 1 else r2

        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'adjusted_r2': adj_r2,
            'mape': mape
        }

### 8.1.2 交叉验证与模型选择

交叉验证是评估模型泛化能力的金标准，能够更可靠地估计模型在未见数据上的性能。

```python
class CrossValidationFramework:
    """交叉验证框架的完整实现"""

    def __init__(self):
        """
        交叉验证的核心思想：
        1. 将数据分成多个折(fold)
        2. 轮流使用不同折作为验证集
        3. 其余折作为训练集
        4. 计算平均性能和方差

        常见的交叉验证方法：
        - K-Fold CV: 标准k折交叉验证
        - Stratified K-Fold: 保持类别比例的k折
        - Leave-One-Out: 留一法
        - Time Series Split: 时间序列分割
        """
        pass

    def k_fold_split(self, X, y, k=5, shuffle=True, random_state=None):
        """
        K折交叉验证数据分割

        将数据分成k个大小相近的折：
        - 每次使用k-1个折训练，1个折验证
        - 重复k次，每个折都作为验证集一次
        """
        n_samples = len(X)
        indices = np.arange(n_samples)

        if shuffle:
            if random_state is not None:
                np.random.seed(random_state)
            np.random.shuffle(indices)

        # 计算每个折的大小
        fold_sizes = [n_samples // k] * k
        for i in range(n_samples % k):
            fold_sizes[i] += 1

        # 生成折的索引
        folds = []
        start_idx = 0

        for fold_size in fold_sizes:
            end_idx = start_idx + fold_size
            fold_indices = indices[start_idx:end_idx]
            folds.append(fold_indices)
            start_idx = end_idx

        # 生成训练/验证分割
        splits = []
        for i in range(k):
            val_indices = folds[i]
            train_indices = np.concatenate([folds[j] for j in range(k) if j != i])

            X_train = X[train_indices] if hasattr(X, '__getitem__') else [X[idx] for idx in train_indices]
            X_val = X[val_indices] if hasattr(X, '__getitem__') else [X[idx] for idx in val_indices]
            y_train = y[train_indices] if hasattr(y, '__getitem__') else [y[idx] for idx in train_indices]
            y_val = y[val_indices] if hasattr(y, '__getitem__') else [y[idx] for idx in val_indices]

            splits.append((X_train, X_val, y_train, y_val))

        return splits

    def stratified_k_fold_split(self, X, y, k=5, random_state=None):
        """
        分层K折交叉验证

        确保每个折中各类别的比例与原数据集相同：
        - 适用于不平衡数据集
        - 保持类别分布的一致性
        """
        # 获取类别和对应的索引
        unique_classes = np.unique(y)
        class_indices = {cls: np.where(y == cls)[0] for cls in unique_classes}

        # 为每个类别创建k个折
        class_folds = {}
        for cls in unique_classes:
            indices = class_indices[cls]
            if random_state is not None:
                np.random.seed(random_state)
            np.random.shuffle(indices)

            # 分割成k个折
            n_samples = len(indices)
            fold_sizes = [n_samples // k] * k
            for i in range(n_samples % k):
                fold_sizes[i] += 1

            folds = []
            start_idx = 0
            for fold_size in fold_sizes:
                end_idx = start_idx + fold_size
                folds.append(indices[start_idx:end_idx])
                start_idx = end_idx

            class_folds[cls] = folds

        # 合并各类别的折
        splits = []
        for i in range(k):
            val_indices = np.concatenate([class_folds[cls][i] for cls in unique_classes])
            train_indices = np.concatenate([
                np.concatenate([class_folds[cls][j] for j in range(k) if j != i])
                for cls in unique_classes
            ])

            X_train = X[train_indices] if hasattr(X, '__getitem__') else [X[idx] for idx in train_indices]
            X_val = X[val_indices] if hasattr(X, '__getitem__') else [X[idx] for idx in val_indices]
            y_train = y[train_indices] if hasattr(y, '__getitem__') else [y[idx] for idx in train_indices]
            y_val = y[val_indices] if hasattr(y, '__getitem__') else [y[idx] for idx in val_indices]

            splits.append((X_train, X_val, y_train, y_val))

        return splits

    def cross_validate_model(self, model_class, model_params, X, y, cv_method='k_fold',
                           k=5, scoring='accuracy', random_state=None):
        """
        执行交叉验证

        参数：
        - model_class: 模型类
        - model_params: 模型参数
        - X, y: 训练数据
        - cv_method: 交叉验证方法
        - k: 折数
        - scoring: 评分方法
        """
        # 获取数据分割
        if cv_method == 'k_fold':
            splits = self.k_fold_split(X, y, k=k, random_state=random_state)
        elif cv_method == 'stratified':
            splits = self.stratified_k_fold_split(X, y, k=k, random_state=random_state)
        else:
            raise ValueError(f"不支持的交叉验证方法: {cv_method}")

        # 存储每折的结果
        fold_scores = []
        fold_predictions = []
        fold_true_labels = []

        for fold_idx, (X_train, X_val, y_train, y_val) in enumerate(splits):
            # 训练模型
            model = model_class(**model_params)

            # 根据模型类型调用不同的训练方法
            if hasattr(model, 'fit'):
                model.fit(X_train, y_train)
            else:
                # 自定义模型可能有不同的训练接口
                model.train(X_train, y_train)

            # 预测
            if hasattr(model, 'predict'):
                y_pred = model.predict(X_val)
            else:
                y_pred = model.predict(X_val)

            # 计算评分
            if scoring == 'accuracy':
                score = np.mean(y_pred == y_val)
            elif scoring == 'f1':
                # 简化的F1计算
                tp = np.sum((y_val == 1) & (y_pred == 1))
                fp = np.sum((y_val == 0) & (y_pred == 1))
                fn = np.sum((y_val == 1) & (y_pred == 0))

                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            else:
                raise ValueError(f"不支持的评分方法: {scoring}")

            fold_scores.append(score)
            fold_predictions.extend(y_pred)
            fold_true_labels.extend(y_val)

        # 计算统计结果
        cv_results = {
            'scores': fold_scores,
            'mean_score': np.mean(fold_scores),
            'std_score': np.std(fold_scores),
            'predictions': fold_predictions,
            'true_labels': fold_true_labels
        }

        return cv_results

    def grid_search_cv(self, model_class, param_grid, X, y, cv_method='k_fold',
                      k=5, scoring='accuracy', random_state=None):
        """
        网格搜索交叉验证

        自动搜索最佳超参数组合：
        1. 遍历所有参数组合
        2. 对每个组合进行交叉验证
        3. 选择性能最佳的参数组合
        """
        # 生成所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())

        # 计算笛卡尔积
        from itertools import product
        param_combinations = list(product(*param_values))

        best_score = -np.inf
        best_params = None
        all_results = []

        print(f"网格搜索开始，共{len(param_combinations)}个参数组合...")

        for i, param_combo in enumerate(param_combinations):
            # 构建参数字典
            current_params = dict(zip(param_names, param_combo))

            print(f"  测试参数组合 {i+1}/{len(param_combinations)}: {current_params}")

            # 执行交叉验证
            cv_results = self.cross_validate_model(
                model_class, current_params, X, y,
                cv_method=cv_method, k=k, scoring=scoring, random_state=random_state
            )

            mean_score = cv_results['mean_score']
            std_score = cv_results['std_score']

            print(f"    平均得分: {mean_score:.4f} (±{std_score:.4f})")

            # 记录结果
            result = {
                'params': current_params,
                'mean_score': mean_score,
                'std_score': std_score,
                'cv_results': cv_results
            }
            all_results.append(result)

            # 更新最佳结果
            if mean_score > best_score:
                best_score = mean_score
                best_params = current_params

        print(f"\n网格搜索完成！")
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.4f}")

        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': all_results
        }

# 模型评估综合演示
def model_evaluation_demo():
    """模型评估综合演示"""

    print("=== 模型评估综合演示 ===")

    # 生成模拟数据
    np.random.seed(42)

    # 场景1：二分类问题评估
    print("\n场景1：二分类模型评估")
    print("目标：全面评估二分类模型的性能")

    # 生成不平衡的二分类数据
    n_samples = 1000
    n_positive = 200  # 正类样本较少
    n_negative = 800  # 负类样本较多

    # 正类数据（集中分布）
    positive_data = np.random.multivariate_normal([2, 2], [[1, 0.3], [0.3, 1]], n_positive)
    positive_labels = np.ones(n_positive)

    # 负类数据（分散分布）
    negative_data = np.random.multivariate_normal([0, 0], [[2, 0.5], [0.5, 2]], n_negative)
    negative_labels = np.zeros(n_negative)

    # 合并数据
    X = np.vstack([positive_data, negative_data])
    y = np.hstack([positive_labels, negative_labels])

    print(f"数据集统计:")
    print(f"  总样本数: {len(X)}")
    print(f"  正类样本: {n_positive} ({n_positive/len(X)*100:.1f}%)")
    print(f"  负类样本: {n_negative} ({n_negative/len(X)*100:.1f}%)")

    # 模拟模型预测结果
    # 创建一个简单的线性分类器
    def simple_classifier(X, threshold=0.5):
        # 基于特征的线性组合进行分类
        scores = 0.8 * X[:, 0] + 0.6 * X[:, 1] + np.random.normal(0, 0.3, len(X))
        probabilities = 1 / (1 + np.exp(-scores))  # sigmoid
        predictions = (probabilities > threshold).astype(int)
        return predictions, probabilities

    y_pred, y_scores = simple_classifier(X)

    # 初始化评估器
    evaluator = ModelEvaluationMetrics()

    # 计算分类指标
    classification_results = evaluator.classification_metrics(y, y_pred)

    print(f"\n分类性能指标:")
    print(f"  准确率: {classification_results['accuracy']:.4f}")
    print(f"  精确率: {classification_results['precision']:.4f}")
    print(f"  召回率: {classification_results['recall']:.4f}")
    print(f"  F1分数: {classification_results['f1_score']:.4f}")

    # 显示混淆矩阵
    cm = classification_results['confusion_matrix']
    print(f"\n混淆矩阵:")
    print(f"        预测")
    print(f"真实   0    1")
    print(f"  0   {cm[0,0]:4d} {cm[0,1]:4d}")
    print(f"  1   {cm[1,0]:4d} {cm[1,1]:4d}")

    # ROC-AUC分析
    roc_results = evaluator.roc_auc_analysis(y, y_scores)
    print(f"\nROC-AUC分析:")
    print(f"  AUC值: {roc_results['auc']:.4f}")

    # PR曲线分析
    pr_results = evaluator.precision_recall_curve(y, y_scores)
    print(f"  平均精确率(AP): {pr_results['average_precision']:.4f}")

    # 场景2：交叉验证演示
    print(f"\n场景2：交叉验证模型选择")
    print("目标：使用交叉验证选择最佳模型参数")

    # 定义一个简单的模型类用于演示
    class SimpleLogisticRegression:
        def __init__(self, learning_rate=0.01, max_iter=100, regularization=0.0):
            self.learning_rate = learning_rate
            self.max_iter = max_iter
            self.regularization = regularization
            self.weights = None
            self.bias = None

        def sigmoid(self, z):
            return 1 / (1 + np.exp(-np.clip(z, -500, 500)))

        def fit(self, X, y):
            n_samples, n_features = X.shape

            # 初始化参数
            self.weights = np.random.randn(n_features) * 0.01
            self.bias = 0

            # 梯度下降训练
            for _ in range(self.max_iter):
                # 前向传播
                z = X @ self.weights + self.bias
                predictions = self.sigmoid(z)

                # 计算梯度
                dw = (1/n_samples) * X.T @ (predictions - y) + self.regularization * self.weights
                db = (1/n_samples) * np.sum(predictions - y)

                # 更新参数
                self.weights -= self.learning_rate * dw
                self.bias -= self.learning_rate * db

        def predict(self, X):
            z = X @ self.weights + self.bias
            probabilities = self.sigmoid(z)
            return (probabilities > 0.5).astype(int)

    # 初始化交叉验证框架
    cv_framework = CrossValidationFramework()

    # 定义参数网格
    param_grid = {
        'learning_rate': [0.001, 0.01, 0.1],
        'max_iter': [50, 100, 200],
        'regularization': [0.0, 0.01, 0.1]
    }

    # 执行网格搜索
    grid_results = cv_framework.grid_search_cv(
        SimpleLogisticRegression, param_grid, X, y,
        cv_method='stratified', k=5, scoring='f1', random_state=42
    )

    print(f"\n网格搜索结果:")
    print(f"最佳参数组合: {grid_results['best_params']}")
    print(f"最佳F1分数: {grid_results['best_score']:.4f}")

    # 显示前5个最佳结果
    sorted_results = sorted(grid_results['all_results'],
                           key=lambda x: x['mean_score'], reverse=True)

    print(f"\n前5个最佳参数组合:")
    for i, result in enumerate(sorted_results[:5]):
        params = result['params']
        score = result['mean_score']
        std = result['std_score']
        print(f"  {i+1}. {params}")
        print(f"     F1分数: {score:.4f} (±{std:.4f})")

    # 场景3：回归模型评估
    print(f"\n场景3：回归模型评估")
    print("目标：评估回归模型的预测性能")

    # 生成回归数据
    np.random.seed(42)
    n_samples = 500
    X_reg = np.random.randn(n_samples, 3)
    true_weights = np.array([2.5, -1.8, 3.2])
    y_reg = X_reg @ true_weights + 0.5 * np.random.randn(n_samples)

    # 模拟回归预测（添加一些预测误差）
    y_pred_reg = X_reg @ true_weights + 0.8 * np.random.randn(n_samples)

    # 计算回归指标
    regression_results = evaluator.regression_metrics(y_reg, y_pred_reg)

    print(f"回归性能指标:")
    print(f"  平均绝对误差(MAE): {regression_results['mae']:.4f}")
    print(f"  均方误差(MSE): {regression_results['mse']:.4f}")
    print(f"  均方根误差(RMSE): {regression_results['rmse']:.4f}")
    print(f"  决定系数(R²): {regression_results['r2']:.4f}")
    print(f"  调整R²: {regression_results['adjusted_r2']:.4f}")
    print(f"  平均绝对百分比误差(MAPE): {regression_results['mape']:.2f}%")

    # 残差分析
    residuals = y_reg - y_pred_reg
    print(f"\n残差分析:")
    print(f"  残差均值: {np.mean(residuals):.6f}")
    print(f"  残差标准差: {np.std(residuals):.4f}")
    print(f"  残差范围: [{np.min(residuals):.4f}, {np.max(residuals):.4f}]")

model_evaluation_demo()

---

# 附录：参考资源与工具链

## A.1 权威学术资源

### A.1.1 经典教材与论文

**机器学习基础教材：**
- 《Pattern Recognition and Machine Learning》by Christopher Bishop
  - 链接：https://www.microsoft.com/en-us/research/people/cmbishop/prml-book/
  - 贝叶斯方法和概率图模型的权威教材

- 《The Elements of Statistical Learning》by Hastie, Tibshirani, and Friedman
  - 链接：https://web.stanford.edu/~hastie/ElemStatLearn/
  - 统计学习理论的经典教材，免费PDF下载

- 《Machine Learning: A Probabilistic Perspective》by Kevin Murphy
  - 链接：https://probml.github.io/pml-book/
  - 概率机器学习的全面教材

**深度学习资源：**
- 《Deep Learning》by Ian Goodfellow, Yoshua Bengio, and Aaron Courville
  - 链接：https://www.deeplearningbook.org/
  - 深度学习的权威教材，免费在线版本

- 《Neural Networks and Deep Learning》by Michael Nielsen
  - 链接：http://neuralnetworksanddeeplearning.com/
  - 深度学习入门的优秀教材

**重要论文集合：**
- Papers With Code：https://paperswithcode.com/
  - 最新AI论文与代码实现的集合
- arXiv.org：https://arxiv.org/list/cs.LG/recent
  - 机器学习最新研究论文预印本

### A.1.2 在线课程与教程

**顶级大学课程：**
- Stanford CS229 (Machine Learning)：http://cs229.stanford.edu/
- MIT 6.034 (Artificial Intelligence)：https://ocw.mit.edu/courses/6-034-artificial-intelligence-fall-2010/
- Berkeley CS188 (Introduction to AI)：https://inst.eecs.berkeley.edu/~cs188/
- CMU 10-701 (Machine Learning)：http://www.cs.cmu.edu/~tom/10701_sp11/

**实践导向课程：**
- Fast.ai：https://www.fast.ai/
  - 实用深度学习课程
- Coursera Machine Learning Course (Andrew Ng)：https://www.coursera.org/learn/machine-learning
- edX MIT Introduction to Machine Learning：https://www.edx.org/course/introduction-to-machine-learning

## A.2 开发工具与框架

### A.2.1 Python机器学习生态系统

**核心科学计算库：**
- NumPy：https://numpy.org/
  - 数值计算基础库
- SciPy：https://scipy.org/
  - 科学计算工具集
- Pandas：https://pandas.pydata.org/
  - 数据处理和分析
- Matplotlib：https://matplotlib.org/
  - 数据可视化
- Seaborn：https://seaborn.pydata.org/
  - 统计数据可视化

**机器学习框架：**
- Scikit-learn：https://scikit-learn.org/
  - 传统机器学习算法库
- XGBoost：https://xgboost.readthedocs.io/
  - 梯度提升框架
- LightGBM：https://lightgbm.readthedocs.io/
  - 微软的梯度提升框架

**深度学习框架：**
- TensorFlow：https://www.tensorflow.org/
  - Google的深度学习框架
- PyTorch：https://pytorch.org/
  - Facebook的深度学习框架
- Keras：https://keras.io/
  - 高级神经网络API

### A.2.2 专业工具与平台

**数据科学平台：**
- Jupyter Notebook：https://jupyter.org/
  - 交互式计算环境
- Google Colab：https://colab.research.google.com/
  - 免费GPU云端Jupyter环境
- Kaggle Kernels：https://www.kaggle.com/kernels
  - 数据科学竞赛平台

**MLOps工具：**
- MLflow：https://mlflow.org/
  - 机器学习生命周期管理
- Weights & Biases：https://wandb.ai/
  - 实验跟踪和可视化
- DVC：https://dvc.org/
  - 数据版本控制

## A.3 数据集资源

### A.3.1 经典数据集

**计算机视觉：**
- MNIST：http://yann.lecun.com/exdb/mnist/
  - 手写数字识别数据集
- CIFAR-10/100：https://www.cs.toronto.edu/~kriz/cifar.html
  - 小图像分类数据集
- ImageNet：https://www.image-net.org/
  - 大规模图像分类数据集

**自然语言处理：**
- IMDB Movie Reviews：https://ai.stanford.edu/~amaas/data/sentiment/
  - 电影评论情感分析
- Stanford Sentiment Treebank：https://nlp.stanford.edu/sentiment/
  - 细粒度情感分析
- Common Crawl：https://commoncrawl.org/
  - 大规模网页文本数据

**通用机器学习：**
- UCI Machine Learning Repository：https://archive.ics.uci.edu/ml/
  - 经典机器学习数据集集合
- Kaggle Datasets：https://www.kaggle.com/datasets
  - 各种领域的数据集

### A.3.2 数据获取工具

**API和爬虫工具：**
- Beautiful Soup：https://www.crummy.com/software/BeautifulSoup/
  - HTML/XML解析库
- Scrapy：https://scrapy.org/
  - 网页爬虫框架
- Requests：https://requests.readthedocs.io/
  - HTTP库

## A.4 社区与学习资源

### A.4.1 技术社区

**问答社区：**
- Stack Overflow：https://stackoverflow.com/questions/tagged/machine-learning
- Cross Validated：https://stats.stackexchange.com/
- Reddit r/MachineLearning：https://www.reddit.com/r/MachineLearning/

**专业博客：**
- Towards Data Science：https://towardsdatascience.com/
- Machine Learning Mastery：https://machinelearningmastery.com/
- Distill.pub：https://distill.pub/
  - 机器学习可视化解释

### A.4.2 会议与期刊

**顶级会议：**
- NeurIPS：https://neurips.cc/
- ICML：https://icml.cc/
- ICLR：https://iclr.cc/
- AAAI：https://www.aaai.org/

**重要期刊：**
- Journal of Machine Learning Research：https://jmlr.org/
- Machine Learning：https://link.springer.com/journal/10994
- IEEE Transactions on Pattern Analysis and Machine Intelligence

## A.5 实践项目建议

### A.5.1 初学者项目

1. **鸢尾花分类**：使用经典数据集学习分类算法
2. **房价预测**：回归问题的完整流程
3. **手写数字识别**：计算机视觉入门项目
4. **电影评论情感分析**：自然语言处理基础

### A.5.2 进阶项目

1. **推荐系统**：协同过滤和内容推荐
2. **时间序列预测**：股价或销量预测
3. **图像分类器**：使用CNN构建自定义分类器
4. **聊天机器人**：基于NLP的对话系统

### A.5.3 高级项目

1. **端到端机器学习系统**：包含数据管道、模型训练、部署
2. **多模态学习**：结合文本、图像、音频的模型
3. **强化学习游戏AI**：训练游戏智能体
4. **生成式AI应用**：文本生成、图像生成等

---

**结语**

这份《AI算法基础全面指南》涵盖了从基础数学理论到实际应用的完整知识体系。通过理论学习、代码实践和项目应用的结合，读者可以建立起扎实的AI/ML基础，并具备解决实际问题的能力。

记住，机器学习是一个快速发展的领域，持续学习和实践是保持竞争力的关键。建议读者：

1. **扎实基础**：深入理解数学原理和算法本质
2. **动手实践**：通过编程实现加深理解
3. **项目驱动**：通过实际项目积累经验
4. **持续学习**：关注最新研究和技术发展
5. **社区参与**：积极参与开源项目和技术讨论

愿这份指南能够成为你AI学习路上的可靠伙伴，助你在人工智能的广阔天地中探索前行！
